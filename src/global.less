@font-face {
  font-family: 'Poppins';
  src: url('./fonts/Poppins/Poppins-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/* 如果你有其他字重的 .ttf 文件，继续添加 */
@font-face {
  font-family: 'Poppins';
  src: url('./fonts/Poppins/Poppins-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

// 微软雅黑加粗
@font-face {
  font-family: 'MicrosoftYaHei';
  src: url('./fonts/MicrosoftYaHei/MSYHBD.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

// 微软雅黑常规
@font-face {
  font-family: 'MicrosoftYaHei Regular';
  src: url('./fonts/MicrosoftYaHei/MSYH.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

// 各行业
@font-face {
  font-family: 'YouSheHeiTi';
  src: url('./fonts/MicrosoftYaHei/HangYe.ttf') format('truetype');
}

::selection {
  background-color: #644cf6;
  /* 你想要的背景颜色 */
}

html,
body,
#root {
  width: 100%;
  height: 100%;
  font-size: 12px;
  //font-family: 'Poppins', sans-serif !important;
  // font-family: sans-serif, Microsoft Yahei;
  font-family: 'Poppins', sans-serif;
}

@select-height: 30px;

//统一表单输入框高度
.ant-input {
  height: @select-height;
  line-height: @select-height - 2px; // 使文字垂直居中
}

.codeContent {
  width: 95%;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #f1e8e2;
}

.am-picker-popup {
  border-radius: 10px !important;
}

.am-picker-popup-header-left {
  color: #666;
  font-size: 1.3rem;
}

.am-picker-popup-header-right {
  color: #7938eb;
  font-size: 1.3rem;
}

.am-picker-popup-header {
  box-shadow: 0px 1px 4px 0px #000;
  background-image: none !important;

  &:after {
    display: none !important;
  }
}

.am-picker-col-indicator {
  z-index: 1 !important;
  background-color: #f1eef7 !important;

  &:after {
    display: none !important;
  }

  &:before {
    display: none !important;
  }
}

.am-picker-col-item-selected {
  font-size: 16px !important;
}

.TermsServiceModal {
  width: 60% !important;
  margin-top: -60px;
  border-radius: 6px;

  .ant-modal-body {
    height: 630px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    overflow: hidden;
    overflow-y: scroll;
    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    font-family: 'Poppins', sans-serif !important;
  }
}

//.selectInput.ant-select-dropdown-hidden{
//  display: block;
//}
//.selectInput.ant-select-dropdown {
//  background: #000d17;
//  border-radius: 6px;
//}
//.selectInput {
//  .ant-select-item {
//    color: rgba(255, 255, 255, 0.65);
//  }
//  .ant-select-item-option-selected:not(.ant-select-item-option-disabled),
//  .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
//    background: #1677ff;
//    //border-radius: 6px;
//  }
//  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
//    color: #fff;
//  }
//}

.selectChannel.ant-select-dropdown {
  font-size: 12px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
}

.selectChannel {
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    font-weight: 400;
  }
}

.ant-input:placeholder-shown {
  font-weight: 400;
  color: rgba(0, 0, 0, 0.25);
  font-family: 'Poppins', sans-serif;
}

.settingDownMenu.ant-dropdown {
  top: 60px !important;
  height: 136px;
  width: 180px !important;

  .AwsAccountIcon {
    width: 16px;
    height: 16px;
    background-image: url('assets/AwsAccountIcon.png');
    background-size: 100%;
    float: left;
    margin-top: 3px;
    margin-right: 5px;
  }

  .ChannelAllocationIcon {
    width: 16px;
    height: 16px;
    background-image: url('assets/ChannelAllocationIcon.png');
    background-size: 100%;
    float: left;
    margin-top: 3px;
    margin-right: 5px;
  }

  .ConfigureCustomerExtensionIcon {
    width: 16px;
    height: 16px;
    background-image: url('assets/ConfigureCustomerExtensionIcon.png');
    background-size: 100%;
    float: left;
    margin-top: 3px;
    margin-right: 5px;
  }

  .WorkOrderExtensionIcon {
    width: 16px;
    height: 16px;
    background-image: url('assets/WorkOrderExtensionIcon.png');
    background-size: 100%;
    float: left;
    margin-top: 3px;
    margin-right: 5px;
  }

  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    border-bottom: 1px solid #f3f7fe;
  }

  .ant-dropdown-menu-item:last-child,
  .ant-dropdown-menu-submenu-title:last-child {
    border-bottom: none;
  }

  .ant-dropdown-menu-item-active {
    background: #8501bb;
  }

  .ant-dropdown-menu-item-active .AwsAccountIcon {
    background-image: url('assets/AwsAccountIconActive.png');
    background-size: 100%;
  }

  .ant-dropdown-menu-item-active .ChannelAllocationIcon {
    background-image: url('assets/ChannelAllocationIconActive.png');
    background-size: 100%;
  }

  .ant-dropdown-menu-item-active .ConfigureCustomerExtensionIcon {
    background-image: url('assets/ConfigureCustomerExtensionIconActive.png');
    background-size: 100%;
  }

  .ant-dropdown-menu-item-active .WorkOrderExtensionIcon {
    background-image: url('assets/WorkOrderExtensionIconActive.png');
    background-size: 100%;
  }

  .ant-dropdown-menu-item-active .ant-dropdown-menu-submenu-title,
  .ant-dropdown-menu-title-content > a {
    color: #666;
    font-size: 12px;
  }

  .ant-dropdown-menu-item-active .ant-dropdown-menu-submenu-title,
  .ant-dropdown-menu-title-content > a:hover {
    color: #fff;
  }

  .ant-dropdown-menu-title-content {
    text-align: left;
  }
}

.setMenuDownMenu.ant-dropdown {
  top: 50px !important;

  .ant-dropdown-menu {
    padding: 0;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 4px;
    background-color: transparent !important;
  }

  .ant-dropdown-menu-item {
    transition: none !important;
  }

  .ant-dropdown-menu-item:nth-of-type(1) {
    border-radius: 4px 4px 0 0;
  }

  .ant-dropdown-menu-item:nth-of-type(4) {
    border-radius: 0 0 4px 4px;
  }

  .ant-dropdown-menu-item:hover {
    color: #fff;
    background: rgba(173, 48, 229, 0.8);
    transition: none !important;
  }
}

.connectDownMenu.ant-dropdown {
  top: 60px !important;
  height: 136px;
  width: 150px !important;
  min-width: 150px !important;
  left: 74% !important;

  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    border-bottom: 1px solid #f3f7fe;
  }

  .ant-dropdown-menu-item:last-child,
  .ant-dropdown-menu-submenu-title:last-child {
    border-bottom: none;
  }

  .ant-dropdown-menu-item-active {
    background: #8501bb;
  }

  .ant-dropdown-menu-item-active .ant-dropdown-menu-submenu-title,
  .ant-dropdown-menu-title-content > a {
    color: #666;
    font-size: 12px;
  }

  .ant-dropdown-menu-item-active .ant-dropdown-menu-submenu-title,
  .ant-dropdown-menu-title-content > a:hover {
    color: #fff;
  }

  .ant-dropdown-menu-title-content {
    text-align: center;
  }
}

.connectDownPersonal.ant-dropdown {
  top: 60px !important;
  height: 136px;
  width: 150px !important;
  min-width: 150px !important;
  left: 86% !important;

  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    border-bottom: 1px solid #f3f7fe;
  }

  .ant-dropdown-menu-item:last-child,
  .ant-dropdown-menu-submenu-title:last-child {
    border-bottom: none;
  }

  .ant-dropdown-menu-item-active {
    background: #8501bb;
  }

  .ant-dropdown-menu-item-active .ant-dropdown-menu-submenu-title,
  .ant-dropdown-menu-title-content > a {
    color: #666;
    font-size: 12px;
  }

  .ant-dropdown-menu-item-active .ant-dropdown-menu-submenu-title,
  .ant-dropdown-menu-title-content > a:hover {
    color: #fff;
  }

  .ant-dropdown-menu-title-content {
    text-align: center;
  }
}

.regularContentPopover {
  .ant-popover-inner-content {
    width: 352px;
    height: 194px;
    background: #ffffff;
    box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
      0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
    border-radius: 8px;
  }

  .regularTitle {
    font-size: 12px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.88);
    height: 40px;
    line-height: 40px;

    .anticon {
      float: right;
      margin-right: 10px;
      margin-top: 13px;
    }
  }

  .ant-input {
    height: 30px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .result {
    height: 32px;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 12px;
    margin-left: 10px;
  }

  .ant-btn-primary {
    font-size: 12px;
    float: right;
  }
}

.addAdminModal {
  width: 55% !important;
  margin-left: 28% !important;
  margin-top: 5%;

  .ant-input {
    border-radius: 6px;
    font-size: 12px;
  }

  .ant-btn {
    margin-right: 20px;
  }

  .ant-form-item-explain-error {
    font-size: 12px;
  }

  .ant-select {
    font-size: 12px;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
  }
}

.addDeptModal {
  .ant-input {
    border-radius: 6px;
    font-size: 12px;
  }

  .ant-select {
    font-size: 12px;
  }

  .ant-form-item-explain-error {
    font-size: 12px;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
  }
}

.remarksModal {
  width: 40% !important;
  margin-left: 35% !important;
  margin-top: 5%;

  .ant-modal-body {
    height: 210px;

    label {
      float: left;
      font-size: 12px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }

    .ant-input-textarea-show-count > .ant-input {
      float: left;
      width: 90%;
      border-radius: 6px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      box-shadow: none;
    }
  }

  .ant-modal-footer {
    text-align: center;
    padding: 0 0 24px 0;
    border-top: none;
  }
}

.settingPopover {
  .ant-popover-arrow {
    display: none;
  }

  .ant-popover-inner {
    background: #ffffff;
    box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
      0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
    border-radius: 6px;
  }

  .settingPopoverContent {
    width: 180px;
    height: 252px;

    .topContent {
      height: 180px;
      overflow: hidden;
      overflow-y: scroll;
      scrollbar-width: 0px;
      -ms-overflow-style: none;

      .topLeftContent {
        height: 100%;
        float: left;
      }

      .ant-checkbox-wrapper {
        width: 100%;
        height: 30px;
        line-height: 24px;
      }
    }

    .topContent::-webkit-scrollbar {
      display: none;
    }

    .footerBtn {
      width: 100%;
      text-align: right;
      margin-top: 30px;

      .cancelBtn {
        margin-right: 8px;
      }
    }
  }
}

// 单独设置翻译窗口位置
.settingTranslatePopover {
  .ant-popover-inner {
    // box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15) !important;
    fill: rgba(255, 255, 255, 0.8) !important;
    // stroke-width: 1px !important;
    // // stroke: #FFF;
    filter: drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.1)) !important;
    // backdrop-filter: blur(5px) !important;
    // background: transparent !important;
    // color: #fff !important;
    // background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
    background: transparent !important;
    background-color: rgba(255, 255, 255, 0.7) !important;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(75px) !important;
    border: 1px solid #fff;
  }

  .ant-popover-arrow {
    display: none;
  }

  .ant-popover-inner-content {
    padding: 10px;
    // box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    // border:1px solid #fff;
    // fill: rgba(255, 255, 255, 0.80);
    // stroke-width: 1px;
    // // stroke: #FFF;
    // filter: drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.1));
    // backdrop-filter: blur(5px);
  }
}

.batchImportModalEn {
  width: 530px !important;
  height: 424px !important;

  .ant-modal-title {
    font-weight: 400;
    color: #333333;
  }

  .ant-modal-header {
    border-bottom: none;
  }

  .ant-modal-body {
    height: 256px;
  }

  .downloadContent,
  .importContent {
    width: 85%;
    height: 150px;
    float: left;
    margin-left: 10%;

    p {
      color: #666;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }

    img {
      width: 64px;
    }

    .uploadDiv {
      width: 100%;
      border-radius: 4px;
      border: 1px dashed var(--neutral-color-border-base, #dcdfe6);
      background: var(--function-component, #fff);
      height: 130px;
      padding: 10px 25px;
      margin-left: 8%;
      text-align: center;

      span {
        //color: #3463fc;
        //font-size: 14px;
      }
    }
  }

  .detailDownload {
    width: 100%;
    height: 40px;
    margin-top: 15px;

    img {
      width: 16px;
      float: left;
      margin-left: 0%;
      margin-right: 3px;
      margin-top: 1px;
    }

    span {
      color: #666;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      float: left;
    }

    a {
      font-size: 12px;
      margin-left: 10px;
      float: left;
    }
  }

  .downloadBtn {
    border-radius: 4px;
    border: 1px solid #3463fc;
    color: #3463fc;
    font-size: 14px;
    float: left;
    margin-left: 8%;
    margin-top: 8px;
  }

  .ant-upload-list-item-name {
    color: #666;
    font-size: 12px;
  }

  .ant-upload-list-item-card-actions-btn.ant-btn-sm.ant-btn:hover,
  .ant-upload-list-item-card-actions-btn.ant-btn-sm.ant-btn {
    border: none;
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    padding: 0px 0px 24px 0px;
  }
}

.batchImportModal {
  width: 360px !important;
  height: 424px !important;

  .ant-modal-title {
    font-weight: 400;
    color: #333333;
  }

  .ant-modal-header {
    border-bottom: none;
  }

  .ant-modal-body {
    height: 256px;
  }

  .downloadContent,
  .importContent {
    width: 85%;
    height: 150px;
    float: left;
    margin-left: 10%;

    p {
      color: #666;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }

    img {
      width: 64px;
    }

    .uploadDiv {
      width: 100%;
      border-radius: 4px;
      border: 1px dashed var(--neutral-color-border-base, #dcdfe6);
      background: var(--function-component, #fff);
      height: 130px;
      padding: 10px 25px;
      margin-left: 8%;
      text-align: center;

      span {
        color: #3463fc;
        font-size: 14px;
      }
    }
  }

  .detailDownload {
    width: 100%;
    height: 40px;
    margin-top: 15px;

    img {
      width: 16px;
      float: left;
      margin-left: 0%;
      margin-right: 3px;
      margin-top: 1px;
    }

    span {
      color: #666;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      float: left;
    }

    a {
      font-size: 12px;
      margin-left: 10px;
      float: left;
    }
  }

  .downloadBtn {
    border-radius: 4px;
    border: 1px solid #3463fc;
    color: #3463fc;
    font-size: 14px;
    float: left;
    margin-left: 8%;
    margin-top: 8px;
  }

  .ant-upload-list-item-name {
    color: #666;
    font-size: 12px;
  }

  .ant-upload-list-item-card-actions-btn.ant-btn-sm.ant-btn:hover,
  .ant-upload-list-item-card-actions-btn.ant-btn-sm.ant-btn {
    border: none;
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    padding: 0px 0px 24px 0px;
  }
}

.changeGroupingModal {
  height: 200px !important;

  label {
    font-size: 12px;
  }

  .changeGroupingFooter {
    width: 100%;
    margin-top: 35px;
    text-align: center;
  }

  .ant-select-multiple .ant-select-selection-placeholder {
    font-size: 12px;
  }

  .ant-select-item {
    font-size: 12px;
  }
}

.ant-btn.ant-btn-primary {
  background-color: #3463fc;
  border: 1px solid #3463fc;
  color: #fff;
}

.ant-btn-background-ghost.ant-btn-primary {
  color: #1890ff;
  border-color: #1890ff;
  text-shadow: none;
}

.ant-btn.ant-btn-background-ghost,
.ant-btn.ant-btn-background-ghost:hover,
.ant-btn.ant-btn-background-ghost:active,
.ant-btn.ant-btn-background-ghost:focus {
  background: transparent;
}

.ant-btn.ant-btn-primary:hover {
  background-color: #3463fc;
  border: 1px solid #3463fc;
  color: #fff;
}

.ant-btn {
  border-radius: 4px;
  border: 1px solid #3463fc;
  color: #3463fc;
}

.ant-btn:hover {
  border-radius: 4px;
  border: 1px solid #3463fc;
  color: #3463fc;
}

.ant-btn:hover,
.ant-btn:focus {
  border-radius: 4px;
  border: 1px solid #3463fc;
  color: #3463fc;
}

.ant-btn.ant-btn-primary:hover,
.ant-btn.ant-btn-primary:focus {
  color: #fff;
}

.ant-table {
  color: #333;
  font-size: 12px;
}

.ant-table-thead > tr > th {
  background: #f3f7fe;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  height: 40px;
  padding: 9px 14px;
}

.ant-table-tbody > tr > td {
  padding: 9px 14px;
  height: 40px;
}

//td.ant-table-column-sort{
//  background-color: #E6E6E6;
//}
.ant-pagination-item {
  border-radius: 3px;
  background: rgba(52, 99, 252, 0.1);
  border: none;
}

.ant-pagination-item a {
  color: #3463fc;
  margin-right: 0px !important;
}

.ant-pagination-item-active {
  background: #3463fc;
}

.ant-pagination-item.ant-pagination-item-active a {
  color: #fff;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  background: rgba(52, 99, 252, 0.1);
  border: none;
}

.ant-table-wrapper .ant-pagination-prev .anticon,
.ant-table-wrapper .ant-pagination-next .anticon {
  color: #3463fc;
}

a {
  color: #3463fc;
}

.ant-breadcrumb a,
.ant-breadcrumb a:hover {
  color: #fff !important;
  text-decoration: none !important;
}

.ant-modal-header {
  border-bottom: none;
  padding: 16px 24px 10px 24px;
}

.ant-modal-header .ant-modal-title {
  font-size: 18px !important;
  color: #333 !important;
  font-weight: 400 !important;
}

.ant-radio-wrapper {
  font-size: 12px;
}

.ant-form-item-label > label {
  font-size: 12px;
}

.ant-tooltip {
  font-size: 12px;
}

//工单中心样式

//催单行背景颜色设置
.ant-table table tr:has(td.reminderStatus) {
  background-color: rgba(52, 99, 252, 0.15) !important;
}

.ant-table table tr:has(td.reminderStatus) .ant-table-cell-fix-left,
.ant-table table tr:has(td.reminderStatus) .ant-table-cell-fix-right {
  background-color: #e1e9fd !important;
}

.selectFilterContent {
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background: #f5f7ff;
    font-weight: 400;
  }

  .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
    background: #f5f7ff;
  }

  .ant-select-item {
    font-size: 12px;
  }

  .ant-select-item:hover {
    background: #f5f7ff;
  }

  img {
    float: left;
    margin-right: 5px;
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }
}

//更多字段气泡卡片
.moreFieldsContentPopover {
  .ant-popover-inner-content {
    width: 230px;
    height: 250px;
    padding: 0px;

    .moreFieldSearch {
      padding: 0px 12px;
      height: 44px;
      fill: #fff;
      box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.05);

      .ant-input-group-wrapper {
        margin-top: 6px;

        .ant-input-group {
          height: 32px;
          border-radius: 4.125px;
          border: 0.688px solid #e6e6e6;
          background: #fff;

          .ant-btn {
            border: none;
          }

          .ant-input {
            border: none;
            padding: 6px 11px;
            box-shadow: none;
            font-size: 12px;
            border-radius: 4.125px;
          }
        }
      }
    }

    .moreFieldList {
      height: 206px;
      overflow: hidden;
      overflow-y: scroll;
      /* 隐藏滚动条 */
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;

      /* IE 10+ */
      .ant-checkbox-group-item,
      .ant-checkbox-wrapper {
        width: 100%;
        height: 36px;
      }

      .ant-checkbox-group-item:hover,
      .ant-checkbox-wrapper:hover {
        background-color: rgba(52, 99, 252, 0.05);
      }

      .ant-checkbox-wrapper + .ant-checkbox-wrapper {
        margin-left: 0px;
      }

      .ant-checkbox + span {
        font-size: 12px;
        line-height: 36px;
      }

      .ant-checkbox {
        font-size: 12px;
        top: 4px;
        margin-left: 12px;
      }

      .ant-checkbox-checked::after:hover {
        border-color: #3463fc;
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #3463fc;
        border-color: #3463fc;
      }
    }

    .moreFieldList::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }

    .moreFieldBtn {
      height: 44px;
      fill: #fff;
      text-align: center;
      box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.05);

      .ant-btn {
        margin-top: 6px;
        margin-right: 12px;
        font-size: 12px;
      }
    }
  }
}

//修改工单抽屉
.editorWorkOrderDrawer {
  .ant-drawer-header {
    border-bottom: none;
    color: #333;
    font-size: 20px;
    padding: 20px 20px 0px 20px;
    font-weight: 400;
  }

  .ant-drawer-body {
    padding: 20px;
  }

  .ant-form label {
    font-size: 12px !important;
  }

  .editorWorkOrderContent {
    width: 100%;

    .titleItem {
      width: 100%;
      height: 30px;
      color: #333;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
    }

    .ant-picker-input > input {
      font-size: 12px;
    }

    .ant-form-item-label > label {
      font-size: 12px !important;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      float: left;
      line-height: 32px;
      //justify-content: end;
      margin-right: 10px;
      width: 80px;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: inherit;
    }

    .ant-input {
      border-radius: 6px;
      //border: 1px solid #e6e6e6;
      font-size: 12px;
      box-shadow: none;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 6px;
      //border: 1px solid #e6e6e6;
      font-size: 12px;
      box-shadow: none;
    }

    .ant-form-item-control-input-content:last-child {
      //text-align: center;
      .ant-btn {
        font-size: 12px;
        margin-right: 16px;
      }
    }
  }
}

//回复工单
.replayWorkOrderDrawer {
  height: 89%;
  top: 8.3%;
  border-radius: 4px;
  z-index: 10000;

  .ant-drawer-header {
    border-bottom: none;
    color: #333;
    font-size: 20px;
    padding: 20px 20px 0px 20px;
    font-weight: 400;
  }

  .ant-drawer-content {
    border-radius: 4px;
  }

  .ant-drawer-body {
    padding: 20px;
  }

  .ant-drawer-content-wrapper {
    width: 630px !important;
  }

  .ant-btn {
    font-size: 12px;
    margin-right: 16px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-upload-list-item-card-actions-btn.ant-btn-sm {
    border: none;
  }
}

.replayChatWorkOrderDrawer {
  .ant-drawer-header {
    border-bottom: none;
    color: #333;
    font-size: 20px;
    padding: 20px 20px 0px 20px;
    font-weight: 400;
  }

  .ant-drawer-body {
    padding: 20px;
  }

  .ant-drawer-content-wrapper {
    width: 460px !important;
    //transform: none !important;
  }

  .ant-btn {
    font-size: 12px;
    margin-right: 16px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    justify-content: end;
    margin-right: 10px;
    min-width: 80px;
    text-align: right;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-upload-list-item-card-actions-btn.ant-btn-sm {
    border: none;
  }
}

//智能AI
.IntelligentAIDrawer {
  .ant-drawer-header {
    border-bottom: none;
    color: #333;
    font-size: 20px;
    padding: 20px 20px 0px 20px;
    font-weight: 400;
  }

  .ant-drawer-body {
    padding: 10px 20px 20px 20px;
  }

  .ant-drawer-content-wrapper {
    width: 30% !important;
  }

  .ant-tabs-tab {
    padding: 5px 10px;
    color: #999;
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #3463fc;
    font-weight: 400;
  }

  .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar,
  .ant-tabs-bottom > .ant-tabs-nav .ant-tabs-ink-bar,
  .ant-tabs-top > div > .ant-tabs-nav .ant-tabs-ink-bar,
  .ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-ink-bar {
    height: 1px;
  }

  .ant-tabs-ink-bar {
    background-color: #3463fc;
  }

  .generatedContent {
    height: 177px;
    border-radius: 6px;
    border: 1px dashed #e6e6e6;
    background: #f9f9f9;
    padding: 10px 12px;
    text-align: center;

    img {
      margin-top: 10px;
    }

    p {
      margin-bottom: 0px;
      margin-top: 5px;
      color: #999;
      font-size: 12px;
    }
  }

  .titleText {
    color: #333;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 3px;

    img {
      width: 16px;
      height: 16px;
      float: right;
      margin-right: 5px;
      cursor: pointer;
    }

    i {
      color: #666;
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
    }
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    width: 100%;
    height: 32px;
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    background: #fff;
  }

  .ant-select {
    width: 100%;
    margin-bottom: 16px;
  }

  textarea.ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: none;
  }

  .imageSizeInput {
    width: 100%;
    margin-bottom: 12px;
    height: 35px;

    .imageSizeInputItem {
      width: 50%;
      float: left;

      label {
        float: left;
        line-height: 32px;
      }

      .ant-input {
        width: 60%;
        float: left;
        border-radius: 6px;
        //border: 1px solid #e6e6e6;
        background: #fff;
        height: 32px;
        box-shadow: none;
        font-size: 12px;
      }
    }
  }

  .ant-input:placeholder-shown {
    color: #999;
    font-size: 12px;
  }

  .translateContent {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    margin-bottom: 20px;

    textarea.ant-input {
      border: none;
      margin-bottom: 0px;
    }

    .translateFooter {
      width: 100%;
      height: 20px;
      border-top: 1px solid #e6e6e6;
      background: rgba(52, 99, 252, 0.05);

      .leftBtn {
        width: 50%;
        float: left;
        text-align: center;
        text-align: center;
        color: #3463fc;
        font-size: 10px;
        cursor: pointer;
        border-right: 1px solid #e6e6e6;
      }

      .rightBtn {
        width: 50%;
        float: left;
        text-align: center;
        color: #3463fc;
        font-size: 10px;
        cursor: pointer;
      }
    }
  }
}

//添加关联工单
.AddAssociatedWorkOrder {
  top: 18%;
  width: 50% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-body {
    padding: 12px 20px 20px 20px;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    width: 60%;
    margin-bottom: 12px;
    box-shadow: none;
    font-size: 12px;
  }

  .channelName {
    img {
      width: 12px;
      height: 12px;
      float: left;
      margin-top: 3px;
      margin-right: 5px;
    }
  }

  .state1 {
    color: #13c825;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #13c825;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .state2 {
    color: #f22417;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #f22417;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .state3 {
    color: #3463fc;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #3463fc;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .state4 {
    color: #333333;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #333333;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .state5 {
    color: #fcb830;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #fcb830;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .footerBtn {
    width: 100%;
    text-align: center;

    .ant-btn {
      margin-right: 12px;
    }
  }
}

//工单升级
.WorkOrderUpgrade {
  top: 18%;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-body {
    padding: 10px 40px 0px 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: #333;
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-form label {
    font-size: 12px;
    font-weight: 400;
    color: #333;
  }

  .ant-radio-checked .ant-radio-inner {
    background-color: rgba(52, 99, 252, 1);
  }

  .ant-radio-checked .ant-radio-inner {
    border-color: rgba(52, 99, 252, 1);
  }

  .ant-radio-inner::after {
    width: 14px;
    height: 14px;
    margin-top: -7px;
    margin-left: -7px;
    background-color: #fff;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }
}

.WorkOrderWhatsApp {
  top: 15%;
  max-width: min-content !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-body {
    padding: 10px 30px;
  }

  .ant-card {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);

    .ant-card-cover {
      padding: 5px;
    }

    .ant-card-cover img {
      border-radius: 8px;
    }

    .ant-card-body {
      padding: 0;
    }
  }

  .whatsAppMessageItemBody {
    font-size: 12px;
    padding: 10px 10px 0 10px;
    white-space: pre-line;

    span {
      color: #999;
      font-size: 12px;
    }

    p {
      margin-bottom: 10px;
    }
  }

  .whatsAppMessageItemBodyBtn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 40px;
    font-size: 12px;
    border-top: 1px solid #e6e6e6;
    color: #3463fc;
    gap: 5px;
  }

  .ant-input {
    border-radius: 6px;
    font-size: 12px;
  }

  .ant-input:focus,
  .ant-input-focused {
    box-shadow: none;
  }
}

.WorkOrderUpgrade2 {
  top: 18%;
  width: 570px !important;

  .whatsAppMessageItem {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    background: #fff;
    margin-top: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    font-size: 12px;
  }

  .whatsAppMessageItemHover {
    border: 1px solid #3463fc !important;
    background: linear-gradient(
        0deg,
        rgba(52, 99, 252, 0.05) 0%,
        rgba(52, 99, 252, 0.05) 100%
      ),
      #fff !important;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-body {
    padding: 10px 40px 20px 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: #333;
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-input-affix-wrapper {
    border-radius: 6px;
    padding-top: 0 !important;

    .ant-input-prefix {
      padding-top: 4px;
    }
  }

  .ant-input-affix-wrapper:focus,
  .ant-input-affix-wrapper-focused {
    box-shadow: none;
  }

  .ant-select-selector {
    box-shadow: none !important;
  }

  .ant-select {
    width: 100%;
    border-radius: 6px;
  }

  .ant-form label {
    font-size: 12px;
    font-weight: 400;
    color: #333;
  }

  .ant-radio-checked .ant-radio-inner {
    background-color: rgba(52, 99, 252, 1);
  }

  .ant-radio-checked .ant-radio-inner {
    border-color: rgba(52, 99, 252, 1);
  }

  .ant-radio-inner::after {
    width: 14px;
    height: 14px;
    margin-top: -7px;
    margin-left: -7px;
    background-color: #fff;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;

    .onLineCircle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13.5px;
      float: left;
      background-color: #00b900;
    }

    .offLineCircle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13.5px;
      float: left;
      background-color: #ee3b31;
    }
  }
}

.WorkOrderUpgrade3 {
  top: 25%;
  width: 570px !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .ant-modal-header {
    background: none;
    padding: 20px 24px 10px 30px !important;
  }

  .ant-btn {
    margin-left: 20px;
    margin-top: 10px;
  }

  .ant-modal-body {
    padding: 10px 40px 20px 5px !important;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: #333;
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-form label {
    font-size: 12px;
    font-weight: 400;
    color: #333;
  }

  .ant-radio-checked .ant-radio-inner {
    background-color: rgba(52, 99, 252, 1);
  }

  .ant-radio-checked .ant-radio-inner {
    border-color: rgba(52, 99, 252, 1);
  }

  .ant-radio-inner::after {
    width: 14px;
    height: 14px;
    margin-top: -7px;
    margin-left: -7px;
    background-color: #fff;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;

    .onLineCircle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13.5px;
      float: left;
      background-color: #00b900;
    }

    .offLineCircle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13.5px;
      float: left;
      background-color: #ee3b31;
    }
  }
}

.WorkOrderUpgrade4 {
  top: 25%;
  width: 400px !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .ant-modal-header {
    background: none;
    padding: 20px 15px 10px 15px !important;
  }

  .ant-btn {
    margin-left: 20px;
    margin-top: 10px;
  }

  .ant-modal-body {
    padding: 10px 20px 20px 20px !important;
  }
}

.onLineCircle {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  margin-right: 5px;
  margin-top: 8.5px;
  float: left;
  background-color: #00b900;
}

.offLineCircle {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  margin-right: 5px;
  margin-top: 8.5px;
  float: left;
  background-color: #ee3b31;
}

.disableLineCircle {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  margin-right: 5px;
  margin-top: 8.5px;
  float: left;
  background-color: #999999;
}

.ant-select-dropdown-hidden {
  //display: block !important;
}

//转派/指派工单、催办工单、备注、终止工单
.ReminderWorkOrder,
.workOrderRemarks,
.TerminateWorkOrder {
  top: 20%;

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-body {
    height: 290px;
    padding: 10px 30px 0px 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    //min-width: 80px;
    //text-align: right;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;

    .onLineCircle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13.5px;
      float: left;
      background-color: #00b900;
    }

    .offLineCircle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13.5px;
      float: left;
      background-color: #ee3b31;
    }
  }
}

.AssignWorkOrder {
  top: 20%;

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-body {
    padding: 10px 30px 0px 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    //min-width: 80px;
    //text-align: right;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;

    .onLineCircle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13.5px;
      float: left;
      background-color: #00b900;
    }

    .offLineCircle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13.5px;
      float: left;
      background-color: #ee3b31;
    }
  }
}

//添加关联工单
.AddAssociatedWorkOrder1 {
  position: relative;
  left: 20%;
  top: 18%;
  width: 45% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-body {
    padding: 12px 20px 20px 20px;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    width: 60%;
    margin-bottom: 12px;
    box-shadow: none;
    font-size: 12px;
  }

  .channelName {
    img {
      width: 12px;
      height: 12px;
      float: left;
      margin-top: 3px;
      margin-right: 5px;
    }
  }

  .state1 {
    color: #13c825;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #13c825;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .state2 {
    color: #f22417;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #f22417;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .state3 {
    color: #3463fc;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #3463fc;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .state4 {
    color: #333333;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #333333;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .state5 {
    color: #fcb830;

    .circle {
      width: 6px;
      height: 6px;
      border-radius: 90px;
      background-color: #fcb830;
      float: left;
      margin-right: 5px;
      margin-top: 6px;
    }
  }

  .footerBtn {
    width: 100%;
    text-align: center;

    .ant-btn {
      margin-right: 12px;
    }
  }
}

//工单升级
.WorkOrderUpgrade1 {
  position: relative;
  left: 20%;
  top: 18%;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-body {
    padding: 10px 40px 0px 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }
}

//转派/指派工单、催办工单、备注、终止工单
.AssignWorkOrder1,
.ReminderWorkOrder1,
.workOrderRemarks1,
.TerminateWorkOrder1 {
  position: relative;
  left: 18%;
  top: 20%;

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-body {
    height: 290px;
    padding: 10px 30px 0px 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    //min-width: 80px;
    //text-align: right;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }
}

.TerminateWorkOrder2 {
  top: 20%;

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-body {
    height: 200px;
    padding: 10px 30px 0px 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    //min-width: 80px;
    //text-align: right;
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }
}

.wh60 {
  .ant-form-item-label > label {
    width: 60px !important;
  }
}

//AI编译器气泡卡
.aiEditorPopover {
  width: 330px;
  height: 125px;
  color: #fff;
  border-radius: 4px;
  background: #3463fc;
  padding: 20px;
  position: absolute;
  z-index: 2;
  left: 25%;
  top: 20%;

  p {
    margin-bottom: 20px;
  }

  .ant-checkbox-wrapper {
    color: #fff;
    margin-top: 5px;
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #fff;
    border: none;
  }

  .ant-checkbox-inner::after {
    border: 1px solid #3463fc;
    border-top: 0;
    border-left: 0;
  }

  .ant-checkbox-checked::after {
    border: none;
  }

  .closeAi {
    margin-right: 12px;
    border-radius: 4px;
    border: 1px solid #fff;
    background-color: #3463fc;
    font-size: 12px;
    color: #fff;
    float: right;
  }

  .giveTry {
    margin-right: 0px;
    font-size: 12px;
    float: right;
    border: 1px solid #fff;
  }

  .ant-popover-inner-content {
    min-height: 100px;
  }
}

.aiEditorReplayPopover {
  width: 330px;
  height: 115px;
  color: #fff;
  border-radius: 4px;
  background: #3463fc;
  padding: 20px;
  position: absolute;
  z-index: 2;
  left: 29%;
  top: 40%;

  p {
    margin-bottom: 20px;
  }

  .ant-checkbox-wrapper {
    color: #fff;
    margin-top: 5px;
    font-size: 12px;
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #fff;
    border: none;
  }

  .ant-checkbox-inner::after {
    border: 1px solid #3463fc;
    border-top: 0;
    border-left: 0;
  }

  .ant-checkbox-checked::after {
    border: none;
  }

  .closeAi {
    margin-right: 12px;
    border-radius: 4px;
    border: 1px solid #fff;
    background-color: #3463fc;
    font-size: 12px;
    color: #fff;
    float: right;
  }

  .giveTry {
    margin-right: 0px;
    font-size: 12px;
    float: right;
    border: 1px solid #fff;
  }

  .ant-popover-inner-content {
    min-height: 100px;
  }
}

.statusChangePopover {
  .ant-popover-inner-content {
    padding: 0px;
  }

  .operationMenuItem {
    width: 160px;
    height: 30px;
    background-color: #fff;
    font-size: 12px;
    cursor: pointer;
    line-height: 30px;
    padding-left: 20px;
  }

  .operationMenuItem:hover {
    background-color: #f5f7ff;
  }
}

.emailTipsPopover {
  .ant-popover-inner-content {
    padding: 11px;
    background-color: #3463fc;
    float: left;
  }

  .ant-popover-arrow-content {
    --antd-arrow-background-color: #3463fc;
  }

  .operationMenuItem {
    width: 160px;
    height: 30px;
    background-color: #3463fc;
    font-size: 12px;
    cursor: pointer;
    line-height: 30px;
    padding-left: 20px;
  }

  p {
    color: #fff;
    font-size: 12px;
  }

  .ant-btn {
    font-size: 12px;
    float: right;
  }
}

.AddFilterModal {
  .ant-btn {
    margin-right: 12px;
    font-size: 12px;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(20px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-body {
    padding: 10px 30px 0px 20px;
    height: 150px;
    background: #fff;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    justify-content: end;
    margin-right: 10px;
    min-width: 80px;
    text-align: right;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }
}

.AddFilterModalBatch {
  .ant-btn {
    margin-right: 12px;
    font-size: 12px;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(20px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-body {
    padding: 10px 30px 0px 20px;
    height: 300px;
    background: #fff;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    justify-content: end;
    margin-right: 10px;
    min-width: 80px;
    text-align: right;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #f5f7ff;
  font-weight: 400;
}

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background: #f5f7ff;
}

.ant-select-item {
  font-size: 12px;
}

.ant-select-item:hover {
  background: #f5f7ff;
}

//宣传首页
.langDownPersonal.ant-dropdown {
  width: 110px;
  top: 45px !important;

  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    text-align: center;
    color: #333;
    font-size: 12px;
  }

  .ant-dropdown-menu-item-active {
    background-color: #3463fc;
    color: #fff;
  }

  .ant-dropdown-menu-item-selected,
  .ant-dropdown-menu-submenu-title-selected {
    background-color: #3463fc;
    color: #fff;
  }

  .ant-dropdown-menu {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(7px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.15);
    border-radius: 6px;
  }
}

//.ant-dropdown-hidden, .ant-dropdown-menu-hidden, .ant-dropdown-menu-submenu-hidden{
//  display: block;
//}

//智能总结弹窗
.intelligentSummaryModal {
  position: relative;
  left: 20%;

  .ant-modal-body {
    padding-top: 0px;
  }

  .ant-modal-content {
    // background-image: url('assets/intelligentBg.png');
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0.2) 100%
      ),
      linear-gradient(15deg, #f4d9ff 0%, #e7f9dc 100%);
    background-size: cover;
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .aText {
    position: absolute;
    top: 18px;
    left: 35%;
    font-size: 12px;
    cursor: pointer;
  }

  .intelligentSummaryContent {
    width: 100%;
    height: 450px;
    position: relative;

    .labelText {
      float: left;
      color: #333;
      font-size: 14px;
      margin-right: 12px;
      line-height: 32px;
    }

    .moodContent {
      width: 100%;
      height: 30px;
      margin-bottom: 12px;
      margin-top: 12px;

      .moodIcon {
        float: left;
        width: 20px;
        margin-right: 12px;
        margin-top: 5px;
      }
    }

    .toDoTitle {
      width: 100%;
      height: 32px;

      .addBtn {
        float: left;
        margin-left: 20px;
        border-radius: 4px;
        border: 1px solid #3463fc;
        color: #3463fc;
        font-size: 12px;

        .ant-btn .anticon {
          margin-right: 5px;
        }
      }
    }

    .toDoContent {
      width: 100%;
      height: 120px;
      margin-top: 12px;
      overflow: hidden;
      overflow-y: scroll;
      /* 隐藏滚动条 */
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;

      /* IE 10+ */
      .toDoInput {
        width: 100%;
        border-radius: 6px;
        border: 1px solid #e6e6e6;
        margin-bottom: 12px;
        box-shadow: none;
      }

      .ant-input:placeholder-shown {
        font-size: 12px;
      }

      .ant-checkbox-wrapper {
        width: 90%;
        float: left;
      }

      .deleteTodoIcon {
        width: 20px;
        height: 20px;
        float: right;
      }

      .ant-row:hover .deleteTodoIcon {
        background-image: url('./assets/delete-to-do.png');
        background-size: 100%;
        background-repeat: no-repeat;
      }

      .ant-checkbox-group {
        width: 100%;
      }

      .ant-row {
        margin-bottom: 10px;
        cursor: pointer;
      }

      .ant-checkbox + span {
        color: #666;
        font-size: 12px;
        //text-overflow: ellipsis;
        //overflow: hidden;
        //white-space: nowrap;
        width: 100%;
        //height: 18px;
      }

      .ant-checkbox-wrapper-checked {
        .ant-checkbox + span {
          text-decoration-line: line-through;
          color: #999;
        }
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #999;
        border-color: #999;
      }

      .ant-checkbox-checked::after {
        border: 1px solid #999 !important;
      }
    }

    .toDoContent::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }

    .footerBtn {
      width: 100%;
      height: 32px;
      position: absolute;
      bottom: 0px;
      text-align: center;

      .ant-btn {
        margin-right: 12px;
      }
    }
  }
}

.intelligentSummaryModalNew {
  position: relative;

  .ant-modal-body {
    padding-top: 0px;
  }

  .ant-modal-content {
    // background-image: url('assets/intelligentBg.png');
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0.2) 100%
      ),
      linear-gradient(15deg, #f4d9ff 0%, #e7f9dc 100%);
    background-size: cover;
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .aText {
    position: absolute;
    top: 18px;
    left: 25%;
    font-size: 12px;
    cursor: pointer;
  }

  .intelligentSummaryContent {
    width: 100%;
    height: 450px;
    position: relative;

    .labelText {
      float: left;
      color: #333;
      font-size: 14px;
      margin-right: 12px;
      line-height: 32px;
    }

    .moodContent {
      width: 100%;
      height: 30px;
      margin-bottom: 12px;
      margin-top: 12px;

      .moodIcon {
        float: left;
        width: 20px;
        margin-right: 12px;
        margin-top: 5px;
      }
    }

    .toDoTitle {
      width: 100%;
      height: 32px;

      .addBtn {
        float: left;
        margin-left: 20px;
        border-radius: 4px;
        border: 1px solid #3463fc;
        color: #3463fc;
        font-size: 12px;

        .ant-btn .anticon {
          margin-right: 5px;
        }
      }
    }

    .toDoContent {
      width: 100%;
      height: 120px;
      margin-top: 12px;
      overflow: hidden;
      overflow-y: scroll;
      /* 隐藏滚动条 */
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;

      /* IE 10+ */
      .toDoInput {
        width: 100%;
        border-radius: 6px;
        border: 1px solid #e6e6e6;
        margin-bottom: 12px;
        box-shadow: none;
      }

      .ant-input:placeholder-shown {
        font-size: 12px;
      }

      .ant-checkbox-wrapper {
        width: 90%;
        float: left;
      }

      .deleteTodoIcon {
        width: 20px;
        height: 20px;
        float: right;
      }

      .ant-row:hover .deleteTodoIcon {
        background-image: url('./assets/delete-to-do.png');
        background-size: 100%;
        background-repeat: no-repeat;
      }

      .ant-checkbox-group {
        width: 100%;
      }

      .ant-row {
        margin-bottom: 10px;
        cursor: pointer;
      }

      .ant-checkbox + span {
        color: #666;
        font-size: 12px;
        //text-overflow: ellipsis;
        //overflow: hidden;
        //white-space: nowrap;
        width: 100%;
        //height: 18px;
      }

      .ant-checkbox-wrapper-checked {
        .ant-checkbox + span {
          text-decoration-line: line-through;
          color: #999;
        }
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #999;
        border-color: #999;
      }

      .ant-checkbox-checked::after {
        border: 1px solid #999 !important;
      }
    }

    .toDoContent::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }

    .footerBtn {
      width: 100%;
      height: 32px;
      position: absolute;
      bottom: 0px;
      text-align: center;

      .ant-btn {
        margin-right: 12px;
      }
    }
  }
}

//服务总结弹窗
.serviceSummaryModal {
  left: 20%;

  .ant-modal-content {
    // background-image: url('assets/intelligentBg.png');
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0.2) 100%
      ),
      linear-gradient(15deg, #f4d9ff 0%, #e7f9dc 100%);
    background-size: cover;
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .serviceSummaryContent {
    width: 100%;
    padding: 0 20px;
    text-align: center;

    .serviceSummaryIcon {
      width: 120px;
    }

    p {
      margin-top: 30px;
      color: #999;
      text-align: center;
      font-size: 12px;
      margin-bottom: 30px;
    }

    .btnText {
      .ant-btn {
        margin-bottom: 16px;

        span {
          color: #fff;
        }
      }

      span {
        color: #3463fc;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}

//AIGC二层抽屉
.workerDrawer {
  .ant-drawer-header {
    border-bottom: none;
    padding: 16px 20px;

    .ant-drawer-title {
      font-size: 18px;
      font-weight: 400;
    }
  }

  .ant-drawer-body {
    padding: 20px;
    padding-top: 0;
    height: 100%;
  }
}

//联系客户抽屉
.contactCustomersDrawer {
  .ant-drawer-header {
    border-bottom: none;
    color: #333;
    font-size: 20px;
    padding: 20px 20px 0px 20px;
    font-weight: 400;
  }

  .ant-drawer-body {
    padding: 20px;
  }

  .ant-drawer-content-wrapper {
    //width: 40% !important;
  }

  .ant-form label {
    font-size: 12px !important;
  }

  .contactCustomersContent {
    width: 100%;

    .titleItem {
      width: 100%;
      height: 30px;
      color: #333;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
    }

    .ant-picker-input > input {
      font-size: 12px;
    }

    .ant-form-item-label > label {
      font-size: 12px !important;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      float: left;
      line-height: 32px;
      //justify-content: end;
      margin-right: 10px;
      width: 80px;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: inherit;
    }

    .ant-input {
      border-radius: 6px;
      //border: 1px solid #e6e6e6;
      font-size: 12px;
      box-shadow: none;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 6px;
      //border: 1px solid #e6e6e6;
      font-size: 12px;
      box-shadow: none;
    }

    .ant-form-item-control-input-content:last-child {
      //text-align: center;
      .ant-btn {
        font-size: 12px;
        margin-right: 16px;
      }
    }

    .aiOperation {
      width: 100%;
      height: 24px;
      position: relative;

      .draftContainer {
        height: 24px;
        border-radius: 2px;
        padding: 2px 4px;
        float: left;
      }

      .draftContainer:hover {
        background: #eee;
      }

      .draftText {
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 21px;
        float: left;
      }

      .optimizeContainer,
      .elaborateContainer,
      .abbreviationContainer {
        float: left;
        margin-left: 10px;
        height: 20px;
        margin-top: 2px;
        cursor: pointer;

        span {
          display: none;
        }
      }

      .optimizeContainer:hover,
      .elaborateContainer:hover,
      .abbreviationContainer:hover {
        span {
          display: block;
          color: #333;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 21px;
          float: left;
          margin-left: 4px;
        }
      }

      .moreIconContainer {
        float: left;
        margin-left: 10px;
        height: 20px;
        margin-top: 1px;
        cursor: pointer;
      }

      .operationLine {
        width: 1px;
        height: 16px;
        background: #e6e6e6;
        float: left;
        margin-left: 10px;
        margin-top: 3px;
      }

      .emailTemplateIcon {
        float: left;
        margin-left: 10px;
        height: 20px;
        margin-top: 2px;
      }
    }

    .editorjs {
      max-width: 600px;
      width: 100%;
      max-height: 350px;
      min-height: 350px;
      overflow: hidden;
      overflow-y: scroll;
      margin-top: 10px;
      border-radius: 4px;
      border: 1px solid #e6e6e6;
      background: rgba(255, 255, 255, 0.5);
      padding: 2px 10px !important;
      margin-left: 7px;

      img {
        max-width: 250px;
      }

      .codex-editor__redactor {
        //margin-left: 55px;
        padding-bottom: 0px !important;
      }

      //.ce-toolbar__actions {
      //  left: 0px;
      //}
    }

    .attachmentContainer {
      .attachmentIcon {
        float: left;
        margin-top: 7px;
        margin-right: 4px;
      }

      .emailAddAttachmentIcon {
        width: 24px;
        height: 24px;
        float: left;
        cursor: pointer;
        margin-top: 3px;
      }
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-form-item {
      margin-bottom: 20px;
    }

    .ant-form-item-label > label {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 32px;
    }

    .ant-input {
      border-radius: 6px;
      //border: 1px solid #E6E6E6;
      background: rgba(255, 255, 255, 0.5);
      font-size: 12px;
      height: 32px;
      box-shadow: none;
    }

    .ant-select {
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.5);
      font-size: 12px;
      height: 32px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      box-shadow: none;
      border-radius: 6px !important;
    }

    .ant-space-item:first-child {
      //width: 83%;
    }

    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.6);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
      color: #3463fc;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 150% */
    }

    .ant-select-multiple .ant-select-selection-item-remove {
      color: #3463fc;
    }
  }
}

//翻译选择语言弹窗
.selectLanguageModal {
  .ant-form-item-label > label {
    font-size: 12px !important;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    width: 80px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-select {
    font-size: 12px;
  }
}

.selectLanguageModal1 {
  left: 20%;

  .ant-form-item-label > label {
    font-size: 12px !important;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    //justify-content: end;
    margin-right: 10px;
    width: 80px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-select {
    font-size: 12px;
  }
}

//全局提示
.ant-notification-notice.ant-notification-notice-success {
  border-radius: 4px;
  border: 1px solid #e1f3d8;
  background: #f0f9eb;
  padding: 15px 15px 15px 20px;

  .ant-notification-notice-with-icon .ant-notification-notice-message {
    font-size: 14px;
    color: #13c825;
    font-style: normal;
    font-weight: 400;
    margin-left: 30px;
  }

  .ant-notification-notice-icon {
    font-size: 14px;
    margin-top: 2px;
  }

  .anticon.ant-notification-notice-icon-success {
    color: #13c825;
  }
}

.ant-notification-notice.ant-notification-notice-error {
  border-radius: 4px;
  border: 1px solid #fde2e2;
  background: #fef0f0;
  padding: 15px 15px 15px 20px;

  .ant-notification-notice-with-icon .ant-notification-notice-message {
    color: #f22417;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-left: 30px;
  }

  .ant-notification-notice-icon {
    font-size: 14px;
    margin-top: 2px;
  }

  .anticon.ant-notification-notice-icon-error {
    color: #f22417;
  }
}

.ant-notification-notice.ant-notification-notice-warning {
  border-radius: 4px;
  border: 1px solid var(--color-yellow-200, #faecd8);
  background: var(--color-yellow-100, #fdf6ec);

  .ant-notification-notice-with-icon .ant-notification-notice-message {
    color: #e6a23c;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-left: 30px;
  }

  .ant-notification-notice-icon {
    font-size: 14px;
    margin-top: 5px;
  }

  .anticon.ant-notification-notice-icon-error {
    color: #e6a23c;
  }
}

.serviceObjectivesText1 {
  color: #f22417;
  font-weight: 700;
}

.serviceObjectivesText2 {
  color: #333;
  font-weight: 700;
}

.serviceObjectivesText3 {
  color: #13c825;
}

.serviceObjectivesText4 {
  color: #fcb830;
}

.ant-menu-inline-collapsed-tooltip {
  display: none !important;
}

.addChannelAllocation {
  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    font-size: 12px;

    svg:not(:root) {
      margin-right: 5px;
      float: left;
      margin-top: 3px;
    }
  }
}

//移动端首页样式
.menuDownPersonal.ant-dropdown {
  width: 150px !important;

  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    text-align: center;
    color: #333;
    font-size: 12px;
  }

  .ant-dropdown-menu-item-active {
    background-color: #3463fc;
    color: #fff;
  }

  .ant-dropdown-menu-item-selected,
  .ant-dropdown-menu-submenu-title-selected {
    background-color: #3463fc;
    color: #fff;
  }
}

.phoneSelectLanguageModal {
  width: 86% !important;

  //top: 200px !important;
  .ant-modal-body {
    height: 400px;
    padding: 0px;
  }

  .ant-modal-content {
    border-radius: 10px 10px 10px 10px !important;
  }

  .ant-modal-header {
    background: #3463fc;
    border-radius: 10px 10px 0px 0px;
    text-align: center;
  }

  .ant-modal-header .ant-modal-title {
    color: #ffffff !important;
  }

  .centerDetail {
    width: 100%;
    height: 340px;
    padding: 0px 24px;
    overflow: hidden;
    overflow-y: scroll;
    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */

    .ant-radio-group {
      width: 100%;

      .ant-radio-wrapper {
        width: 100%;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        font-size: 14px;
        color: #333333;
      }

      .ant-radio {
        position: absolute;
        right: 0px;
        top: 15px;
      }

      .ant-radio-inner {
        width: 20px;
        height: 20px;
      }
    }
  }

  .centerDetail::-webkit-scrollbar {
    display: none;
    /* Chrome Safari */
  }

  .footerDetail {
    width: 100%;
    height: 60px;
    text-align: center;
    box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 0px 0px 10px 10px;

    .ant-btn {
      margin-left: 5px;
      margin-right: 5px;
      margin-top: 14px;
    }
  }
}

//添加知识库文档弹窗
.addKnowledgeModal {
  width: 44% !important;

  .knowledgeTypeContent {
    .leftType,
    .leftTypeEditor {
      width: 49%;
      //height: 195px;
      height: 267px;
      padding: 20px;
      float: left;
      border-radius: 4px;
      //border: 1px solid #3463fc;
      text-align: center;
      margin-bottom: 20px;
      cursor: pointer;

      img {
        width: 64px;
        margin-top: 6px;
        margin-bottom: 8px;
      }
    }

    .rightType,
    .rightTypeEditor {
      width: 49%;
      //height: 195px;
      height: 267px;
      padding: 20px;
      float: left;
      border-radius: 4px;
      //border: 1px solid #3463fc;
      margin-left: 2%;
      text-align: center;
      margin-bottom: 20px;
      cursor: pointer;

      img {
        width: 64px;
        margin-top: 6px;
        margin-bottom: 8px;
      }
    }

    /* 当屏幕宽度小于等于 1440px 时应用这些样式 */
    @media only screen and (max-width: 1430px) {
      .leftType {
        width: 99%;
      }

      .rightType {
        width: 99%;
      }
    }

    /* 当屏幕宽度大于 1440px 时应用这些样式 */
    @media only screen and (min-width: 14431px) {
      .leftType {
        width: 49%;
      }

      .rightType {
        width: 49%;
      }
    }

    .nameTitle {
      color: #3463fc;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
    }

    .detailText {
      color: #333;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      margin-bottom: 0px;
    }

    .tipsText {
      color: #333;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
    }

    .rightType:hover,
    .leftType:hover {
      border: 1px solid #3463fc;
    }
  }

  .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding-bottom: 20px;
  }

  .ant-form-item-label > label {
    width: 100px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    line-height: 32px;
  }

  .ant-input:placeholder-shown,
  .ant-input {
    font-size: 12px;
    box-shadow: none;
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    background: #fff;
  }

  .ant-form-item-explain-error {
    font-size: 12px;
  }

  .ant-btn {
    font-size: 12px;
  }

  .knowledgeTips {
    color: #e6a23c;
    font-size: 12px;
    float: left;
    margin-left: 5%;
  }
}

.blueBorder {
  border-left: 4px #3463fc solid;
  height: 28px;
  width: max-content;
  padding-left: 12px;
  font-size: 18px;
  font-weight: 400;
  flex-shrink: 0;

  .calendarTitle {
    float: left;
  }

  .marketingEventBtn {
    margin-left: 20px;
    margin-top: -3px;
    background: #8501bb;
    border: 1px solid #8501bb;
    border-radius: 4px 4px 4px 4px;
    float: left;

    .anticon svg {
      margin-right: 3px;
    }
  }

  .marketingEventBtn:hover {
    background: #8501bb;
    border: 1px solid #8501bb;
    border-radius: 4px 4px 4px 4px;
  }
}

//添加同义词规则弹窗
.addSynonymRulesModal {
  .ant-form label {
    font-size: 12px;
  }

  .ant-form-item-label > label {
    width: 80px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: right;
    display: inline-block;
    line-height: 32px;
  }

  .ant-input {
    font-size: 12px;
    border-radius: 4px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    box-shadow: none;
    margin-bottom: 10px;
  }

  .ant-form-item-control-input-content {
    max-height: 150px;
    overflow: hidden;
    overflow-y: scroll;
  }

  .unidirectionalSynonymTag {
    .ant-tag {
      font-size: 12px;
      color: #fcb830;
      font-style: normal;
      font-weight: 400;
      border-radius: 4px;
      border: 1px solid rgba(252, 184, 48, 0.5);
      background: rgba(252, 184, 48, 0.05);
      padding-top: 2px;
      padding-bottom: 2px;
      margin-top: 0px;
      margin-bottom: 5px;
    }

    .ant-tag:first-child {
      margin-left: 0px;
    }

    .ant-tag-close-icon {
      color: #fcb830;
      margin-left: 8px;
      width: 12px;
    }
  }

  .bidirectionalSynonymTag {
    .ant-tag {
      font-size: 12px;
      color: #13c825;
      font-style: normal;
      font-weight: 400;
      border-radius: 4px;
      border: 1px solid rgba(19, 200, 37, 0.5);
      background: rgba(19, 200, 37, 0.05);
      padding-top: 2px;
      padding-bottom: 2px;
      margin-top: 0px;
      margin-bottom: 5px;
    }

    .ant-tag:first-child {
      margin-left: 0px;
    }

    .ant-tag-close-icon {
      color: #13c825;
      margin-left: 8px;
      width: 12px;
    }
  }

  .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding-bottom: 20px;
  }

  .ant-btn {
    font-size: 12px;
  }
}

.deleteSynonymRulesModal {
  p {
    img {
      width: 20px;
      float: left;
      margin-right: 8px;
      margin-bottom: 20px;
    }

    margin-bottom: 0px;
  }

  .ant-input {
    font-size: 12px;
    margin-top: 20px;
    border-radius: 4px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    box-shadow: none;
  }

  .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding-bottom: 20px;
  }

  .ant-btn {
    font-size: 12px;
  }
}

//工作台工单tab弹窗
.AddFilterModalNewTab {
  position: relative;
  left: 20%;

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-body {
    padding: 10px 30px 0px 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    justify-content: end;
    margin-right: 10px;
    min-width: 80px;
    text-align: right;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }
}

//添加邮件营销弹窗
.addMarketingEventModal {
  top: 25%;

  .addMarketingEventContent {
    width: 100%;
    height: 165px;

    .emailMarketingContent,
    .otherMarketingContent {
      width: 30%;
      height: 160px;
      float: left;
      text-align: center;
      background: #ffffff;
      box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
      border-radius: 4px 4px 4px 4px;
      //border: 1px solid #E6E6E6;
      cursor: pointer;
      background-size: 100% 100%;

      img {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
        margin-top: 48px;
      }

      p {
        font-size: 12px;
        color: #333333;
      }
    }

    .whatsappMarketingContent {
      width: 30%;
      height: 160px;
      float: left;
      text-align: center;
      background: #f9f9f9;
      border-radius: 4px 4px 4px 4px;
      //border: 1px solid #e6e6e6;
      margin-left: 5%;
      margin-right: 5%;
      background-size: 100% 100%;
      cursor: not-allowed;

      img {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
        margin-top: 48px;
        opacity: 0.5;
      }

      p {
        font-size: 12px;
        color: #999999;
      }
    }
  }

  .ant-modal-footer {
    border: none;
    text-align: center;
  }
}

//添加自定义事件弹窗
.addMarketingCustomEventModal {
  .ant-form-item-label > label {
    width: 112px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    line-height: 30px;
  }

  .ant-input {
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    //border: 1px solid #e6e6e6;
    box-shadow: none;
    font-size: 12px;
    color: #333333;
  }

  .ant-select {
    font-size: 12px;
    color: #333333;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    //border: 1px solid #e6e6e6;
    box-shadow: none;
  }

  .ant-picker {
    width: 100%;
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    //border: 1px solid #e6e6e6;
    box-shadow: none;
    font-size: 12px;
    color: #333333;
  }

  .ant-picker-input > input {
    font-size: 12px;
  }

  .ant-tag {
    font-size: 12px;
    color: #3463fc;
    font-style: normal;
    font-weight: 400;
    background: rgba(52, 99, 252, 0.05);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(52, 99, 252, 0.5);
    padding-top: 2px;
    padding-bottom: 2px;
    margin-top: 5px;
    margin-bottom: 0px;
  }

  .ant-tag:first-child {
    margin-left: 0px;
  }

  .ant-tag-close-icon {
    //color: #fcb830;
    margin-left: 8px;
    width: 12px;
  }

  .ant-form-item-explain-error {
    font-size: 12px;
  }
}

.echartsRadar {
  //height: 320px;
}

//批量删除客户细分提示弹窗
.deleteCustomerModal {
  .ant-modal-body {
    padding-top: 0px;
  }

  .deleteText {
    font-size: 16px;
    color: #333333;
    margin-bottom: 5px;
  }

  .deleteTips {
    font-size: 12px;
    color: #999999;
  }

  .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding-top: 0px;
  }
}

.ant-modal.deleteCustomerModal {
  width: 30% !important;
  top: 30%;
}

.disabled {
  filter: grayscale(100%);
  /* 将元素置灰 */
  pointer-events: none;
  // /* 禁止点击 */
}

.disabledCursor {
  cursor: not-allowed !important;
}

.disabledFont {
  color: #999 !important;
  cursor: not-allowed !important;
  pointer-events: none;

  svg path {
    fill: #999 !important;
  }
}

.ant-popover-inner-content {
  font-size: 12px !important;
}

//自定义营销事件弹窗时间框
.ant-picker-now {
  display: none !important;
}

.ant-popover-hidden {
  //display:block;
}

.operationTipsPopover {
  width: 25%;
}

.addCustomerTagModal {
  // .ant-modal-body {
  //   height: 200px;
  // }
  top: 35%;
  left: 8%;

  .ant-modal-footer {
    border-top: none;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  // .ant-tag {
  //   font-size: 12px;
  //   background-color: rgba(52, 99, 252, 0.05) !important;
  //   color: #3463fc !important;
  //   border-color: rgba(52, 99, 252, 0.5) !important;
  // }

  .ant-btn {
    font-size: 12px;
  }

  label {
    font-size: 12px;
    float: left;
    line-height: 30px;
    margin-left: 20px;
  }

  .ant-input {
    font-size: 12px;
    border-radius: 4px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    box-shadow: none;
    margin-bottom: 10px;
  }

  .ant-modal-footer {
    text-align: center;
  }
}

.addCustomerTagModalNew {
  position: relative;
  left: 20%;
  top: 35%;

  // .ant-modal-body {
  //   height: 200px;
  // }
  .ant-modal-footer {
    border-top: none;
  }

  // .ant-tag {
  //   font-size: 12px;
  //   background-color: rgba(52, 99, 252, 0.05) !important;
  //   color: #3463fc !important;
  //   border-color: rgba(52, 99, 252, 0.5) !important;
  // }

  .ant-btn {
    font-size: 12px;
  }

  label {
    font-size: 12px;
    float: left;
    line-height: 30px;
    margin-left: 20px;
  }

  .ant-input {
    font-size: 12px;
    border-radius: 4px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    box-shadow: none;
    margin-bottom: 10px;
  }

  .ant-modal-footer {
    text-align: center;
  }

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }
}

.ant-popover-message-title {
  font-size: 12px;
}

.ant-popover-buttons {
  .ant-btn {
    font-size: 12px;
  }
}

.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary[disabled]:active {
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: #d9d9d9 !important;
  background: #f5f5f5 !important;
  text-shadow: none;
  box-shadow: none;

  svg path {
    fill: rgba(0, 0, 0, 0.25) !important;
  }
}

.ant-btn[disabled],
.ant-btn[disabled]:hover,
.ant-btn[disabled]:focus,
.ant-btn[disabled]:active {
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: #d9d9d9 !important;
  background: #f5f5f5 !important;
  text-shadow: none;
  box-shadow: none;

  svg path {
    fill: rgba(0, 0, 0, 0.25) !important;
  }
}

.ant-btn-dangerous[disabled],
.ant-btn-dangerous[disabled]:hover,
.ant-btn-dangerous[disabled]:focus,
.ant-btn-dangerous[disabled]:active {
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: #d9d9d9 !important;
  background: #f5f5f5 !important;
  text-shadow: none;
  box-shadow: none;

  svg path {
    fill: rgba(0, 0, 0, 0.25) !important;
  }
}

.agentWorkEfficiencyStatisticsPopover {
  max-width: 22%;

  p {
    margin-bottom: 0px;
  }

  .ant-tag {
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    background: rgba(153, 153, 153, 0.1);
    color: #333;
    font-size: 12px;
    padding: 3px 12px;
  }

  .ant-tag:first-child {
    margin-left: 0px;
  }
}

.selfAssessmentDetailsPopover {
  max-width: 22%;

  p {
    margin-bottom: 0px;
    color: #fff;
  }

  .ant-popover-inner {
    background: rgba(0, 0, 0, 0.8);
  }

  .ant-popover-arrow-content {
    --antd-arrow-background-color: rgba(0, 0, 0, 0.8);
  }
}

.selfAssessmentDetailsPopover1 {
  max-width: 50%;

  p {
    margin-bottom: 0px;
    color: #fff;
  }

  .ant-popover-inner {
    background: rgba(0, 0, 0, 0.8);
  }

  .ant-popover-arrow-content {
    --antd-arrow-background-color: rgba(0, 0, 0, 0.8);
  }
}

.correctAnswerModal {
  .ant-modal {
    top: 25%;
  }

  .ant-modal-body {
    padding-top: 0px;
  }

  .ant-modal-footer {
    text-align: center;
    border-top: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 14px !important;
    font-style: normal;
    font-weight: 700 !important;
  }

  textarea.ant-input {
    font-size: 12px;
    box-shadow: none;
  }
}

.personalRemarksModal {
  top: 25% !important;

  .ant-modal-header {
    padding-bottom: 0px;
  }

  .ant-modal-body {
    padding-top: 15px;
  }

  .ant-input {
    border-radius: 4px;
    background: #fff;
    font-size: 12px;
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    padding-top: 0px;
    padding-bottom: 20px;
  }

  .ant-modal-content {
    background: rgb(255, 255, 255, 0.8);
  }

  .operationContentPersonalRemarks {
    width: 100%;
    height: 32px;
    margin-bottom: 10px;
    float: left;

    .operationIcon {
      width: 24px;
      height: 24px;
      float: left;
      margin-right: 10px;
      cursor: pointer;
    }

    .intelligenceSummary {
      color: #fff;
      font-size: 12px;
      border-radius: 4px;
      border: none;
      margin-left: 10px;
      height: 24px;
      padding: 2px 10px;
      float: left;
      background: linear-gradient(56deg, #187cfc 20.15%, #7700fe 79.85%),
        linear-gradient(0deg, #fcb830 0%, #fcb830 100%), #3463fc;
    }
  }
}

.commentFeedbackPopover {
  width: 21%;

  .ant-popover-placement-bottom .ant-popover-arrow,
  .ant-popover-arrow {
    display: none !important;
  }

  .ant-popover-inner-content {
    width: 100%;
    border: 4px;

    .optionItem {
      width: 100%;
      height: 48px;
      padding-left: 5%;
      cursor: pointer;

      img {
        width: 28px;
        margin-top: 10px;
        float: left;
        margin-right: 4px;
      }

      span {
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        float: left;
        line-height: 48px;
      }
    }

    .optionItem:hover {
      background: rgba(52, 99, 252, 0.05);
    }
  }
}

.ant-popover-hidden {
  //display: block;
}

.workTableOperationContent {
  .ant-popover-inner-content {
    padding: 0px;
  }

  .ticketOperationList {
    list-style: none;
    padding-left: 0px;

    li {
      width: 100%;
      height: 30px;
      padding: 0px 10px;
      cursor: pointer;
    }

    li:hover {
      background: #f3f4fe;
    }

    img {
      width: 16px;
      height: 16px;
      float: left;
      margin-right: 4px;
      margin-top: 7px;
    }

    span {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 30px;
    }
  }

  .ant-popover-arrow {
    display: none;
  }
}

.workTableOperationContent.ant-popover-placement-bottom {
  padding-top: 5px;
}

.emailTemplateTooltip {
  margin-top: 10px;
}

.moreOperationDrodown {
  .ant-dropdown-menu {
    border-radius: 4px;
  }

  .detailItems {
    //width: 130px;
    float: left;

    span {
      float: left;
      margin-right: 10px;
      width: 16px;
      height: 16px;
      margin-top: 2px;
    }

    p {
      float: left;
      margin-bottom: 0px;
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      white-space: nowrap;
      /* 18px */
    }
  }

  .detailNormalItems {
    //width: 120px;
    float: left;

    span {
      float: left;
      margin-right: 10px;
      width: 16px;
      height: 16px;
      margin-top: 2px;
    }

    p {
      float: left;
      margin-bottom: 0px;
      color: #999;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      white-space: nowrap;
      /* 18px */
    }
  }

  .ant-dropdown-menu-item:hover {
    background: #f3f4fe;

    .detailItems {
      p {
        color: #3463fc;
      }
    }

    .detailNormalItems {
      p {
        color: #999;
      }
    }
  }
}

//.ant-dropdown-hidden, .ant-dropdown-menu-hidden, .ant-dropdown-menu-submenu-hidden{
//  display: block;
//}

.draftContainerPopover {
  width: 70% !important;

  .ant-popover-inner-content {
    width: 100%;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(50px);
  }

  .ant-popover-placement-bottomLeft .ant-popover-arrow,
  .ant-popover-arrow-content {
    display: none;
  }

  .ant-popover-inner {
    margin-top: -18px;
  }

  textarea.ant-input {
    width: 100%;
    border-radius: 6px;
    background: #fff;
    box-shadow: none;
    font-size: 12px;
  }

  .sendDraftIcon {
    float: right;
    margin-right: -5px;
    cursor: pointer;
    margin-top: -35px;
  }

  .popoverTitle {
    color: #333;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 3px;

    span {
      color: #999;
      font-size: 10px;
    }
  }

  .btnContainer {
    width: 100%;
    margin-top: 15px;
    text-align: right;

    .intelligenceSummary {
      color: #fff;
      font-size: 12px;
      border-radius: 4px;
      border: none;
      margin-left: 10px;
      height: 32px;
      //width: 92px;
      padding: 2px 10px;
      background: linear-gradient(56deg, #187cfc 20.15%, #7700fe 79.85%),
        linear-gradient(0deg, #fcb830 0%, #fcb830 100%), #3463fc;
    }

    .ant-btn {
      font-size: 12px;
    }
  }
}

.draftContainerPopover1 {
  width: 30% !important;

  .ant-popover-inner-content {
    width: 100%;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(50px);
  }

  .ant-popover-placement-bottomLeft .ant-popover-arrow,
  .ant-popover-arrow-content {
    display: none;
  }

  .ant-popover-inner {
    margin-top: -18px;
  }

  textarea.ant-input {
    width: 100%;
    border-radius: 6px;
    background: #fff;
    box-shadow: none;
    font-size: 12px;
  }

  .sendDraftIcon {
    float: right;
    margin-right: -5px;
    cursor: pointer;
    margin-top: -35px;
  }

  .popoverTitle {
    color: #333;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 3px;

    span {
      color: #999;
      font-size: 10px;
    }
  }

  .btnContainer {
    width: 100%;
    margin-top: 15px;
    text-align: right;

    .intelligenceSummary {
      color: #fff;
      font-size: 12px;
      border-radius: 4px;
      border: none;
      margin-left: 10px;
      height: 32px;
      //width: 92px;
      padding: 2px 10px;
      background: linear-gradient(56deg, #187cfc 20.15%, #7700fe 79.85%),
        linear-gradient(0deg, #fcb830 0%, #fcb830 100%), #3463fc;
    }

    .ant-btn {
      font-size: 12px;
    }
  }
}

.emailTemplateContainerPopover {
  width: 430px !important;

  .ant-popover-inner-content {
    width: 100%;
  }

  .ant-popover-placement-bottomLeft .ant-popover-arrow,
  .ant-popover-arrow-content {
    display: none;
  }

  .ant-popover-inner {
    margin-bottom: -18px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .emailTemplateTitle {
    width: 100%;
    margin-bottom: 11px;

    span {
      color: #333;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }

    img {
      width: 20px;
      height: 20px;
      float: right;
      cursor: pointer;
      z-index: 10;
    }
  }

  .searchContainer {
    margin-bottom: 15px;

    .ant-input-affix-wrapper {
      border-radius: 4px;
      //border: 1px solid #E6E6E6;
      background: #fff;
      font-size: 12px;
      color: #333;
      box-shadow: none;
      height: 32px;
    }

    .ant-input {
      height: 22px !important;
    }
  }

  .emailTemplateList {
    width: 100%;

    .emailTemplateItem {
      width: 100%;
      height: 30px;
      padding: 0px 5px;

      .defaultItem {
        width: 85%;
        float: left;
        line-height: 30px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 0px;

        .circleText {
          width: 3px;
          height: 3px;
          background: #000;
          border-radius: 50%;
          float: left;
          margin-top: 14px;
          margin-right: 7px;
          margin-left: 4px;
        }
      }

      .normalItem {
        width: 100%;
        float: left;
        line-height: 30px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 0px;

        .circleText {
          width: 3px;
          height: 3px;
          background: #000;
          border-radius: 50%;
          float: left;
          margin-top: 14px;
          margin-right: 7px;
          margin-left: 4px;
        }
      }

      .defaultContainer {
        color: #fcb830;
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        line-height: 10px;
        /* 100% */
        border-radius: 2px;
        background: rgba(252, 184, 48, 0.2);
        float: right;
        padding: 4px 8px;
        margin-top: 5px;
      }

      .emailTemplateOperation {
        display: none;
      }
    }

    .emailTemplateItem:hover {
      border-radius: 4px;
      background: linear-gradient(0deg, #f3f4fe 0%, #f3f4fe 100%), #fff;
      /* 阴影 */
      box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);

      .defaultItem,
      .normalItem {
        width: 82%;
      }

      .emailTemplateOperation {
        display: block;
        float: right;

        img {
          width: 18px;
          height: 18px;
          float: left;
          margin-left: 4px;
          margin-top: 6px;
          cursor: pointer;
        }
      }

      .defaultContainer {
        display: none;
      }
    }
  }
}

.defaultTipsConfirm {
  width: 40%;

  .ant-popover-message-icon {
    display: none;
  }

  .ant-popover-buttons {
    text-align: center;
  }

  .ant-popover-buttons .ant-btn {
    //padding-left: 12px;
    //padding-right: 12px;
  }
}

.emailTemplateModal {
  top: 18%;
  width: 600px !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-body {
    padding: 10px 40px 0px 20px;
    height: 400px !important;
  }

  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: #333;
    float: left;
    line-height: 32px;
    //justify-content: end;
    //margin-right: 10px;
    //width: 100px;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inherit;
  }

  .ant-form-item-label
    > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
    display: none;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }

  .ant-form label {
    font-size: 12px;
    font-weight: 400;
    color: #333;
  }

  .emailTemplateBody {
    width: 100%;
    height: 260px;
    margin-bottom: 20px;
    overflow: hidden;
    overflow-y: scroll;
    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    background: rgba(255, 255, 255, 0.5);

    .editorjs1 {
      width: 100%;
      margin-top: 10px;
      padding: 2px 10px;
      max-height: 350px;
      min-height: 350px;
      overflow: hidden;
      overflow-y: scroll;

      img {
        max-width: 250px;
      }

      .codex-editor__redactor {
        //margin-left: 55px;
        padding-bottom: 0px !important;
      }

      //.ce-toolbar__actions {
      //  left: 0px;
      //}
    }
  }

  .emailTemplateBody::-webkit-scrollbar {
    display: none;
    /* Chrome Safari */
  }
}

.ant-modal-wrap {
  z-index: 1031;
}

.ant-form-item-explain-error {
  font-size: 12px;
}

.tagDeleteTipsConfirm {
  .ant-popover-message-icon {
    //display: none;
  }

  .ant-popover-buttons {
    text-align: center;
  }

  .ant-popover-buttons .ant-btn {
    padding-left: 12px;
    padding-right: 12px;
  }
}

.tagSettingsModal {
  top: 25%;
  //width: 20% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 18px !important;
    font-style: normal;
    font-weight: 700 !important;
    line-height: 150%;
    /* 27px */
  }

  .ant-modal-body {
    padding: 10px 40px 0px 20px;
    margin-bottom: 15px !important;
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 10px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #3463fc;
    border-color: #3463fc;
  }
}

.tagOperationModal {
  top: 25%;
  width: 50% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 18px !important;
    font-style: normal;
    font-weight: 700 !important;
    line-height: 150%;
    /* 27px */
  }

  .ant-modal-body {
    min-height: 160px;
    padding: 10px 40px 0px 20px;
    margin-bottom: 15px !important;

    .tagOperationDetail {
      width: 100%;

      .pTips {
        color: #999;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        /* 150% */
      }

      .leftTagContainer {
        width: 78%;
        float: left;

        .labelText {
          color: #333;
          text-align: right;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 32px;
          /* 18px */
          width: 80px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-bottom: 0px;
          margin-right: 3px;
          float: left;
        }

        .ant-input {
          border-radius: 4px;
          background: #fff;
          box-shadow: none;
          width: 84%;
          height: 32px;
          font-size: 12px;
        }

        .tagNotes {
          width: 100%;
          font-size: 12px;
          color: #999999;
          margin-top: 5px;
          margin-bottom: 5px;
          margin-left: 20px;
        }

        .tagDetailContainer {
          width: 85%;
          margin-bottom: 10px;
          margin-left: 60px;
          margin-top: 8px;
          max-height: 90px;
          overflow: hidden;
          overflow-y: scroll;
          /* 隐藏滚动条 */
          scrollbar-width: none;
          /* firefox */
          -ms-overflow-style: none;

          /* IE 10+ */
          .ant-tag {
            height: 24px;
            font-size: 12px;
            padding: 1px 10px;
            margin-bottom: 5px;

            span {
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
            }
          }

          .ant-tag:first-child {
            margin-left: 0px;
          }

          .colorType1 {
            border-radius: 4px;
            border: 1px solid #3463fc;
            background: rgba(52, 99, 252, 0.1);

            span {
              color: #3463fc;
            }
          }

          .colorType2 {
            border-radius: 4px;
            border: 1px solid #00b900;
            background: rgba(0, 185, 0, 0.1);

            span {
              color: #00b900;
            }
          }

          .colorType3 {
            border-radius: 4px;
            border: 1px solid #ad30e5;
            background: rgba(173, 48, 229, 0.1);

            span {
              color: #ad30e5;
            }
          }

          .colorType4 {
            border-radius: 4px;
            border: 1px solid #2d6973;
            background: rgba(45, 105, 115, 0.1);

            span {
              color: #2d6973;
            }
          }

          .colorType5 {
            border-radius: 4px;
            border: 1px solid #f22417;
            background: rgba(242, 36, 23, 0.1);

            span {
              color: #f22417;
            }
          }

          .colorType6 {
            border-radius: 4px;
            border: 1px solid #d7ce1e;
            background: rgba(215, 206, 30, 0.1);

            span {
              color: #d7ce1e;
            }
          }

          .colorType7 {
            border-radius: 4px;
            border: 1px solid #86bf00;
            background: rgba(134, 191, 0, 0.1);

            span {
              color: #86bf00;
            }
          }

          .colorType8 {
            border-radius: 4px;
            border: 1px solid #fb8f83;
            background: rgba(251, 143, 131, 0.1);

            span {
              color: #fb8f83;
            }
          }

          .colorType9 {
            border-radius: 4px;
            border: 1px solid #64635e;
            background: rgba(100, 99, 94, 0.1);

            span {
              color: #64635e;
            }
          }

          .colorType10 {
            border-radius: 4px;
            border: 1px solid #9c9992;
            background: rgba(156, 153, 146, 0.1);

            span {
              color: #9c9992;
            }
          }

          .colorType12 {
            border-radius: 4px;
            border: 1px solid #ee6521;
            background: rgba(238, 101, 33, 0.1);

            span {
              color: #ee6521;
            }
          }

          .colorType13 {
            border-radius: 4px;
            border: 1px solid #fb83f5;
            background: rgba(251, 131, 245, 0.1);

            span {
              color: #fb83f5;
            }
          }
        }
      }

      .rightTagContainer {
        float: right;

        .ant-select {
          width: 120px;
        }

        .ant-select-selection-item {
          .color1 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #3463fc;
            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);

            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color2 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #00b900;
            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color3 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #ad30e5;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color4 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #2d6973;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color5 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #f22417;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color6 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #d7ce1e;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color7 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #86bf00;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color8 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #fb8f83;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color9 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #64635e;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color10 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #9c9992;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color12 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #ee6521;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          .color13 {
            height: 20px;
            width: 20px;
            border-radius: 4px;
            background: #fb83f5;

            /* 阴影 */
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
            float: left;
            margin-top: 5px;
            margin-right: 5px;
          }

          span {
            font-size: 12px;
            color: #333;
          }
        }

        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 4px;
          //border: 1px solid #E6E6E6;
          background: #fff;
          box-shadow: none;
        }
      }

      .ant-form-item-label {
        width: 80px;
        text-align: right;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-right: 3px;
        font-size: 12px;
      }

      .ant-form-item-label > label {
        width: 80px;
        text-align: right;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-block;
        margin-right: 3px;
        line-height: 30px;
      }

      .ant-checkbox + span {
        font-size: 12px;
        color: #333;
      }

      .ant-select {
        background: #fff;
        font-size: 12px;
      }

      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        box-shadow: none;
        border-radius: 4px;
      }

      .ant-form-item-label
        > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        display: none;
      }

      .ant-form-item-explain-error {
        font-size: 12px;
      }

      .tagClassificationSelect {
        width: 100%;
        margin-bottom: 10px;

        .labelText {
          color: #333;
          text-align: right;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 30px;
          /* 18px */
          width: 60px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-bottom: 0px;
          margin-right: 3px;
          float: left;
        }

        .ant-select {
          width: 69.6%;
        }

        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 4px;
          background: #fff;
          box-shadow: none;
        }
      }
    }

    .tagOperationDetail::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 5px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

.tagColorSelect {
  height: 100px;

  .rc-virtual-list-holder-inner {
    display: inline-block !important;
  }

  .ant-select-item {
    width: 20px;
    margin: 5px;
    padding: 0px;
    min-height: 20px;
  }

  .ant-select-item-option {
    display: inline-block;
  }

  .ant-select-item-option-content {
    span {
      display: none;
    }
  }

  .color1 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #3463fc;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color2 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #00b900;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color3 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #ad30e5;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color4 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #2d6973;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color5 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #f22417;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color6 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #d7ce1e;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color7 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #86bf00;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color8 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #fb8f83;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color9 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #64635e;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color10 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #9c9992;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color12 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #ee6521;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .color13 {
    height: 20px;
    width: 20px;
    border-radius: 4px;
    background: #fb83f5;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }
}

.workTableTagSelectColor {
  .colorType1 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #3463fc;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType2 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #00b900;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType3 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #ad30e5;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType4 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #2d6973;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType5 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #f22417;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType6 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #d7ce1e;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType7 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #86bf00;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType8 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #fb8f83;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType9 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #64635e;

    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType10 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #9c9992;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType11 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #fcb830;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType12 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #ee6521;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType13 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #fb83f5;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType14 {
    border: 1px solid #31c759;
    color: white;
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #31c759;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType15 {
    border: 1px solid #97dc16;
    color: white;
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #97dc16;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType16 {
    border: 1px solid #21b1e1;
    color: white;
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #21b1e1;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .colorType17 {
    float: left;
    height: 12px;
    width: 12px;
    margin-top: 4px;
    border-radius: 2px;
    background: #eb903b;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }

  span {
    float: left;
    line-height: 20px;
    margin-left: 4px;
  }
}

.ant-select-dropdown-hidden {
  //display: block;
}

//服务总结弹窗
.emailServiceSummaryModal {
  left: -20%;

  .ant-modal-content {
    // background-image: url('assets/intelligentBg.png');
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0.2) 100%
      ),
      linear-gradient(15deg, #f4d9ff 0%, #e7f9dc 100%);
    background-size: cover;
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    border-radius: 6px;
  }

  .ant-modal-header {
    background: none;
  }

  .serviceSummaryContent {
    width: 100%;
    padding: 0 20px;
    text-align: center;

    .serviceSummaryIcon {
      width: 120px;
    }

    p {
      margin-top: 30px;
      color: #999;
      text-align: center;
      font-size: 12px;
      margin-bottom: 30px;
    }

    .btnText {
      .ant-btn {
        margin-bottom: 16px;

        span {
          color: #fff;
        }
      }

      span {
        color: #3463fc;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}

//创建标签类型
.createTageClassification {
  top: 25% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 18px !important;
    font-style: normal;
    font-weight: 700 !important;
    line-height: 150%;
    /* 27px */
  }

  .ant-modal-body {
    padding: 10px 40px 0px 20px;
    margin-bottom: 15px !important;

    .ant-input {
      border-radius: 4px;
      background: #fff;
      box-shadow: none;
      font-size: 12px;
      color: #333;
    }

    .ant-btn {
      margin-right: 16px;
    }

    .ant-form-item-explain-error {
      font-size: 12px;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 10px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

// 工单领取
.ticketContent {
  left: 350px !important;
  top: 100px !important;
  // position: absolute;
  // left: 30% !important;
  // top:100px !important;
  // width: 171px;

  .ant-popover-placement-bottom .ant-popover-arrow,
  .ant-popover-arrow {
    display: none !important;
  }

  .ant-popover-inner-content {
    padding: 0px;
  }

  .ticketCollectList {
    .ant-popover-arrow {
      display: none;
    }

    list-style: none;
    padding-left: 0px;
    border-radius: 4px 4px 0px 0px;

    li {
      width: 171px;
      height: 30px;
      padding: 6px 10px;
      cursor: pointer;
      text-align: center;
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }

    li:hover {
      background: #f3f4fe;
    }
  }
}

// 拖拽排序
.row-dragging {
  background: #fff;
  border: 1px solid #ccc;

  td {
    padding: 16px;
    vertical-align: middle;
    width: 120px;

    img {
      width: 24px;
      height: 24px;
    }
  }

  .drag-visible {
    visibility: visible;
  }
}

//解决工单完成提示弹窗
.resolveTicketTipsModal {
  top: 20%;
  width: 30% !important;

  .ant-modal-content {
    border-radius: 4px;
    border: 1px solid #3463fc;
    background: linear-gradient(
        0deg,
        rgba(52, 99, 252, 0.1) 0%,
        rgba(52, 99, 252, 0.1) 100%
      ),
      #fff;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(50px);
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 18px !important;
    font-style: normal;
    font-weight: 700 !important;
    line-height: 150%;
    /* 27px */
  }

  .ant-modal-body {
    padding: 10px;

    div {
      text-align: right;

      span {
        color: #3463fc;
        text-align: justify;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        /* 18px */
        cursor: pointer;
      }
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 10px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

.colorType1 {
  color: #3463fc;
  border-radius: 4px;
  border: 1px solid #3463fc;
  background: rgba(52, 99, 252, 0.1);

  .ant-tag-close-icon {
    color: #3463fc;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType2 {
  color: #00b900;
  border-radius: 4px;
  border: 1px solid #00b900;
  background: rgba(0, 185, 0, 0.1);

  .ant-tag-close-icon {
    color: #00b900;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType3 {
  color: #ad30e5;
  border-radius: 4px;
  border: 1px solid #ad30e5;
  background: rgba(173, 48, 229, 0.1);

  .ant-tag-close-icon {
    color: #ad30e5;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType4 {
  color: #2d6973;
  border-radius: 4px;
  border: 1px solid #2d6973;
  background: rgba(45, 105, 115, 0.1);

  .ant-tag-close-icon {
    color: #2d6973;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType5 {
  color: #f22417;
  border-radius: 4px;
  border: 1px solid #f22417;
  background: rgba(242, 36, 23, 0.1);

  .ant-tag-close-icon {
    color: #f22417;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType6 {
  color: #d7ce1e;
  border-radius: 4px;
  border: 1px solid #d7ce1e;
  background: rgba(215, 206, 30, 0.1);

  .ant-tag-close-icon {
    color: #d7ce1e;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType7 {
  color: #86bf00;
  border-radius: 4px;
  border: 1px solid #86bf00;
  background: rgba(134, 191, 0, 0.1);

  .ant-tag-close-icon {
    color: #86bf00;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType8 {
  color: #fb8f83;
  border-radius: 4px;
  border: 1px solid #fb8f83;
  background: rgba(251, 143, 131, 0.1);

  .ant-tag-close-icon {
    color: #fb8f83;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType9 {
  color: #64635e;
  border-radius: 4px;
  border: 1px solid #64635e;
  background: rgba(100, 99, 94, 0.1);

  .ant-tag-close-icon {
    color: #64635e;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType10 {
  color: #9c9992;
  border-radius: 4px;
  border: 1px solid #9c9992;
  background: rgba(156, 153, 146, 0.1);

  .ant-tag-close-icon {
    color: #9c9992;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType11 {
  color: #fcb830;
  border-radius: 4px;
  border: 1px solid #fcb830;
  background: #fffbf4;

  .ant-tag-close-icon {
    color: #fcb830;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType12 {
  color: #ee6521;
  border-radius: 4px;
  border: 1px solid #ee6521;
  background: rgba(238, 101, 33, 0.1);

  .ant-tag-close-icon {
    color: #ee6521;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType13 {
  color: #fb83f5;
  border-radius: 4px;
  border: 1px solid #fb83f5;
  background: rgba(251, 131, 245, 0.1);

  .ant-tag-close-icon {
    color: #fb83f5;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType14 {
  color: white;
  border-radius: 4px;
  border: 1px solid #31c759;
  background: #31c759;

  .ant-tag-close-icon {
    color: white;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType15 {
  color: white;
  border-radius: 4px;
  border: 1px solid #97dc16;
  background: #97dc16;

  .ant-tag-close-icon {
    color: white;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType16 {
  color: white;
  border-radius: 4px;
  border: 1px solid #21b1e1;
  background: #21b1e1;

  .ant-tag-close-icon {
    color: white;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.colorType17 {
  color: white;
  border-radius: 4px;
  border: 1px solid #eb903b;
  background: #eb903b;

  .ant-tag-close-icon {
    color: white;
    float: right;
    margin-top: 5px;
    margin-left: 8px;
  }

  .tagText {
    color: #333;
    font-size: 12px;
  }
}

.tagOpenModal {
  top: 25%;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 18px !important;
    font-style: normal;
    font-weight: 700 !important;
    line-height: 150%;
    /* 27px */
  }

  .ant-modal-body {
    padding: 10px 40px 0px 20px;
    margin-bottom: 15px !important;

    .ant-form-item-label {
      width: 80px;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .ant-form-item-label > label {
      width: 80px;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: inline-block;
      margin-right: 3px;
      line-height: 30px;
    }

    .ant-select {
      background: #fff;
      font-size: 12px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      box-shadow: none;
      border-radius: 4px;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-checkbox + span {
      font-size: 12px;
      color: #333;
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 5px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

// 工作台-聊天内容-电话
.workTableChatPhone {
  .ant-popover-inner-content {
    padding: 0px;
  }

  .ant-popover-arrow {
    display: none;
  }

  .phonePopover {
    list-style: none;
    padding-left: 0px;

    li {
      width: 100%;
      height: 30px;
      padding: 0px 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    li:hover {
      background: #f3f4fe;
    }

    svg {
      width: 16px;
      height: 16px;
      float: left;
      margin-right: 4px;
    }
  }
}

.ticketDetailReplyDrawer {
  .aiOperation {
    width: 100%;
    height: 24px;
    position: relative;

    .draftContainer {
      height: 24px;
      border-radius: 2px;
      padding: 2px 4px;
      float: left;
    }

    .draftContainer:hover {
      background: #eee;
    }

    .draftText {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 21px;
      float: left;
    }

    .optimizeContainer,
    .elaborateContainer,
    .abbreviationContainer {
      float: left;
      margin-left: 10px;
      height: 20px;
      margin-top: 2px;
      cursor: pointer;

      span {
        display: none;
      }
    }

    .optimizeContainer:hover,
    .elaborateContainer:hover,
    .abbreviationContainer:hover {
      span {
        display: block;
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 21px;
        float: left;
        margin-left: 4px;
      }
    }

    .moreIconContainer {
      float: left;
      margin-left: 10px;
      height: 20px;
      margin-top: 1px;
      cursor: pointer;
    }

    .operationLine {
      width: 1px;
      height: 16px;
      background: #e6e6e6;
      float: left;
      margin-left: 10px;
      margin-top: 3px;
    }

    .emailTemplateIcon {
      float: left;
      margin-left: 10px;
      height: 20px;
      margin-top: 2px;
    }
  }

  .editorjs {
    max-width: 600px;
    width: 100%;
    max-height: 350px;
    min-height: 350px;
    overflow: hidden;
    overflow-y: scroll;
    margin-top: 10px;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    background: rgba(255, 255, 255, 0.5);
    padding: 2px 10px !important;
    margin-left: 7px;

    img {
      max-width: 250px;
    }

    .codex-editor__redactor {
      //margin-left: 55px;
      padding-bottom: 0px !important;
    }

    //.ce-toolbar__actions {
    //  left: 0px;
    //}
  }

  .attachmentContainer {
    .attachmentIcon {
      float: left;
      margin-top: 7px;
      margin-right: 4px;
    }

    .emailAddAttachmentIcon {
      width: 24px;
      height: 24px;
      float: left;
      cursor: pointer;
      margin-top: 3px;
    }
  }

  .ant-form-item-label
    > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
    display: none;
  }

  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-form-item-label > label {
    color: #333;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 32px;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #E6E6E6;
    background: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    height: 32px;
    box-shadow: none;
  }

  .ant-select {
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    height: 32px;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    box-shadow: none;
    border-radius: 6px !important;
  }

  .ant-space-item:first-child {
    //width: 83%;
  }

  .ant-select-multiple .ant-select-selection-item {
    border-radius: 4px;
    border: 1px solid rgba(52, 99, 252, 0.6);
    background: linear-gradient(
        0deg,
        rgba(52, 99, 252, 0.1) 0%,
        rgba(52, 99, 252, 0.1) 100%
      ),
      #fff;
    color: #3463fc;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 150% */
  }

  .ant-select-multiple .ant-select-selection-item-remove {
    color: #3463fc;
  }

  .defaultBtnContainer {
    width: 100%;
    //margin-top: 15px;
    //margin-bottom: 15px;
    text-align: center;

    .cancelBtn {
      width: 92px;
      height: 30px;
      padding: 2px 10px;
      font-size: 12px;
      color: #3463fc;
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid #3463fc;
      margin-right: 20px;
    }

    .sendBtn {
      color: #fff;
      font-size: 12px;
      border-radius: 4px;
      border: none;
      height: 30px;
      padding: 2px 24px;
      background: #3463fc;
      //width: 92px;
    }
  }
}

//工作台回复工单上传附件
.uploadListInline .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}

.uploadListInline [class*='-upload-list-rtl'] .ant-upload-list-item {
  float: right;
}

.uploadListInline {
  .ant-upload-list-item-card-actions.picture {
    .ant-btn {
      border: none;
    }
  }

  .ant-upload-list-item-name {
    font-size: 12px;
    color: #333;
    //text-align: right;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
    /* 116.667% */
  }

  .anticon {
    fill: #3463fc;
  }
}

// chat聊天语法纠错
.settingGrammerPopover {
  .ant-popover-inner-content {
    padding: 0px;
  }

  .ant-popover-arrow {
    display: none;
  }

  .grammerOperationList {
    list-style: none;
    padding-left: 0px;
    // width: 139px;
    border-radius: 4px;
    border: 1px solid #e6e6e6;

    /* 模块阴影 */
    box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);

    ul {
      padding: 0;
    }

    li {
      width: 100%;
      // height: 30px;
      padding: 4px 10px;
      cursor: pointer;
      display: flex;
      list-style: none;
    }

    li:hover {
      background: #f3f4fe;

      span:last-child {
        color: #3463fc !important;
      }
    }

    span:first-child {
      margin-top: 7px;
    }

    span:last-child {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 30px;
      margin-left: 10px;
    }
  }
}

.regionPopoverContainer {
  top: 45px !important;

  .ant-popover-inner {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(7px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.15);
    border-radius: 6px;
  }

  .ant-popover-inner-content {
    padding: 4px 0px;
  }

  ul {
    padding-left: 0px;
    margin-bottom: 0px;
  }

  ul li {
    list-style: none;
    padding: 8px 25px;
    text-align: center;

    a {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 14px;
      /* 87.5% */
    }
  }

  ul li:hover {
    background: #3463fc;

    a {
      color: #fff;
    }
  }

  .itemSelect {
    background: #3463fc;

    a {
      color: #fff;
    }
  }
}

.ant-popover-hidden {
  //display: block;
}

//新版编译器样式
.ce-popover--inline .ce-popover-item__icon svg,
.ce-inline-tool svg {
  width: 26px;
  height: 26px;
}

.ant-menu-item a {
  text-decoration: none !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

.ant-menu-item:hover a {
  text-decoration: none !important;
  color: #3463fc !important;
}

.ant-tabs-tab-active .ant-menu-item a {
  color: #3463fc !important;
}

.ruleStyle {
  .ant-table-thead > tr > th {
    font-weight: bold !important; // 给表头字体加粗
  }
}

//设置windows table样式错误
.ant-table-container table > thead > tr:first-child th:last-child {
  right: 0px !important;
}

//删除意图弹窗
.deleteIntentionModal {
  top: 25%;

  p {
    img {
      width: 20px;
      float: left;
      margin-right: 8px;
      margin-bottom: 20px;
    }

    margin-bottom: 0px;
  }

  .ant-modal-body {
    padding: 0px 24px;
  }

  .ant-input {
    font-size: 12px;
    margin-top: 20px;
    border-radius: 4px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    box-shadow: none;
  }

  .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding-bottom: 20px;
  }

  .ant-btn {
    font-size: 12px;
  }
}

//删除智能体
.deleteExternalModal {
  top: 25%;

  p {
    img {
      width: 20px;
      float: left;
      margin-right: 8px;
      margin-bottom: 20px;
    }

    margin-bottom: 0px;
  }

  .ant-modal-body {
    padding: 0px 24px;
  }

  .ant-input {
    font-size: 12px;
    margin-top: 20px;
    border-radius: 4px;
    //border: 1px solid #e6e6e6;
    background: #fff;
    box-shadow: none;
  }

  .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding-bottom: 20px;
  }

  .ant-btn {
    font-size: 12px;
  }
}

//创建意图成功弹窗
.intentionSuccessModal {
  top: 30%;

  .ant-modal-content {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 18px !important;
    font-style: normal;
    font-weight: 700 !important;
    line-height: 150%;
    /* 27px */
  }

  .ant-modal-body {
    padding: 10px;
    text-align: center;

    img {
      width: 250px;
    }

    p {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      /* 18px */
      margin-top: 10px;
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 10px;
    height: 75px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

//意图分类管理弹窗
.intentionClassificationModal {
  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-body {
    padding: 0px 24px 24px 24px;
  }

  .ant-modal-content {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);

    .operationContainer {
      width: 100%;
      height: 32px;
      margin-top: 10px;
      margin-bottom: 10px;

      img {
        width: 32px;
        margin-left: 2%;
        cursor: pointer;
      }

      .ant-input-affix-wrapper {
        font-size: 12px;
        //margin-top: 20px;
        border-radius: 4px;
        //border: 1px solid #e6e6e6;
        background: #fff;
        box-shadow: none;
        height: 32px;
        width: 88%;
        float: left;

        .ant-input {
          height: 24px;
          line-height: 24px;
        }
      }
    }

    .intentionClassificationList {
      max-height: 200px;
      overflow: hidden;
      overflow-y: scroll;
    }

    .intentionClassificationItem {
      height: 50px;
      width: 100%;
      border-top: 1px solid #e6e6e6;

      p {
        width: 88%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 50px;
        /* 125% */
        margin-bottom: 0px;
        float: left;
        margin-left: 1%;
      }

      img {
        float: left;
        margin-left: 2%;
        width: 16px;
        cursor: pointer;
        margin-top: 15px;
      }
    }

    .noDataContent1 {
      width: 100%;
      height: 180px;
      display: grid;
      justify-content: center;
      align-content: center;

      img {
        width: 40%;
        margin-left: 30%;
      }

      p {
        color: #999;
        margin-top: 20px;
        margin-bottom: 20px;
        text-align: center;
      }
    }
  }

  .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding-bottom: 20px;
  }

  .ant-btn {
    font-size: 12px;
  }
}

//全局提示层级
.ant-notification {
  z-index: 1032;
}

//按钮统一样式--鼠标移入
.ant-btn {
  border-radius: 4px;
}

.ant-btn:hover {
  border-radius: 4px;
  border: 1px solid #4873fc;
  background: #f9f9f9;
}

.ant-btn.ant-btn-primary:hover {
  background: #4873fc;
}

//添加知识库管理员
.addKnowledgeRoleModal {
  top: 20%;
  width: 50% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(20px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  //.ant-modal-close {
  //  display: none;
  //}

  .ant-modal-title {
    color: #333;
    font-size: 18px !important;
    font-style: normal;
    font-weight: 700 !important;
    line-height: 150%;
    /* 27px */
  }

  .ant-modal-body {
    min-height: 400px;
    padding: 20px;
    margin-bottom: 15px !important;

    .ant-transfer-list {
      width: 48%;
      height: 400px;
      border-radius: 4px;
      border: 1px solid #e6e6e6;

      .ant-transfer-list-header {
        display: inline-block;
        border: none;
      }

      .ant-transfer-list-header-title {
        text-align: left;
        float: left;
      }

      .ant-transfer-list-content-item {
        margin: 0px 12px;
        border-radius: 10px;
      }

      .ant-transfer-list-content-item:hover {
        background: #ebefff;
      }

      .ant-transfer-list-content-item-checked {
        border-radius: 10px;
        background: #ebefff;
      }

      .anticon.anticon-delete svg {
        width: 20px;
        height: 20px;
      }

      .ant-transfer-list-content-item-remove:hover {
        color: #ee3b31;
      }

      .itemList {
        width: 100%;
        height: 60px;
        display: flex;
        align-items: center;
        cursor: pointer;
        overflow: hidden;

        //border-bottom: 1px solid #E6E6E6;
        .contentItemImg {
          position: relative;
          margin-right: 5px;
          float: left;
          display: inline-block;

          .headAva {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 18px;
            font-weight: 900;
            margin: 5px 0;
            background: linear-gradient(180deg, #7394ff 0%, #3463fc 100%);
          }
        }

        .contentItemContainer {
          width: calc(100% - 40px);
          float: right;
          display: inline-block;
          height: 60px;
          position: relative;

          .customerName {
            color: #333;
            font-size: 12px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 80%;
            margin-bottom: 10px;
            margin-top: 10px;
            margin-left: 10px;
          }

          .groupName {
            width: 80%;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%;
            /* 18px */
            margin-bottom: 0px;
            margin-left: 10px;
          }

          img {
            display: none;
          }
        }

        .contentItemContainer:hover {
          img {
            display: block;
            position: absolute;
            right: 3px;
            top: 20px;
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    .ant-transfer-list:first-child {
      .ant-transfer-list-header {
        display: none;
      }

      .ant-transfer-list-content-item {
        border-bottom: 1px solid #e6e6e6;
      }
    }

    .ant-transfer-list:last-child {
      .ant-transfer-list-body-search-wrapper {
        display: none;
      }
    }

    .ant-input-affix-wrapper {
      border-radius: 4px;
      background: #fff;
      box-shadow: none;
      height: 32px;

      .ant-input {
        height: 24px;
        font-size: 12px;
      }
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 5px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

//客户资料--客户标签弹窗
.customerTagModal {
  top: 25% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-btn {
    margin-right: 12px;
  }

  //.ant-modal-close {
  //  display: none;
  //}

  .ant-modal-body {
    padding: 10px 20px 30px 20px;
    height: 300px;
  }

  .ant-form-item-label > label {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    float: left;
    line-height: 32px;
    justify-content: end;
    margin-right: 10px;
    min-width: 80px;
    text-align: right;
  }

  .ant-input {
    border-radius: 6px;
    //border: 1px solid #e6e6e6;
    font-size: 12px;
    box-shadow: none;
  }
}

//热线关键指标看板--弹窗
.hotlineKeyIndicatorsConfigModal {
  top: 30% !important;
  left: 5% !important;

  .ant-modal-content {
    //background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    //box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    //border-radius: 6px;
    padding-bottom: 5px;

    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    //backdrop-filter: blur(5px);
  }

  .ant-btn {
    margin-right: 12px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-body {
    padding: 10px 20px 20px 20px;
    //height: 300px;
  }

  .ant-form-item-label > label {
    color: #333;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    float: left;
    line-height: 32px;
    justify-content: end;
    margin-right: 10px;
    //min-width: 80px;
    text-align: left;
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    font-size: 12px;
    border-radius: 4px;
    //border: 1px solid #E6EBF2;
    background: rgba(255, 255, 255, 0.6);
    box-shadow: none;
    max-height: 150px;
    overflow: hidden;
    overflow-y: scroll;
  }

  .ant-form-item-label
    > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before,
  .ant-form-item-label > label::after {
    display: none;
  }

  .ant-select-multiple .ant-select-selection-item {
    border-radius: 4px;
    border: 1px solid #3463fc;
    background: #dfe7f7;
    color: #3463fc;
  }

  .anticon {
    color: #3463fc;
  }

  p {
    font-size: 12px;

    span {
      font-size: 12px;
    }
  }
}

//添加座席状态
.AddAgentStatusModal {
  top: 25% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 20px;
  }

  .ant-modal-body {
    min-height: 200px;
    padding: 20px;
    margin-bottom: 15px !important;

    .ant-form-item .ant-form-item-label {
      color: #333;
      text-align: right;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      /* 18px */
      width: 90px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-input {
      border-radius: 4px;
      background: #fff;
      font-size: 12px;
      color: #333;
      box-shadow: none;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 4px;
      background: #fff;
      font-size: 12px;
      color: #333;
      box-shadow: none;
    }

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
      margin-top: 30px;
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 5px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

//智能翻译弹窗
.IntelligentTranslationModal {
  top: 25% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 0px;
  }

  .ant-modal-body {
    min-height: 100px;
    padding: 20px;

    .ant-form-item .ant-form-item-label {
      color: #333;
      text-align: right;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      /* 18px */
      width: 60px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-input {
      border-radius: 4px;
      background: #fff;
      font-size: 12px;
      color: #333;
      box-shadow: none;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 4px;
      background: #fff;
      font-size: 12px;
      color: #333;
      box-shadow: none;
    }

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
      margin-top: 30px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 6px;
      max-height: 200px;
      overflow: hidden;
      overflow-y: scroll;
    }

    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
    }

    .ant-select-multiple .ant-select-selection-item-content {
      color: #3463fc;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }

    .ant-select-multiple .ant-select-selection-item-remove {
      color: #3463fc;
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 5px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

//问答知识库批量导入弹窗
.batchImportQAModal {
  height: 600px !important;
  //width: 650px !important;

  .ant-modal-content {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    /* BG模糊 */
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .ant-modal-title {
    font-weight: 400;
    color: #333333;
  }

  .ant-modal-header {
    border-bottom: none;
    background: none;
  }

  .ant-modal-body {
    height: 390px;
    padding-top: 0px;

    label {
      margin-bottom: 5px;
      font-size: 12px;
      line-height: 28px;
    }

    .ant-select {
      width: 100%;
      margin-bottom: 20px;
      font-size: 12px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 6px;
      height: 32px;
      overflow: hidden;
      overflow-y: scroll;
    }

    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
    }

    .ant-select-multiple .ant-select-selection-item-content {
      color: #3463fc;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }

    .ant-select-multiple .ant-select-selection-item-remove {
      color: #3463fc;
    }

    .ant-tag:first-child {
      margin-left: 0px;
      font-size: 12px;
    }

    .tagText {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
    }
  }

  .downloadContent,
  .importContent {
    width: 100%;
    height: 240px;
    float: left;
    text-align: left;

    p {
      color: #666;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }

    img {
      width: 118px;
      float: left;
      margin-left: 23px;
      margin-top: 45px;
    }

    .fileDescription {
      color: #999;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    .uploadDiv {
      width: 100%;
      border-radius: 4px;
      border: 1px dashed var(--neutral-color-border-base, #dcdfe6);
      background: var(--function-component, #fff);
      height: 230px;
      padding: 10px 25px;

      span {
        font-size: 12px;
      }

      b {
        color: #3463fc;
        cursor: pointer;
      }
    }

    .ant-upload {
      width: 100%;
    }

    .uploadTips {
      a {
        color: #999 !important;

        b {
          color: #3463fc;
        }
      }
    }
  }

  .detailDownload {
    width: 60%;
    height: 40px;
    float: left;
    margin-top: 35px;
    margin-left: 3%;

    p {
      color: #999;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      /* 150% */
      width: 100%;
      float: left;
    }

    img {
      width: 16px;
      float: left;
      margin-left: 0%;
      margin-right: 3px;
      margin-top: 1px;
    }

    span {
      color: #666;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      float: left;
      line-height: 24px;
    }

    a {
      font-size: 12px;
      //margin-left: 10px;
      float: left;
    }
  }

  .downloadBtn {
    border-radius: 4px;
    border: 1px solid #3463fc;
    color: #3463fc;
    font-size: 14px;
    float: left;
    margin-left: 8%;
    margin-top: 8px;
  }

  .ant-upload-list-item-name {
    color: #666;
    font-size: 12px;
  }

  .ant-upload-list-item-card-actions-btn.ant-btn-sm.ant-btn:hover,
  .ant-upload-list-item-card-actions-btn.ant-btn-sm.ant-btn {
    border: none;
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    padding: 0px 0px 24px 0px;
  }
}

//新增SLA规则弹窗
.AddSlaRuleModal {
  width: 60% !important;
  top: 15% !important;
  left: 7% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 0px;
  }

  .ant-modal-body {
    min-height: 100px;
    padding: 20px;

    .ant-form-item .ant-form-item-label {
      text-align: right;
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      /* 183.333% */
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-input {
      border-radius: 4px;
      background: #fff;
      font-size: 12px;
      color: #333;
      box-shadow: none;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 4px;
      background: #fff;
      font-size: 12px;
      color: #333;
      box-shadow: none;
    }

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
      margin-top: 30px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 6px;
      max-height: 32px;
      overflow: hidden;
      overflow-y: scroll;
    }

    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
    }

    .ant-select-multiple .ant-select-selection-item-content {
      color: #3463fc;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }

    .ant-select-multiple .ant-select-selection-item-remove {
      color: #3463fc;
    }

    .ant-form {
      font-size: 12px;
    }

    .secondTitle {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 150%;
      /* 18px */
      margin-top: 10px;
      margin-bottom: 10px;
      height: 25px;

      .ant-col {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .ant-select-multiple .ant-select-selection-item-content {
      img {
        width: 12px;
        float: left;
        margin-right: 3px;
        margin-top: 5px;
      }
    }

    .ant-input-number {
      border-radius: 6px;
      font-size: 12px;
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 5px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

//问答知识库标签气泡窗
.knowledgeQAPopover {
  max-width: 23%;

  .ant-popover-inner {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(7px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.15);
    border-radius: 6px;
  }

  .ant-tag:first-child {
    margin-left: 0px;
  }

  .ant-popover-inner-content {
    padding: 4px 8px;
  }

  .ant-tag {
    margin-bottom: 5px;
    font-size: 12px;
  }
}

//批量删除问答知识库问题
.batchDeleteQAModal {
  top: 25% !important;

  .ant-modal-content {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    /* BG模糊 */
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .ant-modal-title {
    font-weight: 400;
    color: #333333;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-header {
    border-bottom: none;
    background: none;
  }

  .ant-modal-body {
    padding-top: 0px;

    p {
      overflow: hidden;
      color: #333;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 183.333% */
      margin-top: 15px;
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    padding: 0px 0px 24px 0px;
  }
}

// 文档知识库重新上传
.uploadModal {
  width: 50% !important;
  top: 25% !important;
  left: 5% !important;

  .ant-modal-content {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    /* BG模糊 */
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .ant-modal-title {
    font-weight: 400;
    color: #333333;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-header {
    border-bottom: none;
    background: none;
  }

  .ant-modal-body {
    padding-top: 0px;

    p {
      overflow: hidden;
      color: #333;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 183.333% */
      margin-top: 15px;
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    padding: 0px 0px 24px 0px;
  }
}

// 文档知识库重新上传
.uploadModal {
  width: 50% !important;
  top: 25% !important;
  left: 5% !important;

  .ant-modal-content {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    /* BG模糊 */
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .ant-modal-title {
    font-weight: 400;
    color: #333333;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-header {
    border-bottom: none;
    background: none;
  }

  .ant-modal-body {
    padding-top: 0px;

    p {
      overflow: hidden;
      color: #333;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 183.333% */
      margin-top: 15px;
    }

    .selectContainer {
      width: 100%;
      border-radius: 10px;
      background: #f5f3fe;
      padding: 12px;
      margin-bottom: 20px;

      .tipsText {
        color: #666;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }

      .selectItem {
        width: 100%;
        margin-bottom: 10px;

        .labelText {
          color: #333;
          font-size: 12px;
          font-style: normal;
          font-weight: 700;
          line-height: 32px;
        }

        .ant-select {
          width: 90%;
          font-size: 12px;
        }

        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 6px;
          background: #fff;
          box-shadow: none;
          font-size: 12px;
          color: #333;
          max-height: 100px;
          overflow: hidden;
          overflow-y: scroll;
        }

        .ant-picker {
          width: 90%;
          height: 32px;
          border-radius: 6px;
          background: #fff;
          box-shadow: none;
          font-size: 12px;
          color: #333;
        }

        .ant-picker-input > input {
          font-size: 12px;
        }
      }

      .ant-btn[disabled],
      .ant-btn[disabled]:hover,
      .ant-btn[disabled]:focus,
      .ant-btn[disabled]:active {
        border-radius: 4px;
        border: 1px solid #3463fc;
        background: #fff;
        color: #3463fc;
      }

      .ant-btn a {
        color: #3463fc;
      }

      .ant-tag {
        font-size: 12px;
      }
    }

    .uploadContent {
      .ant-upload.ant-upload-drag {
        padding: 20px 10%;
      }

      img {
        width: 64px;
        float: left;
        margin-right: 10px;
      }

      p {
        font-size: 12px;
        color: #999;
        line-height: 22px;
        text-align: left;

        b {
          color: #3463fc;
        }
      }
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    padding: 0px 0px 24px 0px;
  }
}

//智能填单工单类型气泡窗
.ticketTypePopover {
  .ant-tag {
    font-size: 12px;
    color: #3463fc;
    border-radius: 4px;
    border: 1px solid rgba(52, 99, 252, 0.5);
    background: linear-gradient(
        0deg,
        rgba(52, 99, 252, 0.1) 0%,
        rgba(52, 99, 252, 0.1) 100%
      ),
      #fff;
  }

  .ant-tag:first-child {
    margin-left: 0px;
  }
}

//智能填单属性名气泡窗
.attributeNamePopover {
  .ant-tag {
    font-size: 12px;
    color: #fcb830;
    border-radius: 4px;
    border: 1px solid rgba(252, 184, 48, 0.5);
    background: linear-gradient(
        0deg,
        rgba(252, 184, 48, 0.1) 0%,
        rgba(252, 184, 48, 0.1) 100%
      ),
      #fff;
  }

  .ant-tag:first-child {
    margin-left: 0px;
  }
}

//不活跃消息时提醒--新增回复弹窗
.addReplyModal {
  width: 40% !important;
  top: 25% !important;

  .ant-modal-content {
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    border-radius: 6px;
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-close {
    display: none;
  }

  .ant-modal-title {
    color: #333;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 0px;
  }

  .ant-modal-body {
    min-height: 100px;
    padding: 20px;

    .ant-form-item .ant-form-item-label {
      text-align: left;
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 183.333% */
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-input {
      border-radius: 4px;
      //background: #fff;
      font-size: 12px;
      //color: #333;
      box-shadow: none;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 4px;
      //background: #fff;
      font-size: 12px;
      //color: #333;
      box-shadow: none;
    }

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
      margin-top: 30px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 6px;
      //max-height: 32px;
      //overflow: hidden;
      //overflow-y: scroll;
    }

    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
    }

    .ant-select-multiple .ant-select-selection-item-content {
      color: #3463fc;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }

    .ant-select-multiple .ant-select-selection-item-remove {
      color: #3463fc;
    }

    .ant-form {
      font-size: 12px;
    }

    .ant-input-number {
      border-radius: 6px;
      font-size: 12px;
    }

    .ant-col-6 {
      .ant-input-number {
        width: 100%;
        box-shadow: none;
      }

      .ant-input-number-handler-up {
        border-top-right-radius: 4px;
      }

      .ant-input-number-handler-down {
        border-bottom-right-radius: 4px;
      }

      .ant-input-number-handler-wrap {
        border-radius: 0px 4px 4px 0px;
      }
    }

    .ant-col-4 {
      padding-left: 0px !important;

      .ant-form-item-required {
        display: none;
      }

      .ant-form-item-label {
        height: 30px;
      }
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    font-size: 12px;
    margin-bottom: 5px;

    .ant-btn {
      margin-right: 12px;
      font-size: 12px;
    }
  }
}

//智能填单工单类型气泡窗
.ticketTypePopover {
  .ant-tag {
    font-size: 12px;
    color: #3463fc;
    border-radius: 4px;
    border: 1px solid rgba(52, 99, 252, 0.5);
    background: linear-gradient(
        0deg,
        rgba(52, 99, 252, 0.1) 0%,
        rgba(52, 99, 252, 0.1) 100%
      ),
      #fff;
  }

  .ant-tag:first-child {
    margin-left: 0px;
  }
}

//智能填单属性名气泡窗
.attributeNamePopover {
  .ant-tag {
    font-size: 12px;
    color: #fcb830;
    border-radius: 4px;
    border: 1px solid rgba(252, 184, 48, 0.5);
    background: linear-gradient(
        0deg,
        rgba(252, 184, 48, 0.1) 0%,
        rgba(252, 184, 48, 0.1) 100%
      ),
      #fff;
  }

  .ant-tag:first-child {
    margin-left: 0px;
  }
}

//工单详情气泡框
.ticketDetailPopover {
  z-index: 9999999;

  .ant-popover-inner-content {
    height: 280px;
    margin-bottom: 15px;
    overflow: hidden;
    overflow-y: scroll;
    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
  }

  .ant-popover-inner-content::-webkit-scrollbar {
    display: none;
    /* Chrome Safari */
  }
}

.ticketDetailPopover.ant-popover-hidden {
  //display: block;
}

#advancedEditableContainer {
  position: relative;
  width: 100%;

  .ant-modal-mask {
    background-color: transparent;
  }

  .advancedEditableModal {
    position: absolute;
    top: -260px;

    .advancedEditableModalConfirm {
      position: absolute;
      width: fit-content;
      left: -100px;

      .ant-modal-confirm-body-wrapper {
        flex-direction: column;
      }

      .ant-modal-body {
        padding: 16px 10px;
      }

      .ant-modal-content {
        border-radius: 5px;
      }

      .ant-modal-confirm-body > .anticon {
        font-size: 14px;
        margin-right: 8px;
      }

      .ant-modal-confirm-body {
        align-items: center;
      }

      .ant-modal-confirm-content {
        font-size: 12px;
        margin-top: 0 !important;
      }

      .ant-modal-confirm-btns {
        margin-top: 14px;
      }

      .ant-modal-confirm-btns {
        .ant-btn {
          padding: 0 4px;
          height: auto;

          span {
            font-size: 10px;
          }
        }
      }

      .ant-modal-arrow {
        position: absolute;
        display: block;
        width: 22px;
        height: 22px;
        overflow: hidden;
        background: transparent;
        pointer-events: none;
        bottom: 0;
        left: 50%;
        transform: translateY(100%) translateX(-50%);

        .ant-modal-arrow-content {
          box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
          transform: translateY(-11px) rotate(45deg);
          position: absolute;
          background-color: #fff;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          display: block;
          width: 11.3137085px;
          height: 11.3137085px;
          margin: auto;
          content: '';
          pointer-events: auto;
          border-radius: 0 0 2px;
          pointer-events: none;

          &:before {
            position: absolute;
            top: -11.3137085px;
            left: -11.3137085px;
            width: 33.9411255px;
            height: 33.9411255px;
            background: #fff;
            background-repeat: no-repeat;
            background-position: -10px -10px;
            content: '';
            -webkit-clip-path: inset(33% 33%);
            clip-path: inset(33% 33%);
            -webkit-clip-path: path(
              'M 9.849242404917499 24.091883092036785 A 5 5 0 0 1 13.384776310850237 22.627416997969522 L 20.627416997969522 22.627416997969522 A 2 2 0 0 0 22.627416997969522 20.627416997969522 L 22.627416997969522 13.384776310850237 A 5 5 0 0 1 24.091883092036785 9.849242404917499 L 23.091883092036785 9.849242404917499 L 9.849242404917499 23.091883092036785 Z'
            );
            clip-path: path(
              'M 9.849242404917499 24.091883092036785 A 5 5 0 0 1 13.384776310850237 22.627416997969522 L 20.627416997969522 22.627416997969522 A 2 2 0 0 0 22.627416997969522 20.627416997969522 L 22.627416997969522 13.384776310850237 A 5 5 0 0 1 24.091883092036785 9.849242404917499 L 23.091883092036785 9.849242404917499 L 9.849242404917499 23.091883092036785 Z'
            );
          }
        }
      }
    }
  }
}

//智能质检得分详情
.intelligentQualityInspectionScoreDetails {
  .ant-modal-content {
    border-radius: 10px;
    background: linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0.2) 100%
      ),
      linear-gradient(70deg, #f4d9ff 0%, #e7f9dc 100%);
    backdrop-filter: blur(5px);
    //box-shadow: 2px 8px 25px -3px rgba(5, 15, 50, 0.4);
    padding-bottom: 5px;
  }

  .ant-modal-header {
    background: none;
    padding-bottom: 0px;
  }

  .ant-modal-close {
    color: rgba(153, 153, 153, 1);
  }

  .ant-modal-title {
    color: #333;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 0px;
  }

  .detailScopeContainer {
    width: 100%;

    .headerContent {
      width: 100%;
      height: 30px;

      .leftItem {
        width: 84%;
        float: left;
        text-align: left;
        margin-right: 1%;
        padding-left: 20px;
      }

      .rightItem {
        width: 15%;
        float: left;
        text-align: left;
      }
    }

    .detailContent {
      width: 100%;
      height: 180px;
      padding: 20px;
      overflow: hidden;
      overflow-y: scroll;
      /* 隐藏滚动条 */
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;
      /* IE 10+ */
      font-family: 'Poppins', sans-serif !important;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 6px;

      .detailItem {
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .leftItem {
          width: 84%;
          float: left;
          text-align: left;
          margin-right: 1%;

          p {
            color: #666;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            margin-bottom: 0px;
          }
        }

        .rightItem {
          width: 15%;
          float: left;
          text-align: left;

          .operatorText {
            color: #3463fc;
            font-size: 12px;
            font-style: normal;
            font-weight: 700;
            margin: 0px 2px;
          }

          .scopeText {
            color: #333;
            font-size: 12px;
            font-style: normal;
            font-weight: 700;
          }
        }
      }
    }

    .detailContent::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }

    .totalScopeContent {
      width: 100%;
      height: 30px;
      margin-top: 20px;
      text-align: right;
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 30px;

      span {
        color: #30cf40;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        float: right;
      }
    }

    .footerBtn {
      width: 100%;
      height: 32px;
      margin-top: 20px;
      text-align: center;

      .ant-btn.ant-btn-primary {
        padding-left: 24px;
        padding-right: 24px;
      }
    }
  }
}

//工作台智能填单选择语言弹窗
.selectIntelligentFormFillingLanguageModal {
  top: 50% !important;
  left: 3.5% !important;

  .ant-modal-content {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.8);
    /* BG模糊 */
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
  }

  .ant-modal-title {
    font-weight: 400;
    color: #333333;
  }

  .ant-modal-header {
    border-bottom: none;
    background: none;
  }

  .ant-modal-body {
    padding-top: 0px;

    .ant-select {
      font-size: 12px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.5);
    }
  }

  .ant-modal-footer {
    border-top: none;
    text-align: center;
    padding: 0px 0px 24px 0px;
  }
}
