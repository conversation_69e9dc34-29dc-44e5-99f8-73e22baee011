import request from '@/utils/request';

/**
 * 上传转人工等待音乐文件
 * @param payload
 * @returns {Promise<*>}
 */
export async function uploadTransferMusicFile(payload) {
  return request('api/crm/system/company/config/uploadTransferMusicFile', {
    method: 'post',
    data: payload,
  });
}
/**
 * 新增/更新 转人工等待音乐配置
 * @param payload
 * @returns {Promise<*>}
 */
export async function saveTransferMusicConfig(payload) {
  return request('api/crm/system/company/config/saveTransferMusicConfig', {
    method: 'POST',
    data: payload,
  });
}
/**
 * 查询转人工等待音乐配置
 * @returns {Promise<*>}
 */
export async function queryTransferMusicConfig() {
  return request('api/crm/system/company/config/queryTransferMusicConfig', {
    method: 'GET',
  });
}
