import request from '@/utils/request';
//拿到twilio的token
export async function generateAgentToken(payload) {
  return request('api/crm/voice/twilio/generateAgentToken', {
    method: 'GET',
  });
}
//挂断
export async function hangupFromAgent(payload) {
  return request(
    `api/crm/voice/twilio/hangupFromAgent?callSid=${payload.callSid}`,
    {
      method: 'POST',
    },
  );
}
/**
 * 电话录音分片上传
 * @param {{
"audioBuffer": [], //录音的buffer流
"agentId": "", //坐席id
"contactId": "", //callSid
"ticketLanguage": "", //转录语言
"workRecordId": "", //工单id
"end": true //是否最后一次
}}
 * @returns
 */
export async function twilioChunkUpload(payload) {
  return request(`api/crm/file/s3/chunk`, {
    method: 'POST',
    data: payload,
  });
}

//获取是否acw状态
export async function getAcwStateTwilio(payload) {
  return request(
    `api/crm/voice/twilio/event/acw/state?companyId=${payload.companyId}&agentAccountId=${payload.agentAccountId}`,
    {
      method: 'GET',
    },
  );
}
/**
 *
 * @param {{
  "companyId": "", //公司ID
"agentAccountId": "", //坐席ID
"agentAccount": "", //坐席name
"agentGroupId": "", //座席组ID
"agentGroup": "", //座席组name
"connectAlias": "", //实例别名（联络线路）
"incomingOutgoing": "", //呼入/呼出
"eventSystemTimestamp": "2025-07-15 16:00:33", //事件发生的系统时间
"contactId": "", //联络ID,可能为空
"twilioAccountId": "", //twilio 账号ID，可能为空
"customerPhone": "", //客户电话
"systemPhone": "", //系统电话
"eventName": "", //事件名
"eventCode": "", //事件code
"workOrderId": "", //工单id,可能为空
"workOrderNumber": "", //工单编号，可能为空
"createTime": "2025-07-15 16:00:33" //记录的创建时间，不是事件发生时间
"haveACWFlag":boolean, 是否配置了ACW 在挂断事件传递过来
}} payload
 * @returns
 */
//记录电话中事件
export async function saveEventDetailsTwilio(payload) {
  return request(`api/crm/voice/twilio/event/save/details`, {
    method: 'POST',
    data: payload,
  });
}
//拿机器人转录结果
export async function getVoiceAnalysisToRobotByTwilio(payload) {
  return request(
    `api/crm/call/workRecord/voiceAnalysisToRobotByTwilio?contactId=${payload.contactId}&workRecordId=${payload.workRecordId}`,
    {
      method: 'GET',
    },
  );
}

//查询当前坐席的线路列表
export async function queryCurrentAgentLinkList() {
  return request(`api/crm/channel/supplier/queryCurrentAgentLinkList`, {
    method: 'GET',
  });
}

//获取通话信息
export async function getCallInfo(payload) {
  return request(
    `api/crm/voice/twilio/getCallInfo?callSid=${payload.callSid}`,
    {
      method: 'POST',
    },
  );
}

//坐席接听
export async function acceptByAgentClient(payload) {
  return request(
    `api/crm/voice/twilio/acceptByAgentClient?callSid=${payload.callSid}&agentId=${payload.agentId}`,
    {
      method: 'POST',
    },
  );
}
