import request from '@/utils/request';

/** 智能体保存 */
export async function AiAgentSave(payload) {
  return request('api/crm/aiagent/aiagent/deploy/save', {
    method: 'POST',
    data: payload,
  });
}

/** 智能体保存并部署*/
export async function AiAgentSaveAndDeploy(payload) {
  return request(`api/crm/aiagent/aiagent/deploy/saveAndDeploy`, {
    method: 'post',
    data: payload,
  });
}

/** 查询智能体，部署保存的信息 */
export async function getAiAgentDeployList(payload) {
  return request(
    `api/crm/aiagent/aiagent/deploy/getDeployInfo?aiAgentId=${payload.aiAgentId}
  `,
    {
      method: 'get',
    },
  );
}
/** 查询智能体详情 */
export async function getAiAgentDetail(payload) {
  return request(
    `api/crm/aiagent/details?aiAgentId=${payload.aiAgentId}
  `,
    {
      method: 'get',
    },
  );
}
/** 智能体版本部署 */
export async function AiAgentDeploy(payload) {
  return request(`api/crm/aiagent/aiagent/deploy/deploy/${payload.aiAgentId}`, {
    method: 'post',
  });
}
/** 查询智能体，部署保存的详情 */
export async function AiAgentInfo(payload) {
  return request(
    `api/crm/aiagent/aiagent/deploy/getDeployDetails?deployId=${payload.deployId}
  `,
    {
      method: 'post',
    },
  );
}

/** 查询智能体组件库列表 */
export async function getAiAgentComList(payload) {
  return request(`api/crm/aiagent/aiagent/flow/componentList/${payload.lang}`, {
    method: 'get',
  });
}
/** 查询当前智能体变量（传入智能体ID) */
export async function queryCurrentVar(payload) {
  return request(`api/crm/aiagent/variable/get/${payload.id}`, {
    method: 'post',
    data: { variableName: payload.variableName },
  });
}
/** 查询当前智能体变量分组（传入智能体ID) */
export async function queryGroupVariables(payload) {
  return request(`api/crm/aiagent/variable/groupVariables`, {
    method: 'get',
  });
}

/** 删除当前智能体变量（传入智能体ID) */
export async function deteleCurrentVar(payload) {
  return request(`api/crm/aiagent/variable/delete/${payload.id}`, {
    method: 'post',
  });
}
/** 修改当前智能体变量（传入智能体ID) */
export async function updateCurrentVar(payload) {
  return request(`api/crm/aiagent/variable/update/${payload.id}`, {
    method: 'post',
    data: payload.data,
  });
}
/** 添加当前智能体变量（传入智能体ID) */
export async function addCurrentVar(payload) {
  return request(`api/crm/aiagent/variable/add`, {
    method: 'post',
    data: payload,
  });
}

/** 查询意图提醒设置列表*/
export async function queryReminderList(payload) {
  return request(`api/crm/aiagent/reminder/list`, {
    method: 'POST',
    data: payload,
  });
}
/** 查询所有意图*/
export async function getIntentGrouped() {
  return request(`api/crm/aiagent/intent/grouped`, {
    method: 'GET',
  });
}
/** 新增/修改意图提醒设置*/
export async function saveReminderList(payload) {
  return request(`api/crm/aiagent/reminder/save`, {
    method: 'POST',
    data: payload,
  });
}
/** 删除意图提醒设置*/
export async function deleteReminderList(payload) {
  return request(`api/crm/aiagent/reminder/delete/${payload}`, {
    method: 'POST',
    data: payload,
  });
}
/** 修改智能体名称*/
export async function updateAiAgentName(payload) {
  return request(`api/crm/aiagent/updateName/${payload.aiAgentId}`, {
    method: 'POST',
    data: { aiAgentName: payload.aiAgentName },
  });
}
/** 修改智能体渠道*/
export async function updateAiAgentChannel(payload) {
  return request(`api/crm/aiagent/updateChannel/${payload.aiAgentId}`, {
    method: 'POST',
    data: { channelIds: payload.channelIds },
  });
}

/** 获取api列表 */
export async function getApiList(payload) {
  return request(`api/crm/aiagent/apiOperation/list`, {
    method: 'POST',
    data: payload,
  });
}
//获取客户属性列表
export async function getCustomerAttributeList() {
  return request(`api/crm/customer/customerinfo/queryCustomerAttributeList`, {
    method: 'GET',
  });
}
//获取工单属性列表
export async function getTicketAttributeList() {
  return request(`api/crm/call/workRecord/ticketExtOption`, {
    method: 'GET',
  });
}
//获取热点翻译
export async function getHotTranslationList(payload) {
  return request(`api/crm/aiagent/aiagent/flow/hotIssueTranslate`, {
    method: 'POST',
    data: payload,
  });
}

// 智能体分享码 /crm/aiagent/shareCodeInfo
export async function getShareCode(payload) {
  return request(
    `api/crm/aiagent/shareCodeInfo/?aiAgentId=${payload.aiAgentId}`,
    {
      method: 'GET',
    },
  );
}

// 根据分享码获取内容
export async function getShareCodeContent(payload) {
  return request(`api/crm/aiagent/content/${payload.shareCode}`, {
    method: 'GET',
  });
}
