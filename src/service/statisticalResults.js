import request from '@/utils/request';

/**
 * 查询活动名称
 */
export async function queryActivityList(payload) {
  return request('api/crm/market/statistics/queryActivityList', {
    method: 'GET',
    params: { excludeEndStatus: payload.excludeEndStatus },
  });
}

/** 查询渠道类型*/
export async function channelType(payload) {
  return request(
    'api/crm/channel/channelConfig/channelType?needShowChannelType=' + payload,
    {
      method: 'GET',
    },
  );
}
/** 查询营销事件*/
export async function queryEventNameList(payload) {
  return request(
    'api/crm/market/statistics/queryEventNameList?activityId=' + payload,
    {
      method: 'GET',
    },
  );
}
/** 查询营销事件---A/B测试页面专用*/
export async function queryEventNameListAbTest(payload) {
  return request(
    'api/crm/market/statistics/queryEventNameList?activityId=' +
      payload.activityId +
      '&needShowMarketingType=' +
      payload.needShowMarketingType,
    {
      method: 'GET',
    },
  );
}
/** 查询营销事件批次*/
export async function queryEventBatchList(payload) {
  return request(
    'api/crm/market/statistics/queryEventBatchList?eventId=' + payload,
    {
      method: 'GET',
    },
  );
}
/** 查询营销详情tab*/
export async function queryMarketingStatusSummary(payload) {
  return request('api/crm/market/statistics/queryMarketingStatusSummary', {
    method: 'POST',
    data: payload.selectItem,
    params: {
      pageNum: payload.pageNum,
      pageSize: payload.pageSize,
    },
  });
}
/** 查询营销详情列表*/
export async function queryMarketingDetailList(payload) {
  return request('api/crm/market/statistics/queryMarketingDetailList', {
    method: 'POST',
    data: payload.selectItem,
    params: {
      pageNum: payload.pageNum,
      pageSize: payload.pageSize,
    },
  });
}

/** 查询营销详情页面的详情*/
export async function queryMarketingDetailById(payload) {
  return request(
    'api/crm/market/statistics/queryMarketingDetailById?detailId=' + payload,
    {
      method: 'GET',
    },
  );
}
/** 查询营销详情列表页的详情历史记录*/
export async function queryMarketingDetailHis(payload) {
  return request(
    'api/crm/market/statistics/queryMarketingDetailHis?detailId=' +
      payload.detailId,
    {
      method: 'GET',
      params: {
        pageNum: payload.pageNum,
        pageSize: payload.pageSize,
      },
    },
  );
}

/** 查询营销结果列表*/
export async function queryMarketingResultList(payload) {
  return request('api/crm/market/statistics/queryMarketingResultList', {
    method: 'POST',
    data: payload.selectItem,
    params: {
      pageNum: payload.pageNum,
      pageSize: payload.pageSize,
    },
  });
}
/** 导出客户清单*/
export async function exportCustomerList(payload) {
  // post请求导出工单，必须如下写法
  return request.post(
    'api/crm/market/statistics/exportCustomerList',
    {
      data: payload,
      responseType: 'blob',
    },
    'blob',
  );
}

/** 查询雷达图，数据图和分析结果*/
export async function queryMarketingAnalysisResult(payload) {
  return request('api/crm/market/statistics/queryMarketingAnalysisResult', {
    method: 'POST',
    data: payload,
  });
}
