import request from '@/utils/request';

/**
 * 查询营销活动
 */
export async function queryMarketing(payload) {
  return request('api/crm/market/marketingActivity/queryMarketing', {
    method: 'POST',
    data: payload.selectItem,
    params: {
      pageNum: payload.pageNum,
      pageSize: payload.pageSize,
    },
  });
}
export async function queryMarketing1(payload) {
  return request('api/crm/market/marketingActivity/queryMarketing', {
    method: 'POST',
    data: payload,
  });
}
/**
 * 查询活动详情
 */
export async function marketingDetail(payload) {
  return request(
    'api/crm/market/marketingActivity/marketingDetail?marketingId=' + payload,
    {
      method: 'GET',
    },
  );
}
/**
 * 上传海报
 */
export async function uploadPicture(payload) {
  return request('api/crm/market/marketingActivity/uploadPicture', {
    method: 'POST',
    data: payload,
  });
}
/**
 * 新增营销活动
 */
export async function addUpdateMarketing(payload) {
  return request('api/crm/market/marketingActivity/addUpdateMarketing', {
    method: 'POST',
    data: payload,
  });
}
/**
 * 删除营销活动
 */
export async function deleteMarketingStatus(payload) {
  return request('api/crm/market/marketingActivity/deleteMarketingStatus', {
    method: 'POST',
    data: payload,
  });
}
/**
 * 修改营销活动状态
 */
export async function updateMarketingStatus(payload) {
  return request('api/crm/market/marketingActivity/updateMarketingStatus', {
    method: 'POST',
    data: payload,
  });
}
/**
 *  查询活动日历
 */
export async function activityNodeDetail(payload) {
  // return request('api/crm/market/marketingActivity/activityNodeDetail?marketingId=1767755428483444738', {
  return request(
    'api/crm/market/marketingActivity/activityNodeDetail?marketingId=' +
      payload,
    {
      method: 'GET',
    },
  );
}
/**
 *  查询活动日历线
 */
export async function activityNode(payload) {
  // return request('api/crm/market/marketingActivity/activityNode?marketingId=1767755428483444738', {
  return request(
    'api/crm/market/marketingActivity/activityNode?marketingId=' + payload,
    {
      method: 'GET',
    },
  );
}
/**
 *  添加自定义营销事件
 */
export async function addMarketingEvent(payload) {
  return request('api/crm/market/event/market/event', {
    method: 'POST',
    data: payload,
  });
}
/** 查询整体方案*/
export async function schemeCreate(payload) {
  return request('api/crm/aigc/ai/schemeCreate', {
    method: 'POST',
    data: payload,
  });
}
/** 查询创意方案*/
export async function culturalScheme(payload) {
  return request('api/crm/aigc/ai/culturalScheme', {
    method: 'POST',
    data: payload,
  });
}
