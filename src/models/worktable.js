import {
  createWorkRecord,
  replyMessage,
  syncCallRecord,
  autoCreateWork,
  getSaml2,
  translate,
  queryProcessingWorkOrderList,
  queryProcessingWorkOrDerListSchedule,
  queryPendingWorkOrderList,
  queryRobotTicketTotalCount,
  queryRobotTicketList,
  emailTemplates,
  emailTemplatesDetail,
  emailTemplatesCreate,
  emailTemplatesUpdate,
  emailTemplatesDelete,
  queryEmailWorkOrderList,
  queryEmailTotalCount,
  listQueueQuickConnects,
  systemRegionTimezones,
  aiGenerateEmailDraft,
  aiBeautifyEmail,
  aiNeutralEmail,
  aiGentleEmail,
  aiSummarizeEmail,
  aiExpandEmail,
  grammarCheck,
  updateDefault,
  formalEmail,
  updateReadStatus,
  queryWorkBenchTicketTotalCount,
  aiExpansionText,
  aiBeautifyText,
  aiConciseText,
  aiGentleText,
  aiNeutralText,
  formalText,
  grammarCheckText,
  updateCustomerPhone,
  addPhoneContactDetail,
  addChannelContactDetail,
  postIsTop,
  messageRecall,
  s3ToBinary,
  updateContentDetails,
  getTicketSmartFillLanguage,
  queryCompanyTelPrefix,
  queryWhatsAppMessage,
  sendWhatsAppMessage,
} from '@/service/worktable';
import {
  queryWorkOrderDetail,
  queryWorkRecordInfo,
  claimWorkOrder,
  queryDefineExtList,
  querySeatsUser,
  queryWorkRecordType,
  queryWorkRecordLevel,
  queryByTicketRemarks,
  queryHistoryCustomerWorkOrder,
  queryAssociationWorkOrder,
  queryUpdateWork,
  upload,
  deleteFile,
  associationWorkOrderTable,
  updateWorkAssociation,
  workUpgrade,
  assignWorkOrder,
  transferWorkOrder,
  urgingWorkOrder,
  workRemark,
  terminationWorkStatus,
  solveWorkStatus,
  queryConcernedWorkOrder,
  addWaitExecute,
  updateWaitExecute,
  workSummarize,
  saveWorkSummarizeNew,
  querySummary,
  voiceAnalysis,
  queryAnswer,
  download,
  uploadPicture,
  WorkOrderOperationRecords,
  uploadByUrl,
  allocationTicket,
} from '@/service/workOrderCenter';
import {
  generateAgentToken,
  hangupFromAgent,
  twilioChunkUpload,
  getAcwStateTwilio,
  saveEventDetailsTwilio,
  queryCurrentAgentLinkList,
  getVoiceAnalysisToRobotByTwilio,
  getCallInfo,
  acceptByAgentClient,
} from '@/service/twilioService';
import { customerinfoList } from '../service/customerInformation';
import { queryChannelById, getChannelByEmailTo } from '@/service/channel';
import { notification } from '@/utils/utils';
import { FormattedMessage } from 'umi';
import { getIntl } from '@@/plugin-locale/localeExports';
import React from 'react';
import { closeChat } from '../service/channel';

export default {
  namespace: 'worktable',
  state: {
    email: [],
    showEmail: false,
    channelName: '',
    channelId: '',
    workRecordId: '',
    acwStatus: false,
    extractedTextsNew: '',
    newChannelTypeId: '',
    newCustomerId: '', //ccp面板客户id，用于客户资料初始化
    questions: '', //ccp面板问题，用于智能匹配
    replyType: '', //角色：1客服 2用户
    customerId: '',
    roleId: '',
    workOrderDetail: [],
    replyWorkOrderDetail: {},
    replySuccess: false,
    newWorkOrderDetail: [],
    loadingReply: false,
    // phoneNumber: '',
    content: '',
    contentFileList: [],
    pictureList: [],
    firstOpenAiEditor: true,
    operationDescriptionList: [],
    openAiEditor: false,
    fileList: [],
    channelTypeId: '',
    intelligentSummaryStatus: 1,
    workRecordList: [],
    attr: {},
    contact: null,
    contactIdTranslation: '',
    // 工作台转派显示弹窗状态
    upgradeModalOpen: false,
    initTranscriptList: [],
    //工作台工单领取：'1'待处理，'2':未分配,'3':机器人
    workTableTabProcesOrPendValue: '1',
    collectTicketStatus: false, // 工单是否领取成功，true成功
    // 工作台邮件
    workTableTabValue: null,
    emailSelectTicketData: {},
    isShowAcwAiIntelligenceSumModal: true, // true:acw状态弹出总结框，false：其他情况弹出总结框
    // 邮件类型转单、解决工单、终止工单状态
    refreshLeftEmailList: false,
    refreshRightAssociationList: false,
    workTableCopyAnswer: {},
    workTableSettingTranslation: false,
    workTableSettingTranslationPhone: false,
    workTableSettingTranslationCode: '',
    workTableSettingTranslationPhoneCode: '',
    workTableSettingTranslationPhoneMicrophones: '',
    workTableSettingTranslationPhoneSpeakers: '',
  },
  effects: {
    *createWorkRecord({ payload, callback }, { call, put }) {
      const response = yield call(createWorkRecord, payload);
      if (response) {
        let { code, data, msg } = response;
        if (code === 200) {
          yield put({
            type: 'saveState',
            payload: {
              workRecordId: data,
            },
          });
        }
      }
    },
    *autoCreateWork({ payload, callback }, { call, put }) {
      const response = yield call(autoCreateWork, payload);
      if (response) {
        let { code, data, msg } = response;
        if (code === 200) {
          yield put({
            type: 'saveState',
            payload: {
              workRecordId: data,
              showEmail: true,
            },
          });
          callback(response);
        }
      }
    },
    *claimWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(claimWorkOrder, payload);
      if (response) {
        let { code, data, msg } = response;
        if (code === 200) {
          yield put({
            type: 'saveState',
            payload: {
              workRecordId: payload.workRecordId,
              showEmail: true,
            },
          });
          callback(payload);
        }
      }
    },
    *initEmail({}, { select, call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          // workRecordId: payload,
          showEmail: true,
        },
      });

      // const state = yield select(state => state);
      // let workRecordId = state.worktable.workRecordId;
      // if (workRecordId) {
      //   yield put({
      //     type: 'queryWorkOrderDetail',
      //     payload: workRecordId,
      //   });
      // }
    },
    *saveConnectTranslation({ payload }, { call, put }) {
      yield put({
        type: 'saveTranslationState',
        payload: {
          attr: payload.attr,
          contact: payload.contact,
          contactIdTranslation: payload.contactId,
        },
      });
    },
    *exitEmail({ payload, callback }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          showEmail: false,
          newCustomerId: '',
        },
      });
      callback();
    },
    *copyAnswerToWorkTable({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workTableCopyAnswer: payload,
        },
      });
    },
    *showChannelDropDownList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          channelName: payload.name,
          channelId: payload.channelId,
        },
      });
    },
    *replyMessage({ payload, callback }, { call, put }) {
      const response = yield call(replyMessage, payload);
      yield put({
        type: 'saveState',
        payload: {
          replyType: payload.replyType,
          questions: payload.content,
        },
      });
      callback(response);
    },
    *addChannelContactDetail({ payload }, { call, put }) {
      const response = yield call(addChannelContactDetail, payload);
    },
    *syncCallRecord({ payload }, { call, put }) {
      yield call(syncCallRecord, payload);
    },
    *newSyncCallRecord({ payload }, { call, put }) {
      const response = yield call(syncCallRecord, payload);
      if (response) {
        if (response.code == 200) {
          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      }
    },
    *changeAcwState({ payload }, { select, call, put }) {
      const state = yield select(state => state);
      let acwStatus = state.worktable.acwStatus;
      if (!acwStatus) {
        yield put({
          type: 'saveState',
          payload: {
            acwStatus: true,
          },
        });
        yield put({
          type: 'queryWorkOrderDetail',
          payload: payload,
        });
      }
    },

    *queryWorkOrderDetail({ payload }, { call, put }) {
      const response = yield call(queryWorkOrderDetail, payload);
      if (response) {
        if (response.code == 200) {
          let params = {
            workRecordId: response.data.workRecordId,
            channelTypeId: response.data.channelTypeId,
            newWorkOrderDetail: response.data,
          };
          yield put({
            type: 'saveState',
            payload: {
              newChannelTypeId: response.data.channelTypeId,
              newCustomerId: response.data.customerId,
            },
          });
          console.log('测试加载回复详情第一个接口-----------', params);
          yield put({
            type: 'queryWorkRecordInfo',
            payload: params,
          });
          // let data = {
          //   pageInfo: {
          //     pageNum: 1,
          //     pageSize: 10,
          //   },
          //   data: {
          //     extInstVoList: [
          //       {
          //         code: 'customerId', // input 对应的name
          //         value: response.data.customerId, // input对应的value  如果是多选必须是list 其他类型为String
          //         propType: 1002, // input类型
          //         extType: 2, // 筛选条件类型  1-扩展类型  2-系统属性
          //       },
          //     ],
          //   },
          // };
          // yield put({
          //   type: 'saveState',
          //   payload: {
          //     newChannelTypeId: response.data.channelTypeId,
          //   },
          // });
          // yield put({
          //   type: 'customerinfoList',
          //   payload: data,
          // });
        }
      }
    },
    *queryWorkOrderDetail1({ payload, callback }, { call, put }) {
      const response = yield call(queryWorkOrderDetail, payload);
      if (response) {
        // callback(response);
        let workOrderDetail = response.data;
        let waitExecuteList = workOrderDetail.waitExecuteList;
        let selectWaitExecuteList = [];
        if (waitExecuteList) {
          for (let i = 0; i < waitExecuteList.length; i++) {
            /**
             * 0、待办 1、已办
             */
            if (waitExecuteList[i].waitExecuteStatus == 1) {
              selectWaitExecuteList.push(waitExecuteList[i].waitExecuteId);
            }
          }
        }

        yield put({
          type: 'saveState',
          payload: {
            newWorkOrderDetail: response.data,
            summarizeStatus: workOrderDetail.summarizeStatus,
            customerMood: workOrderDetail.customerMood,
            contentSummary: workOrderDetail.contentSummary,
            waitExecuteList: waitExecuteList,
            initWaitExecuteList: waitExecuteList,
            selectWaitExecuteList: selectWaitExecuteList,
            initContentSummary: workOrderDetail.contentSummary,
          },
        });

        yield put({
          type: 'initStepWorkOrder',
          payload: workOrderDetail,
        });
      }
    },
    *queryWorkOrderDetail2({ payload, callback }, { call, put }) {
      const response = yield call(queryWorkOrderDetail, payload);
      if (response) {
        callback(response);
        if (response.code == 200) {
          let params = {
            workRecordId: response.data.workRecordId,
            channelTypeId: response.data.channelTypeId,
            newWorkOrderDetail: response.data,
          };
          yield put({
            type: 'saveState',
            payload: {
              workRecordId: response.data.workRecordId,
              newChannelTypeId: response.data.channelTypeId,
              newCustomerId: response.data.customerId,
            },
          });
          // yield put({
          //   type: 'queryWorkRecordInfo',
          //   payload: params,
          // });
          //

          // let data = {
          //   pageInfo: {
          //     pageNum: 1,
          //     pageSize: 10,
          //   },
          //   data: {
          //     extInstVoList: [
          //       {
          //         code: 'customerId', // input 对应的name
          //         value: response.data.customerId, // input对应的value  如果是多选必须是list 其他类型为String
          //         propType: 1002, // input类型
          //         extType: 2, // 筛选条件类型  1-扩展类型  2-系统属性
          //       },
          //     ],
          //   },
          // };
          // yield put({
          //   type: 'saveState',
          //   payload: {
          //     newChannelTypeId: response.data.channelTypeId,
          //   },
          // });
          // yield put({
          //   type: 'customerinfoList',
          //   payload: data,
          // });
        }
      }
    },
    *queryWorkOrderDetailRead({ payload, callback }, { call, put }) {
      const response = yield call(queryWorkOrderDetail, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkRecordInfo({ payload }, { call, put }) {
      const response = yield call(queryWorkRecordInfo, payload);
      if (response) {
        if (response.code == 200) {
          let detailInfoList = response.data.dataList;
          if (
            payload.channelTypeId == 7 ||
            payload.channelTypeId == 10 ||
            payload.channelTypeId == 11 ||
            payload.channelTypeId == 17 ||
            payload.channelTypeId == 18
          ) {
            let contactId = detailInfoList[0].contact_id;
            let workRecordId = detailInfoList[0].work_record_id;
            yield put({
              type: 'queryVoiceAnalysis1',
              payload: { contactId, workRecordId },
            });
            yield put({
              type: 'saveState',
              payload: {
                ticketContentList: detailInfoList,
                ticketDetailList: detailInfoList,
                channelTypeId: payload.channelTypeId,
                loadingReply: false,
              },
            });
          } else if (payload.channelTypeId == 1) {
            let extractedTexts = '';
            for (let i = 0; i < detailInfoList.length; i++) {
              let content = detailInfoList[i].content;
              let replyType = detailInfoList[i].reply_type;
              const parser = new DOMParser();
              const doc = parser.parseFromString(content, 'text/html');
              let textNodes = doc.body.innerText.trim().split('\n');
              // let newTextNodes=''
              if (replyType == 1 || replyType == 3) {
                // 客服
                textNodes = 'agent:' + textNodes + '\n';
              } else if (replyType == 2) {
                // 客户
                textNodes = 'custom:' + textNodes + '\n';
              }
              extractedTexts += textNodes;
              // detailInfoList[i].translate_content = null;
            }
            yield put({
              type: 'saveState',
              payload: {
                contentList: detailInfoList,
                detailList: detailInfoList,
                channelTypeId: payload.channelTypeId,
                loadingReply: false,
                extractedTextsNew: extractedTexts,
              },
            });
          } else {
            let extractedTexts = '';
            for (let i = 0; i < detailInfoList.length; i++) {
              let content = detailInfoList[i].content;
              let replyType = detailInfoList[i].reply_type;
              const parser = new DOMParser();
              const doc = parser.parseFromString(content, 'text/html');
              let textNodes = doc.body.innerText.trim().split('\n');
              // let newTextNodes=''
              if (replyType == 1 || replyType == 3) {
                // 客服
                textNodes = 'agent:' + textNodes + '\n';
              } else if (replyType == 2) {
                // 客户
                textNodes = 'custom:' + textNodes + '\n';
              }
              extractedTexts += textNodes;
              // detailInfoList[i].translate_content = null;
            }
            yield put({
              type: 'saveState',
              payload: {
                contentList: detailInfoList,
                detailList: detailInfoList,
                channelTypeId: payload.channelTypeId,
                loadingReply: false,
                extractedTextsNew: extractedTexts,
              },
            });
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      }
    },
    *restAcwState({}, { select, call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          acwStatus: false,
        },
      });
    },

    *setCheckedTipsStatus({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          checkedTipsStatus: payload,
        },
      });
    },
    *queryDefineExtList({ callback }, { call, put }) {
      const response = yield call(queryDefineExtList);
      if (response) {
        callback(response);
      }
    },
    *querySeatsUser({ callback }, { call, put }) {
      const response = yield call(querySeatsUser);
      if (response) {
        callback(response);
      }
    },
    // 查看工单详情
    *queryWorkOrderDetailList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          customerId: payload.workOrderDetail.customerId,
          roleId: payload.roleId,
          workOrderDetail: payload.workOrderDetail,
        },
      });
    },
    *queryWorkRecordType({ callback }, { call, put }) {
      const response = yield call(queryWorkRecordType);
      if (response) {
        callback(response);
      }
    },
    *queryWorkRecordLevel({ callback }, { call, put }) {
      const response = yield call(queryWorkRecordLevel);
      if (response) {
        callback(response);
      }
    },
    *queryByTicketRemarks({ payload, callback }, { call, put }) {
      const response = yield call(queryByTicketRemarks, payload);
      if (response) {
        callback(response);
      }
    },
    *queryHistoryCustomerWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(queryHistoryCustomerWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAssociationWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(queryAssociationWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUpdateWork({ payload, callback }, { call, put }) {
      const response = yield call(queryUpdateWork, payload);
      if (response) {
        callback(response);
      }
    },
    *setReplyWorkOrderDetail({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          replyWorkOrderDetail: payload,
        },
      });
    },
    *setReplySuccess({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          replySuccess: false,
        },
      });
    },
    *queryReplyMessage({ payload, callback }, { select, call, put }) {
      const response = yield call(replyMessage, payload);
      const state = yield select(state => state);
      let newWorkOrderDetail = state.worktable.newWorkOrderDetail;

      let params = {
        workRecordId: newWorkOrderDetail.workRecordId,
        channelTypeId: newWorkOrderDetail.channelTypeId,
      };
      yield put({
        type: 'saveState',
        payload: {
          loadingReply: true,
          replySuccess: true,
        },
      });
      // yield put({
      //   type: 'queryWorkRecordInfo',
      //   payload: params,
      // });
      if (response) {
        callback(response);
      }
    },
    *queryGenerateAgentToken({ payload, callback }, { call, put }) {
      const response = yield call(generateAgentToken, payload);
      if (response) {
        callback(response);
      }
    },
    *getAcwStateTwilio({ payload, callback }, { call, put }) {
      const response = yield call(getAcwStateTwilio, payload);
      if (response) {
        callback(response);
      }
    },
    *saveEventDetailsTwilio({ payload, callback }, { call, put }) {
      const response = yield call(saveEventDetailsTwilio, payload);
      if (response) {
        callback(response);
      }
    },
    *getVoiceAnalysisToRobotByTwilio({ payload, callback }, { call, put }) {
      const response = yield call(getVoiceAnalysisToRobotByTwilio, payload);
      if (response) {
        callback(response);
      }
    },
    *getCallInfo({ payload, callback }, { call, put }) {
      const response = yield call(getCallInfo, payload);
      if (response) {
        callback(response);
      }
    },
    *queryCurrentAgentLinkList({ payload, callback }, { call, put }) {
      const response = yield call(queryCurrentAgentLinkList, payload);
      if (response) {
        callback(response);
      }
    },
    *hangupFromAgent({ payload, callback }, { call, put }) {
      const response = yield call(hangupFromAgent, payload);
      if (response) {
        callback(response);
      }
    },
    *twilioChunkUpload({ payload, callback }, { call, put }) {
      const response = yield call(twilioChunkUpload, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUpload({ payload, callback }, { call, put }) {
      const response = yield call(upload, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUploadByUrl({ payload, callback }, { call, put }) {
      const response = yield call(uploadByUrl, payload);
      if (response) {
        callback(response);
      }
    },
    *queryDeleteFile({ payload, callback }, { call, put }) {
      const response = yield call(deleteFile, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAssociationWorkOrderTable({ payload, callback }, { call, put }) {
      const response = yield call(associationWorkOrderTable, payload);
      if (response) {
        callback(response);
      }
    },
    *acceptByAgentClient({ payload, callback }, { call, put }) {
      const response = yield call(acceptByAgentClient, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUpdateWorkAssociation({ payload, callback }, { call, put }) {
      const response = yield call(updateWorkAssociation, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkUpgrade({ payload, callback }, { call, put }) {
      const response = yield call(workUpgrade, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAssignWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(assignWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAllocationTicket({ payload, callback }, { call, put }) {
      const response = yield call(allocationTicket, payload);
      if (response) {
        callback(response);
      }
    },
    *getSaml2({ payload, callback }, { call, put }) {
      const response = yield call(getSaml2, payload);
      if (response) {
        callback(response);
      }
    },
    *queryTransferWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(transferWorkOrder, payload);
      if (response) {
        let { code, data, msg } = response;
        if (code === 200) {
          yield put({
            type: 'saveState',
            payload: {
              workRecordId: data,
              showEmail: true,
            },
          });
          callback(response);
        }
      }
    },
    *queryUrgingWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(urgingWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkRemark({ payload, callback }, { call, put }) {
      const response = yield call(workRemark, payload);
      if (response) {
        callback(response);
      }
    },
    *queryTerminationWorkStatus({ payload, callback }, { call, put }) {
      const response = yield call(terminationWorkStatus, payload);
      if (response) {
        callback(response);
      }
    },
    *querySolveWorkStatus({ payload, callback }, { call, put }) {
      const response = yield call(solveWorkStatus, payload);
      if (response) {
        callback(response);
      }
    },
    *queryClaimWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(claimWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryConcernedWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(queryConcernedWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *translate({ payload, callback }, { call, put }) {
      const response = yield call(translate, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAddWaitExecute({ payload, callback }, { call, put }) {
      const response = yield call(addWaitExecute, payload);
      if (response) {
        callback(response);
      }
    },
    *queryProcessingWorkOrderList({ payload, callback }, { call, put }) {
      const response = yield call(queryProcessingWorkOrderList, payload);
      if (response) {
        callback(response);
      }
    },
    *queryProcessingWorkOrDerListSchedule(
      { payload, callback },
      { call, put },
    ) {
      const response = yield call(
        queryProcessingWorkOrDerListSchedule,
        payload,
      );
      if (response) {
        callback(response);
      }
    },
    //
    *queryPendingWorkOrderList({ payload, callback }, { call, put }) {
      const response = yield call(queryPendingWorkOrderList, payload);
      if (response) {
        callback(response);
      }
    },
    *queryRobotTicketList({ payload, callback }, { call, put }) {
      const response = yield call(queryRobotTicketList, payload);
      if (response) {
        callback(response);
      }
    },
    *queryRobotTicketTotalCount({ callback }, { call, put }) {
      const response = yield call(queryRobotTicketTotalCount);
      if (response) {
        callback(response);
      }
    },

    *queryUpdateWaitExecute({ payload, callback }, { call, put }) {
      const response = yield call(updateWaitExecute, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkSummarize({ payload, callback }, { call, put }) {
      const response = yield call(workSummarize, payload);
      if (response) {
        callback(response);
        yield put({
          type: 'saveState',
          payload: {
            intelligentSummaryStatus: 2,
          },
        });
      }
    },
    *listQueueQuickConnects({ payload, callback }, { call, put }) {
      const response = yield call(listQueueQuickConnects, payload);
      if (response) {
        callback(response);
      }
    },
    *systemRegionTimezones({ payload, callback }, { call, put }) {
      const response = yield call(systemRegionTimezones, payload);
      if (response) {
        callback(response);
      }
    },
    // 智能总结保存
    *saveWorkSummarizeNew({ payload, callback }, { call, put }) {
      const response = yield call(saveWorkSummarizeNew, payload);
      if (response) {
        callback(response);
      }
    },
    *querySummary({ payload, callback }, { call, put }) {
      const response = yield call(querySummary, payload);
      if (response) {
        callback(response);
      }
    },
    *updateContentDetails({ payload, callback }, { call, put }) {
      const response = yield call(updateContentDetails, payload);
      if (response) {
      }
    },
    *queryWhatsAppMessage({ payload, callback }, { call, put }) {
      const response = yield call(queryWhatsAppMessage, payload);
      if (response) {
        callback(response);
      }
    },
    *sendWhatsAppMessage({ payload, callback }, { call, put }) {
      const response = yield call(sendWhatsAppMessage, payload);
      if (response) {
        callback(response);
      }
    },
    *queryVoiceAnalysis({ payload, callback }, { call, put }) {
      const response = yield call(voiceAnalysis, payload);
      if (response) {
        callback(response);
      }
    },
    // *setPhoneNumber({ payload }, { call, put }) {
    //   yield put({
    //     type: 'saveState',
    //     payload: {
    //       phoneNumber: payload,
    //     },
    //   });
    // },
    *queryAnswer({ payload, callback }, { select, call, put }) {
      const state = yield select(state => state);
      let ticketContentList = state.worktable.contentList;
      let newTicketContentList = JSON.stringify(ticketContentList);
      let newTicketContentList1 = JSON.parse(newTicketContentList);
      // let ticketContentList1 = Object.assign({}, state.workOrderCenter.ticketContentList);
      // let ticketContentList =Array.from(ticketContentList1);
      // console.log(ticketContentList);
      // let length = Object.keys(ticketContentList).length;
      yield put({
        type: 'saveState',
        payload: {
          loadingReply: true,
        },
      });
      let params = {
        promptId: '40000',
        contentQuestion: payload.contentQuestion,
        language: payload.language,
      };
      let wordType = payload.wordType;
      let workRecordContentId = payload.workRecordContentId;
      const response = yield call(queryAnswer, params);
      if (response) {
        callback(response);
        if (wordType == 3) {
          let answerChatText = response.data.text;
          for (let i = 0; i < newTicketContentList1.length; i++) {
            if (
              newTicketContentList1[i].workRecordContentId ==
              workRecordContentId
            ) {
              newTicketContentList1[i].translationContent = answerChatText;
            }
          }
          yield put({
            type: 'saveState',
            payload: {
              contentList: newTicketContentList1,
            },
          });
          yield put({
            type: 'saveState',
            payload: {
              loadingReply: false,
            },
          });
        } else if (wordType == 2) {
          let answerEmailText = response.data.text;
          for (let i = 0; i < newTicketContentList1.length; i++) {
            if (
              newTicketContentList1[i].workRecordContentId ==
              workRecordContentId
            ) {
              newTicketContentList1[i].content = answerEmailText;
            }
          }
          yield put({
            type: 'saveState',
            payload: {
              contentList: newTicketContentList1,
            },
          });
          yield put({
            type: 'saveState',
            payload: {
              loadingReply: false,
            },
          });
        } else {
          yield put({
            type: 'saveState',
            payload: {
              loadingReply: false,
            },
          });
        }
      }
    },
    *queryDownload({ payload, callback }, { call, put }) {
      const response = yield call(download, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUploadPicture({ payload, callback }, { call, put }) {
      const response = yield call(uploadPicture, payload);
      if (response) {
        callback(response);
      }
    },
    *queryCompanyTelPrefix({ payload, callback }, { call, put }) {
      const response = yield call(queryCompanyTelPrefix, payload);
      if (response) {
        callback(response);
      }
    },
    *addPhoneContactDetail({ payload }, { call, put }) {
      const response = yield call(addPhoneContactDetail, payload);
    },
    *updateContentFileList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          contentFileList: payload,
        },
      });
    },
    *updatePictureList({ payload, callback }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          pictureList: payload,
        },
      });
    },
    *queryWorkOrderOperationRecords({ payload }, { call, put }) {
      const response = yield call(WorkOrderOperationRecords, payload);
      if (response) {
        if (response.code == 200) {
          let operationDescriptionList = [];
          let data = response.data;
          for (let i = 0; i < data.length; i++) {
            let item = {
              title: data[i].createTime,
              description: data[i].operationLogDescribe,
            };
            operationDescriptionList.push(item);
          }
          yield put({
            type: 'saveState',
            payload: {
              operationDescriptionList: operationDescriptionList,
            },
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      }
    },
    *setOpenAiEditor({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          openAiEditor: payload,
        },
      });
    },
    *setFirstOpenAiEditor({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          firstOpenAiEditor: payload,
        },
      });
    },
    *setTicketContentList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          contentList: payload,
        },
      });
    },
    *initStepWorkOrder({ payload }, { select, call, put }) {
      /**
       * 状态0-待分配，1-待客服处理，2-待客户回复，3-已解决，4-已终止，5-已转单
       */
      // const state = yield select(state => state);
      // let newWorkOrderDetail = state.worktable.contentList;
      let status = payload.status;
      if (status == '0') {
        this.setState({
          createTime: payload.createTime,
          statusStepCurrent: 0,
          renew: payload,
        });
      } else if (status == '3') {
        yield put({
          type: 'saveState',
          payload: {
            resolveTime: payload.resolveTime,
            initiationTime: payload.initiationTime,
            createTime: payload.createTime,
            statusStepCurrent: 2,
            titleText: getIntl().formatMessage({
              id: 'work.order.ticket.02.1',
              defaultValue: '处理中',
            }),
            renew: payload,
          },
        });
      } else {
        let titleText = '';
        let stepsStatus = '';
        let initiationTime = '';
        if (status == '4') {
          titleText = getIntl().formatMessage({
            id: 'work.order.ticket.02.2',
            defaultValue: '以终止',
          });
          stepsStatus = 'error';
          initiationTime = payload.terminateTime;
        } else if (status == '5') {
          titleText = getIntl().formatMessage({
            id: 'work.order.ticket.02.3',
            defaultValue: '已转单',
          });
          stepsStatus = 'error';
          initiationTime = payload.transferTime;
        } else {
          titleText = getIntl().formatMessage({
            id: 'work.order.ticket.02.1',
            defaultValue: '处理中',
          });
          stepsStatus = 'process';
          initiationTime = payload.initiationTime;
        }
        yield put({
          type: 'saveState',
          payload: {
            statusStepCurrent: 1,
            titleText: titleText,
            stepsStatus: stepsStatus,
            initiationTime: initiationTime,
            createTime: payload.createTime,
            renew: payload,
          },
        });
      }
    },
    *queryChannelById({ payload, callback }, { call, put }) {
      let response = yield call(queryChannelById, payload);
      callback(response);
    },
    *getChannelByEmailTo({ payload, callback }, { call, put }) {
      // console.log('payload', payload);
      let response = yield call(getChannelByEmailTo, payload);
      // console.log('response', response);
      if (response) {
        callback(response);
      }
    },
    *closeChat({ payload, callback }, { call, put }) {
      let response = yield call(closeChat, payload);
      if (response) {
        callback(response);
      }
    },
    *queryVoiceAnalysis1({ payload }, { call, put }) {
      const response = yield call(voiceAnalysis, payload);
      if (response) {
        if (response) {
          if (response.code == 200) {
            if (response.data) {
              let extractedTexts = '';
              let Transcript = response.data.translateVOS;
              if (Transcript?.length > 0) {
                for (let i = 0; i < Transcript.length; i++) {
                  let newContent =
                    Transcript[i].type + ':' + Transcript[i].content;
                  extractedTexts += newContent + '\n';
                }
                yield put({
                  type: 'saveState',
                  payload: {
                    extractedTextsNew: extractedTexts,
                    initTranscriptList: Transcript,
                    initTranscriptText: response.data.text,
                    initTranslateType: response.data.translateType,
                  },
                });
              }
            }
          }
        }
      }
    },

    *saveWorkRecordId({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workRecordId: payload,
        },
      });
    },
    *saveWorkRecordList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workRecordList: payload,
        },
      });
    },
    // 转派工单弹窗
    *setUpgradeModalOpen({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          upgradeModalOpen: payload,
        },
      });
    },

    // 工作台工单领取
    *saveWorkTableTabProcesOrPendValue({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workTableTabProcesOrPendValue: payload,
        },
      });
    },
    // 工作台工单领取成功标识
    *saveWorkTableCollectTicket({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          collectTicketStatus: payload,
        },
      });
    },
    *saveWorkTableSettingTranslation({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workTableSettingTranslation: payload,
        },
      });
    },
    *saveWorkTableSettingTranslationPhone({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workTableSettingTranslationPhone: payload,
        },
      });
    },
    *saveWorkTableSettingTranslationCode({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workTableSettingTranslationCode: payload,
        },
      });
    },
    *saveWorkTableSettingTranslationPhoneCode({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workTableSettingTranslationPhoneCode: payload,
        },
      });
    },
    *saveWorkTableSettingTranslationPhoneMicrophones(
      { payload },
      { call, put },
    ) {
      yield put({
        type: 'saveState',
        payload: {
          workTableSettingTranslationPhoneMicrophones: payload,
        },
      });
    },
    *saveWorkTableSettingTranslationPhoneSpeakers({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workTableSettingTranslationPhoneSpeakers: payload,
        },
      });
    },
    // 工作台工单待分配、处理中数量
    *queryWorkBenchTicketTotalCount({ callback }, { call, put }) {
      const response = yield call(queryWorkBenchTicketTotalCount);
      if (response) {
        callback(response);
      }
    },
    // 工作台置顶会话
    *postIsTop({ payload, callback }, { call, put }) {
      const response = yield call(postIsTop, payload);
      if (response) {
        callback(response);
      }
    },
    // 工作台消息撤回,删除
    *messageRecall({ payload, callback }, { call, put }) {
      const response = yield call(messageRecall, payload);
      if (response) {
        callback(response);
      }
    },
    // 工作台邮件
    *saveWorkTableTabValue({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workTableTabValue: payload,
        },
      });
    },
    *saveEmailSelectTicketData({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          emailSelectTicketData: payload,
        },
      });
    },
    *queryWorkTableTicketInfo({ payload, callback }, { call, put }) {
      const response = yield call(queryWorkRecordInfo, payload);
      if (response) {
        callback(response);
      }
    },
    *emailTemplates({ payload, callback }, { call, put }) {
      const response = yield call(emailTemplates, payload);
      if (response) {
        callback(response);
      }
    },
    *emailTemplatesDetail({ payload, callback }, { call, put }) {
      const response = yield call(emailTemplatesDetail, payload);
      if (response) {
        callback(response);
      }
    },
    *emailTemplatesCreate({ payload, callback }, { call, put }) {
      const response = yield call(emailTemplatesCreate, payload);
      if (response) {
        callback(response);
      }
    },
    *emailTemplatesUpdate({ payload, callback }, { call, put }) {
      const response = yield call(emailTemplatesUpdate, payload);
      if (response) {
        callback(response);
      }
    },
    *emailTemplatesDelete({ payload, callback }, { call, put }) {
      const response = yield call(emailTemplatesDelete, payload);
      if (response) {
        callback(response);
      }
    },
    *queryEmailWorkOrderList({ payload, callback }, { call, put }) {
      const response = yield call(queryEmailWorkOrderList, payload);
      if (response) {
        callback(response);
      }
    },

    *saveAcwAiIntelligenceSumModal({ payload }, { put }) {
      yield put({
        type: 'saveState',
        payload: {
          isShowAcwAiIntelligenceSumModal: payload,
        },
      });
    },

    *queryEmailTotalCount({ callback }, { call, put }) {
      const response = yield call(queryEmailTotalCount);
      if (response) {
        callback(response);
      }
    },
    *aiGenerateEmailDraft({ payload, callback }, { call, put }) {
      const response = yield call(aiGenerateEmailDraft, payload);
      if (response) {
        callback(response);
      }
    },
    // chat聊天保存手机号
    *updateCustomerPhone({ payload, callback }, { call, put }) {
      const response = yield call(updateCustomerPhone, payload);
      if (response) {
        callback(response);
      }
    },
    *aiBeautifyEmail({ payload, callback }, { call, put }) {
      const response = yield call(aiBeautifyEmail, payload);
      if (response) {
        callback(response);
      }
    },
    *formalEmail({ payload, callback }, { call, put }) {
      const response = yield call(formalEmail, payload);
      if (response) {
        callback(response);
      }
    },
    //
    *formalText({ payload, callback }, { call, put }) {
      const response = yield call(formalText, payload);
      if (response) {
        callback(response);
      }
    },
    //
    *grammarCheckText({ payload, callback }, { call, put }) {
      const response = yield call(grammarCheckText, payload);
      if (response) {
        callback(response);
      }
    },
    *aiNeutralEmail({ payload, callback }, { call, put }) {
      const response = yield call(aiNeutralEmail, payload);
      if (response) {
        callback(response);
      }
    },
    *aiGentleEmail({ payload, callback }, { call, put }) {
      const response = yield call(aiGentleEmail, payload);
      if (response) {
        callback(response);
      }
    },
    *aiSummarizeEmail({ payload, callback }, { call, put }) {
      const response = yield call(aiSummarizeEmail, payload);
      if (response) {
        callback(response);
      }
    },
    *aiExpandEmail({ payload, callback }, { call, put }) {
      const response = yield call(aiExpandEmail, payload);
      if (response) {
        callback(response);
      }
    },
    *grammarCheck({ payload, callback }, { call, put }) {
      const response = yield call(grammarCheck, payload);
      if (response) {
        callback(response);
      }
    },
    *aiBeautifyText({ payload, callback }, { call, put }) {
      const response = yield call(aiBeautifyText, payload);
      if (response) {
        callback(response);
      }
    },
    *aiExpansionText({ payload, callback }, { call, put }) {
      const response = yield call(aiExpansionText, payload);
      if (response) {
        callback(response);
      }
    },

    //
    *aiConciseText({ payload, callback }, { call, put }) {
      const response = yield call(aiConciseText, payload);
      if (response) {
        callback(response);
      }
    },
    //
    *aiGentleText({ payload, callback }, { call, put }) {
      const response = yield call(aiGentleText, payload);
      if (response) {
        callback(response);
      }
    },
    //
    *aiNeutralText({ payload, callback }, { call, put }) {
      const response = yield call(aiNeutralText, payload);
      if (response) {
        callback(response);
      }
    },
    *updateDefault({ payload, callback }, { call, put }) {
      const response = yield call(updateDefault, payload);
      if (response) {
        callback(response);
      }
    },
    *updateReadStatus({ payload, callback }, { call, put }) {
      const response = yield call(updateReadStatus, payload);
      if (response) {
        callback(response);
      }
    },
    *s3ToBinary({ payload, callback }, { call, put }) {
      const response = yield call(s3ToBinary, payload);
      const url = window.URL.createObjectURL(
        new Blob([response], {
          type:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        }),
      );
      if (url) {
        callback(url);
      }
    },
    // 修改转派、解决、终止工单状态
    *setRefreshLeftEmailList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          refreshLeftEmailList: payload,
        },
      });
    },
    *setRefreshRightAssociationList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          refreshRightAssociationList: payload,
        },
      });
    },
    // 智能填单语言下拉列表查询
    *getTicketSmartFillLanguage({ callback }, { call, put }) {
      const response = yield call(getTicketSmartFillLanguage);
      if (response) {
        callback(response);
      }
    },
  },

  //

  reducers: {
    saveState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    updateFileList(state, { payload: fileList }) {
      return { ...state, fileList };
    },
    saveTranslationState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
