import {
  AiAgentSave,
  AiAgentSaveAndDeploy,
  getAiAgentDeployList,
  AiAgentDeploy,
  AiAgentInfo,
  getAiAgentComList,
  queryCurrentVar,
  deteleCurrentVar,
  updateCurrentVar,
  addCurrentVar,
  queryReminderList,
  getAiAgentDetail,
  getIntentGrouped,
  saveReminderList,
  deleteReminderList,
  queryGroupVariables,
  updateAiAgentChannel,
  updateAiAgentName,
  getShareCode,
  getShareCodeContent,
} from '@/service/aiAgent';
export default {
  namespace: 'aiagent',
  state: {
    currentTitle: '',
    currentVariables: '',
  },
  effects: {
    *setCurrentTitle({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          currentTitle: payload,
        },
      });
    },
    *setCurrentVariables({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          currentVariables: payload,
        },
      });
    },
    *AiAgentSave({ payload, callback }, { call, put }) {
      let response = yield call(AiAgentSave, payload);
      if (response) {
        callback(response);
      }
    },
    *AiAgentSaveAndDeploy({ payload, callback }, { call, put }) {
      let response = yield call(AiAgentSaveAndDeploy, payload);
      if (response) {
        callback(response);
      }
    },
    *getAiAgentDeployList({ payload, callback }, { call, put }) {
      let response = yield call(getAiAgentDeployList, payload);
      if (response) {
        callback(response);
      }
    },
    *AiAgentDeploy({ payload, callback }, { call, put }) {
      let response = yield call(AiAgentDeploy, payload);
      if (response) {
        callback(response);
      }
    },
    *AiAgentInfo({ payload, callback }, { call, put }) {
      let response = yield call(AiAgentInfo, payload);
      if (response) {
        callback(response);
      }
    },
    *getAiAgentComList({ payload, callback }, { call, put }) {
      let response = yield call(getAiAgentComList, payload);
      if (response) {
        callback(response);
      }
    },
    *queryCurrentVar({ payload, callback }, { call, put }) {
      let response = yield call(queryCurrentVar, payload);
      if (response) {
        callback(response);
      }
    },
    *queryGroupVariables({ payload, callback }, { call, put }) {
      let response = yield call(queryGroupVariables, payload);
      if (response) {
        callback(response);
      }
    },
    *deteleCurrentVar({ payload, callback }, { call, put }) {
      let response = yield call(deteleCurrentVar, payload);
      if (response) {
        callback(response);
      }
    },
    *updateCurrentVar({ payload, callback }, { call, put }) {
      let response = yield call(updateCurrentVar, payload);
      if (response) {
        callback(response);
      }
    },
    *addCurrentVar({ payload, callback }, { call, put }) {
      let response = yield call(addCurrentVar, payload);
      if (response) {
        callback(response);
      }
    },
    *queryReminderList({ payload, callback }, { call, put }) {
      let response = yield call(queryReminderList, payload);
      if (response) {
        callback(response);
      }
    },
    *getAiAgentDetail({ payload, callback }, { call, put }) {
      let response = yield call(getAiAgentDetail, payload);
      if (response) {
        callback(response);
      }
    },
    *getIntentGrouped({ payload, callback }, { call, put }) {
      let response = yield call(getIntentGrouped, payload);
      if (response) {
        callback(response);
      }
    },
    *saveReminderList({ payload, callback }, { call, put }) {
      let response = yield call(saveReminderList, payload);
      if (response) {
        callback(response);
      }
    },
    *deleteReminderList({ payload, callback }, { call, put }) {
      let response = yield call(deleteReminderList, payload);
      if (response) {
        callback(response);
      }
    },
    *updateAiAgentChannel({ payload, callback }, { call, put }) {
      let response = yield call(updateAiAgentChannel, payload);
      if (response) {
        callback(response);
      }
    },
    *updateAiAgentName({ payload, callback }, { call, put }) {
      let response = yield call(updateAiAgentName, payload);
      if (response) {
        callback(response);
      }
    },
    *getShareCode({ payload, callback }, { call, put }) {
      let response = yield call(getShareCode, payload);
      if (response) {
        callback(response);
      }
    },
    *getShareCodeContent({ payload, callback }, { call, put }) {
      let response = yield call(getShareCodeContent, payload);
      if (response) {
        callback(response);
      }
    },
    reducers: {
      saveState(state, { payload }) {
        return {
          ...state,
          ...payload,
        };
      },
    },
  },
};
