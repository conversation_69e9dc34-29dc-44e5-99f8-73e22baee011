import {
  uploadTransferMusicFile,
  queryTransferMusicConfig,
  saveTransferMusicConfig,
} from '@/service/manualWaiting';
export default {
  namespace: 'manualWaiting',
  state: {},
  effects: {
    // 上传转人工等待音乐文件
    *uploadTransferMusicFile({ payload, callback }, { call, put }) {
      const response = yield call(uploadTransferMusicFile, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询转人工等待音乐配置
    *queryTransferMusicConfig({ callback }, { call, put }) {
      const response = yield call(queryTransferMusicConfig);
      if (response) {
        callback(response);
      }
    },
    // 新增/更新 转人工等待音乐配置
    *saveTransferMusicConfig({ payload, callback }, { call, put }) {
      const response = yield call(saveTransferMusicConfig, payload);
      if (response) {
        callback(response);
      }
    },
  },
  reducers: {
    saveState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
