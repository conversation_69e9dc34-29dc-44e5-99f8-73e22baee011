import {
  queryMarketing,
  marketingDetail,
  uploadPicture,
  addUpdateMarketing,
  updateMarketingStatus,
  activityNodeDetail,
  queryMarketing1,
  activityNode,
  addMarketingEvent,
  culturalScheme,
  schemeCreate,
  deleteMarketingStatus,
} from '../service/marketingActivities';

export default {
  namespace: 'marketingActivities',
  state: {
    menuType: '',
  },
  effects: {
    // 查询营销活动
    *queryMarketing({ payload, callback }, { call, put }) {
      let response = yield call(queryMarketing, payload);
      if (response) {
        callback(response);
      }
    },
    *queryMarketing1({ payload, callback }, { call, put }) {
      let response = yield call(queryMarketing1, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询营销活动详情
    *marketingDetail({ payload, callback }, { call, put }) {
      let response = yield call(marketingDetail, payload);
      if (response) {
        callback(response);
      }
    },
    // 上传海报
    *uploadPicture({ payload, callback }, { call, put }) {
      let response = yield call(uploadPicture, payload);
      if (response) {
        callback(response);
      }
    },

    *setQueryType({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          menuType: payload,
        },
      });
    },
    // 新增营销活动
    *addUpdateMarketing({ payload, callback }, { call, put }) {
      let response = yield call(addUpdateMarketing, payload);
      if (response) {
        callback(response);
      }
    },
    // 删除营销活动
    *deleteMarketingStatus({ payload, callback }, { call, put }) {
      let response = yield call(deleteMarketingStatus, payload);
      if (response) {
        callback(response);
      }
    },
    // 修改营销活动状态
    *updateMarketingStatus({ payload, callback }, { call, put }) {
      let response = yield call(updateMarketingStatus, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询活动日历
    *activityNodeDetail({ payload, callback }, { call, put }) {
      let response = yield call(activityNodeDetail, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询活动日历线
    *activityNode({ payload, callback }, { call, put }) {
      let response = yield call(activityNode, payload);
      if (response) {
        callback(response);
      }
    },
    // 添加自定义营销事件
    *addMarketingEvent({ payload, callback }, { call, put }) {
      let response = yield call(addMarketingEvent, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询整体方案
    *schemeCreate({ payload, callback }, { call, put }) {
      let response = yield call(schemeCreate, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询创意方案
    *culturalScheme({ payload, callback }, { call, put }) {
      let response = yield call(culturalScheme, payload);
      if (response) {
        callback(response);
      }
    },
  },
  reducers: {
    saveState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
