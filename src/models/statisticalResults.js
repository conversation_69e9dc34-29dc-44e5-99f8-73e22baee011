import {
  queryActivityList,
  channelType,
  queryEventNameList,
  queryEventBatchList,
  queryMarketingStatusSummary,
  queryMarketingDetailList,
  queryMarketingDetailById,
  queryMarketingDetailHis,
  queryMarketingResultList,
  exportCustomerList,
  queryMarketingAnalysisResult,
  queryEventNameListAbTest,
} from '../service/statisticalResults';
import { getIntl } from '@@/plugin-locale/localeExports';

export default {
  namespace: 'statisticalResults',
  state: {},
  effects: {
    // 查询活动名称
    *queryActivityList({ payload, callback }, { call, put }) {
      let response = yield call(
        queryActivityList,
        payload ? payload : { excludeEndStatus: 0 },
      );
      if (response) {
        callback(response);
      }
    },
    // 查询渠道类型
    *channelType({ payload, callback }, { call, put }) {
      let response = yield call(channelType, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询营销事件
    *queryEventNameList({ payload, callback }, { call, put }) {
      let response = yield call(queryEventNameList, payload);
      if (response) {
        callback(response);
      }
    },
    *queryEventNameListAbTest({ payload, callback }, { call, put }) {
      let response = yield call(queryEventNameListAbTest, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询营销事件批次
    *queryEventBatchList({ payload, callback }, { call, put }) {
      let response = yield call(queryEventBatchList, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询营销详情tab
    *queryMarketingStatusSummary({ payload, callback }, { call, put }) {
      let response = yield call(queryMarketingStatusSummary, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询营销详情列表
    *queryMarketingDetailList({ payload, callback }, { call, put }) {
      let response = yield call(queryMarketingDetailList, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询营销详情列表页的详情
    *queryMarketingDetailById({ payload, callback }, { call, put }) {
      let response = yield call(queryMarketingDetailById, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询营销详情列表页的详情历史记录
    *queryMarketingDetailHis({ payload, callback }, { call, put }) {
      let response = yield call(queryMarketingDetailHis, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询营销结果列表
    *queryMarketingResultList({ payload, callback }, { call, put }) {
      let response = yield call(queryMarketingResultList, payload);
      if (response) {
        callback(response);
      }
    },
    // 导出客户清单
    *exportCustomerList({ payload, callback }, { call, put }) {
      let response = yield call(exportCustomerList, payload);
      const url = window.URL.createObjectURL(
        new Blob([response], {
          type:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        }),
      );
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      let timestamp = new Date().getTime();
      link.setAttribute(
        'download',
        getIntl().formatMessage({
          id: 'marketing.results.table.customer.list',
        }) +
          '_' +
          timestamp +
          '.xlsx',
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      // if (response) {
      //   callback(response);
      // }
    },
    // 查询雷达图，数据图和分析结果
    *queryMarketingAnalysisResult({ payload, callback }, { call, put }) {
      let response = yield call(queryMarketingAnalysisResult, payload);
      if (response) {
        callback(response);
      }
    },
  },
  reducers: {
    saveState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
