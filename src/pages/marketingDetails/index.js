import React, { Component } from 'react';
import { connect, FormattedMessage, history, getIntl } from 'umi';
import { Input, Button, Checkbox, Spin, Select, Tabs, Table } from 'antd';
import styles from './index.less';
import { SearchOutlined } from '@ant-design/icons';
import EmailMarketingIcon from '@/assets/email-marketing-icon.png';
import ExportCustomerIcon from '@/assets/export-customer-icon.png';
import { notification } from '../../utils/utils';
import EmailIcon from '@/assets/email.svg';
import FacebookIcon from '@/assets/facebook.svg';
import WhatsAppIcon from '@/assets/whats-app.svg';
import TwitterIcon from '@/assets/twitter.svg';
import LineIcon from '@/assets/line.svg';
import PhoneIcon from '@/assets/phone.svg';
import ChatIcon from '@/assets/chat-icon.jpg';
import AppChatOutlinedIcon from '@/assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '@/assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon from '@/assets/AppVideoOutlined.svg';
import TableEmailIcon from '@/assets/table-email-icon.png';
import TablePhoneIcon from '@/assets/table-phone-icon.png';
import TableInfoIcon from '@/assets/table-info-icon.png';
import HOCAuth from '@/components/HOCAuth/index';
import AwsChannelIcon from '@/assets/aws-channel-icon.svg';
import TikTokIcon from '@/assets/new-tiktok-icon.svg';
import DiscordIcon from '@/assets/new-discord-icon.svg';
import { ChannelTypeSelect } from '@/components/channelSelect'; // 导入通用组件
import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import NewFaceBookIcon from '@/assets/new-facebook-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';

const { Option } = Select;

class MarketingDetailsContent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loadingDetail: false,
      workOrderStatus: '',
      activityList: [],
      channelTypeList: [],
      eventNameList: [],
      eventBatchList: [],
      channelType: '',
      activityId: '',
      eventId: '',
      batchNum: '',
      marketingType: '',
      customerContactInfo: '',
      selectItem: {
        activityId: '',
        channelType: '',
        eventId: '',
        batchNum: '',
        marketingType: '',
        customerContactInfo: '',
        sendStatus: '',
      },
      sendStatus: '',
      total: 0,
      pageSize: 10,
      pageNum: 1,
      marketingDetailList: [],
      allNum: 0,
      sentNum: 0,
      deliveryNum: 0,
      openNum: 0,
      clickNum: 0,
      complaintNum: 0,
      unsubscribeNum: 0,
      bounceNum: 0,
      deliveryDelayNum: 0,
      rejectNum: 0,
      renderingFailureNum: 0,
    };
  }
  componentDidMount() {
    if (this.props.history.location.state) {
      let {
        channelType,
        marketingType,
        customerContactInfo,
        sendStatus,
        eventId,
        batchNum,
      } = this.state;
      let newActivityId = this.props.history.location.state.activityId;
      let newEventId = this.props.history.location.state.eventId;
      let newBatchNum = this.props.history.location.state.batchNum;
      if (newActivityId) {
        if (newEventId) {
          if (newBatchNum) {
            let params = {
              activityId: newActivityId,
              channelType: channelType,
              eventId: newEventId,
              batchNum: newBatchNum,
              marketingType: marketingType,
              customerContactInfo: customerContactInfo,
              sendStatus: sendStatus,
            };
            this.queryActivityList();
            this.queryChannelNameList();
            this.queryEventNameList(newActivityId);
            this.queryEventBatchList(newEventId);
            this.queryMarketingStatusSummary(params);
            this.queryMarketingDetailList(params);
            this.setState({
              activityId: newActivityId,
              eventId: newEventId,
              batchNum: newBatchNum,
            });
          } else {
            let params = {
              activityId: newActivityId,
              channelType: channelType,
              eventId: newEventId,
              batchNum: batchNum,
              marketingType: marketingType,
              customerContactInfo: customerContactInfo,
              sendStatus: sendStatus,
            };
            this.queryActivityList();
            this.queryChannelNameList();
            this.queryEventNameList(newActivityId);
            this.queryEventBatchList(newEventId);
            this.queryMarketingStatusSummary(params);
            this.queryMarketingDetailList(params);
            this.setState({
              activityId: newActivityId,
              eventId: newEventId,
            });
          }
        } else {
          let params = {
            activityId: newActivityId,
            channelType: channelType,
            eventId: eventId,
            batchNum: batchNum,
            marketingType: marketingType,
            customerContactInfo: customerContactInfo,
            sendStatus: sendStatus,
          };
          this.queryActivityList();
          this.queryChannelNameList();
          this.queryEventNameList(eventId);
          this.queryEventBatchList(batchNum);
          this.queryMarketingStatusSummary(params);
          this.queryMarketingDetailList(params);
          this.setState({
            activityId: newActivityId,
          });
        }
      }
    } else {
      let { selectItem } = this.state;
      this.queryActivityList();
      this.queryChannelNameList();
      this.queryEventNameList('');
      this.queryEventBatchList('');
      this.queryMarketingStatusSummary(selectItem);
      this.queryMarketingDetailList(selectItem);
    }
  }

  // 查询活动名称
  queryActivityList = () => {
    this.props.dispatch({
      type: 'statisticalResults/queryActivityList',
      payload: { excludeEndStatus: 0 },
      callback: response => {
        if (response.code == 200) {
          this.setState({
            activityList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询渠道类型
  queryChannelNameList = () => {
    this.props.dispatch({
      type: 'statisticalResults/channelType',
      payload: 1,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            channelTypeList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询营销事件
  queryEventNameList = params => {
    this.props.dispatch({
      type: 'statisticalResults/queryEventNameList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            eventNameList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询营销事件批次
  queryEventBatchList = params => {
    this.props.dispatch({
      type: 'statisticalResults/queryEventBatchList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            eventBatchList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询营销事件详情tab
  queryMarketingStatusSummary = selectItem => {
    this.setState({
      loadingDetail: true,
    });
    let { pageSize, pageNum } = this.state;
    let params = {
      selectItem: selectItem,
      pageSize: pageSize,
      pageNum: pageNum,
    };
    this.props.dispatch({
      type: 'statisticalResults/queryMarketingStatusSummary',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let data = response.data;
          if (data) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].statusCode == 0) {
                this.setState({
                  allNum: data[i].count,
                });
              } else if (data[i].statusCode == 1) {
                this.setState({
                  deliveryNum: data[i].count,
                });
              } else if (data[i].statusCode == 2) {
                this.setState({
                  openNum: data[i].count,
                });
              } else if (data[i].statusCode == 3) {
                this.setState({
                  clickNum: data[i].count,
                });
              } else if (data[i].statusCode == 4) {
                this.setState({
                  complaintNum: data[i].count,
                });
              } else if (data[i].statusCode == 5) {
                this.setState({
                  unsubscribeNum: data[i].count,
                });
              } else if (data[i].statusCode == 6) {
                this.setState({
                  bounceNum: data[i].count,
                });
              } else if (data[i].statusCode == 7) {
                this.setState({
                  deliveryDelayNum: data[i].count,
                });
              } else if (data[i].statusCode == 8) {
                this.setState({
                  rejectNum: data[i].count,
                });
              } else if (data[i].statusCode == 9) {
                this.setState({
                  renderingFailureNum: data[i].count,
                });
              } else if (data[i].statusCode == 10) {
                this.setState({
                  sentNum: data[i].count,
                });
              }
            }
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
        this.setState({
          loadingDetail: false,
        });
      },
    });
  };
  // 查询营销事件列表
  queryMarketingDetailList = selectItem => {
    this.setState({
      loadingDetail: true,
    });
    let { pageSize, pageNum } = this.state;
    let params = {
      selectItem: selectItem,
      pageSize: pageSize,
      pageNum: pageNum,
    };
    this.props.dispatch({
      type: 'statisticalResults/queryMarketingDetailList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let data = response.data;
          this.setState({
            marketingDetailList: data.rows,
            total: response.data.total,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
        this.setState({
          loadingDetail: false,
        });
      },
    });
  };

  // 切换活动名称
  handleChangeActivityName = value => {
    if (value !== undefined) {
      this.setState({
        activityId: value,
        eventId: '',
        batchNum: '',
      });
      this.queryEventNameList(value);
      this.queryEventBatchList('');
    } else {
      this.setState({
        activityId: '',
        eventId: '',
        batchNum: '',
      });
      this.queryEventNameList('');
      this.queryEventBatchList('');
    }
  };
  // 切换营销渠道
  handleChangeChannelType = value => {
    if (value !== undefined) {
      this.setState({
        channelType: value,
      });
    } else {
      this.setState({
        channelType: '',
      });
    }
  };
  // 切换营销事件
  handleChangeEventName = value => {
    if (value !== undefined) {
      this.setState({
        eventId: value,
        batchNum: '',
      });
      this.queryEventBatchList(value);
    } else {
      this.setState({
        eventId: '',
        batchNum: '',
      });
      this.queryEventBatchList('');
    }
  };
  // 切换营销事件批次
  handleChangeEventBatch = value => {
    if (value !== undefined) {
      this.setState({
        batchNum: value,
      });
    } else {
      this.setState({
        batchNum: '',
      });
    }
  };
  // 切换营销方式
  handleChangeMarketingType = value => {
    if (value !== undefined) {
      this.setState({
        marketingType: value,
      });
    } else {
      this.setState({
        marketingType: '',
      });
    }
  };
  // 联系方式取值
  handleChangeCustomerContactInfo = e => {
    this.setState({
      customerContactInfo: e.target.value,
    });
  };

  // 切换结果tab
  changeTabOrder = key => {
    let {
      activityId,
      channelType,
      eventId,
      batchNum,
      marketingType,
      customerContactInfo,
    } = this.state;
    this.setState(
      {
        pageNum: 1,
      },
      () => {
        if (key == 'all') {
          let params = {
            activityId: activityId,
            channelType: channelType,
            eventId: eventId,
            batchNum: batchNum,
            marketingType: marketingType,
            customerContactInfo: customerContactInfo,
            sendStatus: '',
          };
          this.queryMarketingStatusSummary(params);
          this.queryMarketingDetailList(params);

          this.setState({
            sendStatus: '',
            selectItem: params,
          });
        } else {
          let params = {
            activityId: activityId,
            channelType: channelType,
            eventId: eventId,
            batchNum: batchNum,
            marketingType: marketingType,
            customerContactInfo: customerContactInfo,
            sendStatus: key,
          };
          this.queryMarketingStatusSummary(params);
          this.queryMarketingDetailList(params);
          this.setState({
            sendStatus: key,
            selectItem: params,
          });
        }
      },
    );
  };
  // 点击按钮筛选
  handleSearch = () => {
    let {
      activityId,
      channelType,
      eventId,
      batchNum,
      marketingType,
      customerContactInfo,
      sendStatus,
    } = this.state;
    this.setState(
      {
        pageNum: 1,
      },
      () => {
        let params = {
          activityId: activityId,
          channelType: channelType,
          eventId: eventId,
          batchNum: batchNum,
          marketingType: marketingType,
          customerContactInfo: customerContactInfo,
          sendStatus: sendStatus,
        };
        this.setState({
          selectItem: params,
        });
        this.queryMarketingStatusSummary(params);
        this.queryMarketingDetailList(params);
      },
    );
  };
  // 导出客户清单
  handleExportCustomer = () => {
    let {
      activityId,
      channelType,
      eventId,
      batchNum,
      marketingType,
      customerContactInfo,
      sendStatus,
    } = this.state;
    let params = {
      activityId: activityId,
      eventId: eventId,
      batchNum: batchNum,
      channelType: channelType,
      sendStatus: sendStatus,
      marketingType: marketingType,
      customerContactInfo: customerContactInfo,
    };
    this.props.dispatch({
      type: 'statisticalResults/exportCustomerList',
      payload: params,
    });
  };

  onChangeStore = pagination => {
    let pageSize = pagination.pageSize;
    let pageNum = pagination.current;
    this.setState(
      {
        pageSize: pageSize,
        pageNum: pageNum,
      },
      () => {
        let selectItem = this.state.selectItem;
        // this.queryMarketingStatusSummary(selectItem);
        this.queryMarketingDetailList(selectItem);
      },
    );
  };

  jumpToDetail = detailId => {
    localStorage.setItem('detailId', detailId);
    history.push({
      pathname: 'marketingResultsDetails',
      state: {
        detailId: detailId,
      },
    });
  };

  render() {
    let {
      loadingDetail,
      total,
      pageSize,
      pageNum,
      workOrderStatus,
      activityList,
      channelTypeList,
      eventNameList,
      eventBatchList,
      batchNum,
      eventId,
      allNum,
      sentNum,
      deliveryNum,
      openNum,
      clickNum,
      complaintNum,
      unsubscribeNum,
      bounceNum,
      deliveryDelayNum,
      rejectNum,
      renderingFailureNum,
      marketingDetailList,
      activityId,
    } = this.state;

    const columns = [
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.activity.name',
          defaultValue: '活动名称',
        }),
        dataIndex: 'activityName',
        key: 'activityName',
        ellipsis: true,
        width: 180,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.channel.type',
          defaultValue: '营销渠道类型',
        }),
        dataIndex: 'channelType',
        key: 'channelType',
        render: (text, record) => {
          if (record.channelType) {
            if (record.channelType == '1') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={EmailMarketingIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.email"
                      defaultMessage="邮件"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '7') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={TablePhoneIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.phone"
                      defaultMessage="电弧"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '2') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={TableInfoIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.info"
                      defaultMessage="短信"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '3') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewFaceBookIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.facebook"
                      defaultMessage="Facebook"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '4') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={WhatsAppIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.whats.app"
                      defaultMessage="WhatsApp"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '8') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={ChatIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.chat"
                      defaultMessage="在线聊天"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '9') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={AppChatOutlinedIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.app.chat"
                      defaultMessage="App在线聊天"
                    />
                  </span>
                </div>
              );
            } else if (record.channelTypeId == '10') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={WebVideoOutlinedIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.web.video"
                      defaultMessage="Web在线视频"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '11') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={AppVideoOutlinedIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.app.video"
                      defaultMessage="App在线视频"
                    />
                  </span>
                </div>
              );
            }
          } else if (record.channelType == '12') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={AwsChannelIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.amazon.message"
                    defaultMessage="亚马逊站内信"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '13') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewInstagramIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.instagram"
                    defaultMessage="Instagram"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '14') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewLineIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.line"
                    defaultMessage="Line"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '15') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewWeComIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.weCom"
                    defaultMessage="微信客服"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '16') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewWechatOfficialAccountIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.weChat.official.account"
                    defaultMessage="微信公众号"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '17') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewWebOnlineVoiceIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.web.online.video"
                    defaultMessage="WEB在线语音"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '18') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewAppOnlineVoiceIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.app.online.video"
                    defaultMessage="APP在线语音"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '19') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewTwitterIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.twitter"
                    defaultMessage="Twitter"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '20') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewTelegramIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.telegram"
                    defaultMessage="Telegram"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '21') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewWeChatMiniProgramIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.weChat.mini.program"
                    defaultMessage="微信小程序"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '22') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewShopifyIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.shopify"
                    defaultMessage="Shopify"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '23') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={NewGooglePlayIcon} />
                <span>
                  <FormattedMessage
                    id="marketing.channel.type.google.play"
                    defaultMessage="Google Play"
                  />
                </span>
              </div>
            );
          } else if (record.channelType == '24') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={TikTokIcon} />
                <span>TikTok Shop</span>
              </div>
            );
          } else if (record.channelType == '25') {
            return (
              <div className={styles.marketingChannelTypeContent}>
                <img src={DiscordIcon} />
                <span>Discord</span>
              </div>
            );
          } else {
            return null;
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.send.channel',
          defaultValue: '发送渠道',
        }),
        dataIndex: 'channelConfigName',
        key: 'channelConfigName',
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.events',
          defaultValue: '营销事件',
        }),
        dataIndex: 'eventName',
        key: 'eventName',
        ellipsis: true,
        width: 180,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.event.batches',
          defaultValue: '营销事件批次',
        }),
        dataIndex: 'batchNum',
        key: 'batchNum',
        ellipsis: true,
        width: 150,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.methods',
          defaultValue: '营销方式',
        }),
        dataIndex: 'marketingType',
        key: 'marketingType',
        ellipsis: true,
        render: (text, record) => {
          if (record.marketingType == 1) {
            return (
              <span>
                <FormattedMessage
                  id="marketing.results.marketing.methods.1"
                  defaultMessage="标准测试"
                />
              </span>
            );
          } else if (record.marketingType == 2) {
            return (
              <span>
                <FormattedMessage
                  id="marketing.results.marketing.methods.2"
                  defaultMessage="A/B测试"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.test.type',
          defaultValue: 'A/B测试类型',
        }),
        dataIndex: 'eventDetailType',
        key: 'eventDetailType',
        ellipsis: true,
        render: (text, record) => {
          if (record.eventDetailType == 1) {
            return (
              <span>
                <FormattedMessage
                  id="marketing.results.marketing.methods.3"
                  defaultMessage="计划A"
                />
              </span>
            );
          } else if (record.eventDetailType == 2) {
            return (
              <span>
                <FormattedMessage
                  id="marketing.results.marketing.methods.4"
                  defaultMessage="计划B"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.customer.name',
          defaultValue: '客户名称',
        }),
        dataIndex: 'customerName',
        key: 'customerName',
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.customer.contact.information',
          defaultValue: '客户联系方式',
        }),
        dataIndex: 'customerContactInfo',
        key: 'customerContactInfo',
        width: 180,
        ellipsis: true,
      },
      // {
      //   title: getIntl().formatMessage({
      //     id: 'marketing.results.table.status',
      //     defaultValue: '状态',
      //   }),
      //   dataIndex: 'sendStatus',
      //   key: 'sendStatus',
      //   ellipsis: true,
      //   render: (text, record) => {
      //     if (record.sendStatus == 1) {
      //       return (
      //         <div className={styles.statusContent1}>
      //           <div className={styles.circle}></div>
      //           <span>
      //             <FormattedMessage
      //               id="event.notification.status.service"
      //               defaultMessage="送达"
      //             />
      //           </span>
      //         </div>
      //       );
      //     } else if (record.sendStatus == 2) {
      //       return (
      //         <div className={styles.statusContent2}>
      //           <div className={styles.circle}></div>
      //           <span>
      //             <FormattedMessage
      //               id="event.notification.status.read"
      //               defaultMessage="已读"
      //             />
      //           </span>
      //         </div>
      //       );
      //     } else if (record.sendStatus == 3) {
      //       return (
      //         <div className={styles.statusContent3}>
      //           <div className={styles.circle}></div>
      //           <span>
      //             <FormattedMessage
      //               id="event.notification.status.click"
      //               defaultMessage="点击"
      //             />
      //           </span>
      //         </div>
      //       );
      //     } else if (record.sendStatus == 4) {
      //       return (
      //         <div className={styles.statusContent4}>
      //           <div className={styles.circle}></div>
      //           <span>
      //             <FormattedMessage
      //               id="event.notification.status.subscribe"
      //               defaultMessage="订阅"
      //             />
      //           </span>
      //         </div>
      //       );
      //     } else if (record.sendStatus == 5) {
      //       return (
      //         <div className={styles.statusContent5}>
      //           <div className={styles.circle}></div>
      //           <span>
      //             <FormattedMessage
      //               id="event.notification.status.unsubscribe"
      //               defaultMessage="退订"
      //             />
      //           </span>
      //         </div>
      //       );
      //     } else if (record.sendStatus == 6) {
      //       return (
      //         <div className={styles.statusContent6}>
      //           <div className={styles.circle}></div>
      //           <span>
      //             <span>
      //               <FormattedMessage
      //                 id="event.notification.status.complaint"
      //                 defaultMessage="投诉"
      //               />
      //             </span>
      //           </span>
      //         </div>
      //       );
      //     } else if (record.sendStatus == 7) {
      //       return (
      //         <div className={styles.statusContent7}>
      //           <div className={styles.circle}></div>
      //           <span>
      //             <FormattedMessage
      //               id="event.notification.status.fail"
      //               defaultMessage="失败"
      //             />
      //           </span>
      //         </div>
      //       );
      //     } else if (record.sendStatus == 8) {
      //       return (
      //         <div className={styles.statusContent8}>
      //           <div className={styles.circle}></div>
      //           <span>
      //             <FormattedMessage
      //               id="event.notification.status.have.send"
      //               defaultMessage="已发送"
      //             />
      //           </span>
      //         </div>
      //       );
      //     }
      //   },
      // },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.have.send',
          defaultValue: '已发送',
        }),
        dataIndex: 'send',
        key: 'send',
        ellipsis: true,
        render: (text, record) => {
          if (record.send) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.service',
          defaultValue: '已送达',
        }),
        dataIndex: 'delivery',
        key: 'delivery',
        ellipsis: true,
        render: (text, record) => {
          if (record.delivery) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.read',
          defaultValue: '已读',
        }),
        dataIndex: 'open',
        key: 'open',
        ellipsis: true,
        render: (text, record) => {
          if (record.open) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.click',
          defaultValue: '已点击',
        }),
        dataIndex: 'click',
        key: 'click',
        ellipsis: true,
        render: (text, record) => {
          if (record.click) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.complaint',
          defaultValue: '投诉',
        }),
        dataIndex: 'complaint',
        key: 'complaint',
        ellipsis: true,
        render: (text, record) => {
          if (record.complaint) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.unsubscribe',
          defaultValue: '退订',
        }),
        dataIndex: 'unsubscription',
        key: 'unsubscription',
        ellipsis: true,
        render: (text, record) => {
          if (record.unsubscription) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.bounce',
          defaultValue: '退信',
        }),
        dataIndex: 'bounce',
        key: 'bounce',
        ellipsis: true,
        render: (text, record) => {
          if (record.bounce) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.delivery.delay',
          defaultValue: '延迟送达',
        }),
        dataIndex: 'deliveryDelay',
        key: 'deliveryDelay',
        ellipsis: true,
        render: (text, record) => {
          if (record.deliveryDelay) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.reject',
          defaultValue: '拒绝',
        }),
        dataIndex: 'reject',
        key: 'reject',
        ellipsis: true,
        render: (text, record) => {
          if (record.reject) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.fail',
          defaultValue: '呈现失败',
        }),
        dataIndex: 'renderingFailure',
        key: 'renderingFailure',
        ellipsis: true,
        render: (text, record) => {
          if (record.renderingFailure) {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.yes"
                  defaultMessage="是"
                />
              </span>
            );
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.failure.reason',
          defaultValue: '失败原因',
        }),
        dataIndex: 'error',
        key: 'error',
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.operation',
          defaultValue: '操作',
        }),
        dataIndex: 'operation',
        key: 'operation',
        fixed: 'right',
        ellipsis: true,
        width: 150,
        render: (text, record) => {
          return (
            <HOCAuth authKey={'marketing_details'}>
              {authAccess => (
                <div
                  className={`${styles.operationContent} ${
                    authAccess ? 'disabled' : ''
                  }`}
                  onClick={() => this.jumpToDetail(record.detailId)}
                >
                  <span>
                    <FormattedMessage
                      id="marketing.results.table.detail"
                      defaultMessage="详情"
                    />
                  </span>
                </div>
              )}
            </HOCAuth>
          );
        },
      },
    ];
    const ChildrenContent = () => (
      <div>
        <div className={styles.numberIcon}>{allNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="work.order.management.status.all"
            defaultMessage="全部"
          />
        </div>
      </div>
    );
    const ChildrenContent1 = () => (
      <div>
        <div className={styles.numberIcon}>{deliveryNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.service"
            defaultMessage="已送达"
          />
        </div>
      </div>
    );
    const ChildrenContent2 = () => (
      <div>
        <div className={styles.readNumberIcon}>{openNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.read"
            defaultMessage="已读"
          />
        </div>
      </div>
    );
    const ChildrenContent3 = () => (
      <div>
        <div className={styles.clickNumberIcon}>{clickNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.click"
            defaultMessage="已点击"
          />
        </div>
      </div>
    );
    const ChildrenContent4 = () => (
      <div>
        <div className={styles.terminatedNumberIcon}>{complaintNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.complaint"
            defaultMessage="投诉"
          />
        </div>
      </div>
    );
    const ChildrenContent5 = () => (
      <div>
        <div className={styles.unsubscribeNumberIcon}>{unsubscribeNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.unsubscribe"
            defaultMessage="退订"
          />
        </div>
      </div>
    );
    const ChildrenContent6 = () => (
      <div>
        <div className={styles.bounceNumIcon}>{bounceNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.bounce"
            defaultMessage="退信"
          />
        </div>
      </div>
    );
    const ChildrenContent9 = () => (
      <div>
        <div className={styles.deliveryDelayNumIcon}>{deliveryDelayNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.delivery.delay"
            defaultMessage="延迟送达"
          />
        </div>
      </div>
    );
    const ChildrenContent10 = () => (
      <div>
        <div className={styles.rejectNumIcon}>{rejectNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.reject"
            defaultMessage="拒绝"
          />
        </div>
      </div>
    );
    const ChildrenContent7 = () => (
      <div>
        <div className={styles.renderingFailureNumberIcon}>
          {renderingFailureNum}
        </div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.fail"
            defaultMessage="呈现失败"
          />
        </div>
      </div>
    );
    const ChildrenContent8 = () => (
      <div>
        <div className={styles.sentNumberIcon}>{sentNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.have.send"
            defaultMessage="已发送"
          />
        </div>
      </div>
    );
    const tabsItems = [
      {
        key: 'all',
        label: <ChildrenContent />,
      },
      {
        key: '10',
        label: <ChildrenContent8 />,
      },
      {
        key: '1',
        label: <ChildrenContent1 />,
      },
      {
        key: '2',
        label: <ChildrenContent2 />,
      },
      {
        key: '3',
        label: <ChildrenContent3 />,
      },
      {
        key: '4',
        label: <ChildrenContent4 />,
      },
      {
        key: '5',
        label: <ChildrenContent5 />,
      },
      {
        key: '6',
        label: <ChildrenContent6 />,
      },
      {
        key: '7',
        label: <ChildrenContent9 />,
      },
      {
        key: '8',
        label: <ChildrenContent10 />,
      },
      {
        key: '9',
        label: <ChildrenContent7 />,
      },
    ];

    return (
      <Spin spinning={loadingDetail}>
        <div className={styles.marketingDetailsContent}>
          <div className={styles.topSelectContent}>
            <div style={{ width: '100%', height: '50px', float: 'left' }}>
              <label>
                <FormattedMessage
                  id="marketing.activities.activity.name"
                  defaultMessage="活动名称："
                />
              </label>
              <Select
                value={activityId}
                showSearch
                allowClear={true}
                options={activityList}
                fieldNames={{
                  label: 'activityName',
                  value: 'activityId',
                  key: 'activityId',
                }}
                filterOption={(inputValue, option) =>
                  option.activityName
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.activity.name.placeholder',
                  defaultValue: '请选择活动名称',
                })}
                onChange={this.handleChangeActivityName}
              />
              <label>
                <FormattedMessage
                  id="marketing.results.marketing.channel.type"
                  defaultMessage="营销渠道类型："
                />
              </label>
              <ChannelTypeSelect
                popupClassName="selectFilterContent"
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.channel.type.placeholder',
                  defaultValue: '请选择营销渠道类型',
                })}
                channelTypeList={channelTypeList}
                onChange={this.handleChangeChannelType}
              />

              <label>
                <FormattedMessage
                  id="marketing.results.marketing.event.name"
                  defaultMessage="营销事件名称："
                />
              </label>
              <Select
                allowClear={true}
                value={eventId}
                options={eventNameList}
                showSearch
                fieldNames={{
                  label: 'eventName',
                  value: 'eventId',
                  key: 'eventId',
                }}
                filterOption={(inputValue, option) =>
                  option.eventName
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.event.name.placeholder',
                  defaultValue: '请选择营销事件名称',
                })}
                onChange={this.handleChangeEventName}
              />
            </div>
            <div style={{ width: '100%', height: '50px', float: 'left' }}>
              <label>
                <FormattedMessage
                  id="marketing.results.marketing.event.batches"
                  defaultMessage="营销事件批次："
                />
              </label>
              <Select
                allowClear={true}
                value={batchNum}
                options={eventBatchList}
                showSearch
                fieldNames={{
                  label: 'batchNum',
                  value: 'batchId',
                  key: 'batchId',
                }}
                filterOption={(inputValue, option) =>
                  option.batchNum
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.event.batches.placeholder',
                  defaultValue: '请输入营销事件批次',
                })}
                onChange={this.handleChangeEventBatch}
              />
              <label>
                <FormattedMessage
                  id="marketing.results.marketing.methods"
                  defaultMessage="营销方式："
                />
              </label>
              <Select
                allowClear={true}
                options={[
                  {
                    value: 1,
                    label: getIntl().formatMessage({
                      id: 'marketing.results.marketing.methods.1',
                      defaultValue: '标准测试',
                    }),
                  },
                  {
                    value: 2,
                    label: getIntl().formatMessage({
                      id: 'marketing.results.marketing.methods.2',
                      defaultValue: 'A/B测试',
                    }),
                  },
                ]}
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.methods.placeholder',
                  defaultValue: '请选择营销方式',
                })}
                onChange={this.handleChangeMarketingType}
              />
              <label>
                <FormattedMessage
                  id="marketing.results.contact.information"
                  defaultMessage="客户联系方式："
                />
              </label>
              <Input
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.contact.information.placeholder',
                  defaultValue: '请输入客户联系方式',
                })}
                onChange={this.handleChangeCustomerContactInfo}
              />
            </div>
            <Button
              onClick={this.handleSearch}
              type="primary"
              icon={<SearchOutlined />}
            >
              <FormattedMessage
                id="marketing.activities.search.btn"
                defaultMessage="筛选"
              />
            </Button>
          </div>
          <div className={styles.tableDetailContent}>
            <Tabs
              defaultActiveKey={workOrderStatus}
              items={tabsItems}
              onChange={key => this.changeTabOrder(key)}
            />
            <HOCAuth authKey={'export_marketing_details_customer_list'}>
              {authAccess => (
                <Button
                  onClick={this.handleExportCustomer}
                  type={'primary'}
                  className={styles.exportCustomerBtn}
                  disabled={authAccess}
                >
                  <FormattedMessage
                    id="marketing.results.table.export.customer"
                    defaultMessage="导出客户清单"
                  />
                </Button>
              )}
            </HOCAuth>
            <Table
              dataSource={marketingDetailList}
              columns={columns}
              scroll={{
                x: 2600,
              }}
              onChange={this.onChangeStore}
              pagination={{
                total: total,
                pageSize: pageSize,
                current: pageNum,
                showSizeChanger: true,
                pageSizeOptions: [10, 20, 50, 100],
                showTotal: total => (
                  <FormattedMessage
                    id="studentManagement.altogether"
                    defaultMessage={`共 ${total} 条`}
                    values={{ total }}
                  />
                ),
              }}
            />
          </div>
        </div>
      </Spin>
    );
  }
}

const mapStateToProps = ({ statisticalResults }) => {
  return {
    ...statisticalResults,
  };
};
export default connect(mapStateToProps)(MarketingDetailsContent);
