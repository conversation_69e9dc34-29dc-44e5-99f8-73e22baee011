import React, { useState, useRef, useEffect } from 'react';
import styles from './index.less';
import {
  Button,
  notification,
  Spin,
  Form,
  Row,
  Col,
  InputNumber,
  Checkbox,
  Upload,
} from 'antd';
import {
  useDispatch,
  getIntl,
  FormattedMessage,
  history,
  FormattedHTMLMessage,
} from 'umi';
import { Add, BackIcon, SortIcon } from '../autoCloseSetting/icon';
import UploadImg from '@/assets/upload-cloud.png';
import AudioPlayer from 'react-h5-audio-player';
import 'react-h5-audio-player/lib/styles.css';
import { DeleteIcon } from '../icon';

const { Dragger } = Upload;

const HoldMusicContent = () => {
  const dispatch = useDispatch();
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [carouselOpen, setCarouselOpen] = useState(true);
  const [fileList, setFileList] = useState({});
  const [audioUrl, setAudioUrl] = useState('');
  const audioTypes = [
    'audio/mpeg', // .mp3
    'audio/aac', // .aac
    'audio/ogg', // .ogg
    'audio/wav', // .wav
  ];
  const audioExts = ['.mp3', '.aac', '.ogg', '.wav'];

  useEffect(() => {
    queryTransferMusicConfig();
    formRef.current.setFieldsValue({ isLoop: carouselOpen });
  }, []);

  // 查询转人工等待音乐配置
  const queryTransferMusicConfig = () => {
    setLoading(true);
    dispatch({
      type: 'manualWaiting/queryTransferMusicConfig',
      callback: response => {
        setLoading(false);
        if (200 === response.code) {
          let data = response.data;
          let fileItem = {
            filePath: data.filePath,
            fileRealName: data.fileRealName,
            preSignedUrl: data.preSignedUrl,
          };
          setFileList(fileItem);
          setAudioUrl(data.preSignedUrl);
          setCarouselOpen(Boolean(data.isLoop));
          formRef.current.setFieldsValue(data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 取消
  const handleCancel = () => {
    history.go(-1);
    formRef.current.resetFields();
  };
  // 保存
  const onFinish = values => {
    setLoadingBtn(true);
    let params = {
      filePath: fileList.filePath,
      fileRealName: fileList.fileRealName,
      preSignedUrl: fileList.preSignedUrl,
      isLoop: values.isLoop ? 1 : 0,
      loopCount: values.loopCount ? values.loopCount : 1,
    };
    dispatch({
      type: 'manualWaiting/saveTransferMusicConfig',
      payload: params,
      callback: response => {
        setLoadingBtn(false);
        if (200 === response.code) {
          notification.success({
            message: response.msg,
          });
          queryTransferMusicConfig();
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 上传组件的相关属性配置
  const newProps = {
    name: 'file',
    multiple: false,
    showUploadList: false,
    accept: '.mp3,.aac,.ogg,.wav',
    // 上传前的校验
    beforeUpload: file => {
      const isAudio =
        audioTypes.includes(file.type) ||
        audioExts.some(ext => file.name.toLowerCase().endsWith(ext));
      if (!isAudio) {
        notification.warning(
          getIntl().formatMessage({ id: 'hold.music.content.upload.waring' }),
        );
      }
      let { size } = file;
      let fileSize = size / 1024 / 1024;
      if (fileSize > 10) {
        notification.error({
          message: getIntl().formatMessage(
            {
              id: 'message.upload.size',
            },
            {
              fileSize: 10,
            },
          ),
        });
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    // 自定义的上传接口
    customRequest: ({ file, onSuccess }) => {
      const fmData = new FormData();
      fmData.append('file', file);
      dispatch({
        type: 'manualWaiting/uploadTransferMusicFile',
        payload: fmData,
        callback: response => {
          if (200 === response.code) {
            setAudioUrl(response.data.preSignedUrl);
            setFileList(response.data);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    },
  };
  // 是否开启轮播
  const handleChangeCheckBox = e => {
    setCarouselOpen(e.target.checked);
  };
  return (
    <div className={styles.holdMusicContainer}>
      <Spin spinning={loading}>
        <div className={styles.headerContainer}>
          <div style={{ display: 'flex', fontSize: 18 }}>
            <span
              style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
              onClick={() => history.go(-1)}
            >
              {BackIcon()}
            </span>
            <FormattedMessage
              id="agentOperationSettings.config.title.4"
              defaultMessage="转人工等待音乐"
            />
          </div>
        </div>
        <div className={styles.contentContainer}>
          <Form
            name="basic"
            onFinish={onFinish}
            autoComplete="off"
            layout="vertical"
            ref={formRef}
          >
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="hold.music.content.waiting.prompt.sound"
                      defaultMessage="上传等待提示音"
                    />
                  }
                  name="filePath"
                  rules={[
                    {
                      required: true,
                      message: getIntl().formatMessage({
                        id: 'hold.music.content.waiting.prompt.sound.required',
                        defaultValue: '请上传等待提示音',
                      }),
                    },
                  ]}
                >
                  <Dragger {...newProps}>
                    <div className={styles.uploadContent}>
                      <img src={UploadImg} />
                      <p>
                        <FormattedHTMLMessage
                          id="hold.music.content.upload.tips"
                          defaultMessage="将文件拖拽到此处或点击上传，支持单个上传。目前支持mp3、AAC、OGG、WAV等格式文件。"
                          values={{ CH: '点击上传', US: ' click upload' }}
                        />
                      </p>
                    </div>
                  </Dragger>
                </Form.Item>
              </Col>
            </Row>
            {audioUrl && (
              <Row gutter={24}>
                <Col span={8}>
                  <div className={styles.audioContainer}>
                    <AudioPlayer
                      className={styles.customPlayer}
                      src={audioUrl}
                    />
                  </div>
                </Col>
              </Row>
            )}
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="hold.music.content.carousel"
                      defaultMessage="是否轮播"
                    />
                  }
                  name="isLoop"
                  valuePropName="checked"
                  rules={[{ required: false }]}
                >
                  <Checkbox onChange={handleChangeCheckBox}>
                    <FormattedMessage
                      id="chat.channel.configuration.work.panels.checkbox"
                      defaultMessage="开启"
                    />
                  </Checkbox>
                </Form.Item>
              </Col>
            </Row>
            {carouselOpen && (
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    label={
                      <FormattedMessage
                        id="hold.music.content.carousel.num"
                        defaultMessage="轮播次数"
                      />
                    }
                    name="loopCount"
                    rules={[
                      {
                        required: true,
                        message: getIntl().formatMessage({
                          id: 'hold.music.content.carousel.num.placeholder.1',
                          defaultValue: '轮播数不能为空',
                        }),
                      },
                      {
                        type: 'number',
                        min: 1,
                        max: 10,
                        message: getIntl().formatMessage({
                          id: 'hold.music.content.carousel.num.placeholder',
                          defaultValue: '请输入1～10之间的整数',
                        }),
                        transform: value =>
                          value === '' ? undefined : Number(value),
                      },
                    ]}
                  >
                    <InputNumber
                      min={1}
                      max={10}
                      controls={false}
                      precision={0}
                      placeholder={getIntl().formatMessage({
                        id: 'hold.music.content.carousel.num.placeholder',
                        defaultValue: '请输入1～10之间的整数',
                      })}
                    />
                  </Form.Item>
                  <p className={styles.tipsText}>
                    <FormattedMessage
                      id="hold.music.content.carousel.tips"
                      defaultMessage="备注：等待转人工音乐轮播结束后自动挂断电话"
                    ></FormattedMessage>
                  </p>
                </Col>
              </Row>
            )}

            <Form.Item
              label={null}
              style={{ position: 'absolute', right: '0px', top: '0px' }}
            >
              <Button onClick={handleCancel} style={{ marginRight: '10px' }}>
                <FormattedMessage
                  id="work.order.management.btn.cancel"
                  defaultMessage="取消"
                />
              </Button>
              <Button type="primary" htmlType="submit" loading={loadingBtn}>
                <FormattedMessage
                  id="customerInformation.add.basicInformation.button.save"
                  defaultValue="保存"
                />
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Spin>
    </div>
  );
};

export default HoldMusicContent;
