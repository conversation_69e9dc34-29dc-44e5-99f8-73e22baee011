.holdMusicContainer {
  margin: 20px;
  overflow: hidden;
  overflow-y: scroll;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;

  position: relative;

  .headerContainer {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;

    .btnBox {
      float: right;
    }
  }
  .contentContainer {
    width: 100%;
    margin-top: 20px;
    .uploadContent {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 60px;
      img {
        width: 64px;
      }
      p {
        color: #999;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px; /* 150% */
      }
    }
    .tipsText {
      color: #999;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 14px; /* 116.667% */
      margin-top: -10px;
    }

    :global {
      .ant-input-number {
        width: 100% !important;
        font-size: 12px;
        border-radius: 6px !important;
        background: #fff;
        box-shadow: none;
      }
      .ant-input-number-input,
      .ant-input-number-input-wrap {
        background: #fff;
        border-radius: 6px !important;
      }
      .ant-upload.ant-upload-drag {
        background: #fff;
      }
      .ant-form-item-label
        > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        display: none;
      }
      .ant-form-item-label
        > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
      .ant-form-item-label > label {
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 14px; /* 116.667% */
      }
    }

    .audioContainer {
      width: 100%;
      height: 60px;
      padding: 10px 10px;
      border-radius: 30px;
      border: 1px solid #e6e6e6;
      background: #fff;
      /* 阴影 */
      box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
      span {
        cursor: pointer;
      }
    }
    .customPlayer {
      :global {
        .rhap_additional-controls,
        .rhap_volume-controls,
        .rhap_rewind-button,
        .rhap_forward-button {
          display: none;
        }
        .rhap_main {
          flex-direction: row-reverse;
          flex: 1 1;
        }
        .rhap_controls-section {
          flex: none;
        }
        .rhap_stacked .rhap_controls-section {
          margin-top: 0px;
          margin-right: 5px;
        }
        .rhap_progress-bar-show-download {
          background-color: #f4f7fd;
        }
        .rhap_download-progress {
          background-color: #f4f7fd;
        }
        .rhap_progress-filled {
          background-color: #3463fc;
        }
        .rhap_progress-indicator {
          background: #3463fc;
        }
        .rhap_time {
          color: #999;
          font-size: 12px;
        }
        .rhap_main-controls-button {
          color: #3463fc;
        }
        //svg:not(:root){
        //  width: 30px;
        //  height: 30px;
        //}
      }
    }
    :global {
      .rhap_container {
        padding: 0px 5px;
        background-color: inherit;
        box-shadow: none;
        //width: 90%;
      }
    }
  }
}
.holdMusicContainer::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}
