import React, { Component } from 'react';
import { connect, FormattedMessage, getIntl, history } from 'umi';
import { Input, Button, Checkbox, Select, Spin, Table } from 'antd';
import styles from './index.less';
import { SearchOutlined } from '@ant-design/icons';
import { ChannelTypeSelect } from '@/components/channelSelect'; // 导入通用组件
import ExportCustomerIcon from '@/assets/export-customer-icon.png';
import EmailMarketingIcon from '@/assets/email-marketing-icon.png';
import { notification } from '../../utils/utils';
import TablePhoneIcon from '@/assets/table-phone-icon.png';
import TableInfoIcon from '@/assets/table-info-icon.png';
import WhatsAppIcon from '@/assets/whats-app.svg';
import TikTokIcon from '@/assets/new-tiktok-icon.svg';
import DiscordIcon from '@/assets/new-discord-icon.svg';
import ChatIcon from '@/assets/chat-icon.jpg';
import AppChatOutlinedIcon from '@/assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '@/assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon from '@/assets/AppVideoOutlined.svg';
import EmailIcon from '@/assets/email.svg';
import FacebookIcon from '@/assets/facebook.svg';
import TwitterIcon from '@/assets/twitter.svg';
import LineIcon from '@/assets/line.svg';
import PhoneIcon from '@/assets/phone.svg';
import { pinyin } from 'pinyin-pro';
import HOCAuth from '@/components/HOCAuth/index';
import AwsChannelIcon from '@/assets/aws-channel-icon.svg';

import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import NewFaceBookIcon from '@/assets/new-facebook-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';

class MarketingResultsContent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loadingDetail: false,
      total: '',
      pageSize: 10,
      pageNum: 1,
      selectItem: {
        activityId: '',
        channelType: '',
        eventId: '',
      },
      marketingResultList: [],
    };
  }
  componentDidMount() {
    let { selectItem } = this.state;
    if (this.props.history.location.state) {
      let activityId = this.props.history.location.state.activityId;
      let newEventId = this.props.history.location.state.eventId;
      if (activityId) {
        if (newEventId) {
          this.setState(
            {
              activityId: activityId,
              eventId: newEventId,
            },
            () => {
              selectItem.activityId = activityId;
              selectItem.eventId = newEventId;
              this.queryMarketingResultList(selectItem);
              this.queryEventNameList(activityId);
            },
          );
        } else {
          this.setState(
            {
              activityId: activityId,
            },
            () => {
              selectItem.activityId = activityId;
              this.queryMarketingResultList(selectItem);
              this.queryEventNameList(activityId);
            },
          );
        }
      }
    } else {
      this.queryMarketingResultList(selectItem);
      this.queryEventNameList('');
    }
    this.queryActivityList();
    this.queryChannelNameList();
  }

  // 查询营销结果列表
  queryMarketingResultList = selectItem => {
    let { pageNum, pageSize } = this.state;
    let params = {
      selectItem: selectItem,
      pageNum: pageNum,
      pageSize: pageSize,
    };
    this.props.dispatch({
      type: 'statisticalResults/queryMarketingResultList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            total: response.data.total,
            marketingResultList: response.data.rows,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询活动名称
  queryActivityList = () => {
    this.props.dispatch({
      type: 'statisticalResults/queryActivityList',
      payload: { excludeEndStatus: 0 },
      callback: response => {
        if (response.code == 200) {
          this.setState({
            activityList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询渠道类型
  queryChannelNameList = () => {
    this.props.dispatch({
      type: 'statisticalResults/channelType',
      payload: 1,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            channelTypeList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询营销事件
  queryEventNameList = params => {
    this.props.dispatch({
      type: 'statisticalResults/queryEventNameList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            eventNameList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 切换活动名称
  handleChangeActivityName = value => {
    if (value !== undefined) {
      this.setState({
        activityId: value,
        eventId: '',
        batchId: '',
      });
      this.queryEventNameList(value);
    } else {
      this.setState({
        activityId: '',
        eventId: '',
        batchId: '',
      });
      this.queryEventNameList('');
    }
  };
  // 切换营销渠道
  handleChangeChannelType = value => {
    if (value !== undefined) {
      this.setState({
        channelType: value,
      });
    } else {
      this.setState({
        channelType: '',
      });
    }
  };
  // 切换营销事件
  handleChangeEventName = value => {
    if (value !== undefined) {
      this.setState({
        eventId: value,
      });
    } else {
      this.setState({
        eventId: '',
      });
    }
  };
  // 点击按钮筛选
  handleSearch = () => {
    let { activityId, channelType, eventId } = this.state;
    this.setState(
      {
        pageNum: 1,
      },
      () => {
        let params = {
          activityId: activityId,
          channelType: channelType,
          eventId: eventId,
        };
        this.setState({
          selectItem: params,
        });
        this.queryMarketingResultList(params);
      },
    );
  };
  // 导出客户清单
  handleExportCustomer = record => {
    console.log(record);
    let params = {
      activityId: record.activityId,
      eventId: record.eventId,
      eventName: record.eventName,
      batchNum: record.batchId,
      eventDetailType: record.eventDetailType,
    };
    this.props.dispatch({
      type: 'statisticalResults/exportCustomerList',
      payload: params,
    });
  };
  // 跳转营销详情页面
  handleJumpToMarketingDetail = record => {
    history.push({
      pathname: 'marketingDetails',
      state: {
        activityId: record.activityId,
        eventId: record.eventId,
        batchNum: record.batchId,
      },
    });
  };

  onChangeStore = pagination => {
    let pageSize = pagination.pageSize;
    let pageNum = pagination.current;
    this.setState(
      {
        pageSize: pageSize,
        pageNum: pageNum,
      },
      () => {
        let { selectItem } = this.state;
        this.queryMarketingResultList(selectItem);
      },
    );
  };

  render() {
    let {
      loadingDetail,
      total,
      pageSize,
      pageNum,
      marketingResultList,
      activityList,
      eventNameList,
      eventId,
      channelTypeList,
      activityId,
    } = this.state;
    const columns = [
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.activity.name',
          defaultValue: '活动名称',
        }),
        dataIndex: 'activityName',
        key: 'activityName',
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.channel.type',
          defaultValue: '营销渠道类型',
        }),
        dataIndex: 'channelType',
        key: 'channelType',
        width: 150,
        sorter: (a, b) => a.channelType - b.channelType,
        render: (text, record) => {
          if (record.channelType) {
            if (record.channelType == '1') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={EmailMarketingIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.email"
                      defaultMessage="邮件"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '7') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={TablePhoneIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.phone"
                      defaultMessage="电弧"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '2') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={TableInfoIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.info"
                      defaultMessage="短信"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '3') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewFaceBookIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.facebook"
                      defaultMessage="Facebook"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '4') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={WhatsAppIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.whats.app"
                      defaultMessage="WhatsApp"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '8') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={ChatIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.chat"
                      defaultMessage="在线聊天"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '9') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={AppChatOutlinedIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.app.chat"
                      defaultMessage="App在线聊天"
                    />
                  </span>
                </div>
              );
            } else if (record.channelTypeId == '10') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={WebVideoOutlinedIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.web.video"
                      defaultMessage="Web在线视频"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '11') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={AppVideoOutlinedIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.app.video"
                      defaultMessage="App在线视频"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '12') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={AwsChannelIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.amazon.message"
                      defaultMessage="亚马逊站内信"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '13') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewInstagramIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.instagram"
                      defaultMessage="Instagram"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '14') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewLineIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.line"
                      defaultMessage="Line"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '15') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewWeComIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.weCom"
                      defaultMessage="微信客服"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '16') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewWechatOfficialAccountIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.weChat.official.account"
                      defaultMessage="微信公众号"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '17') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewWebOnlineVoiceIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.web.online.video"
                      defaultMessage="WEB在线语音"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '18') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewAppOnlineVoiceIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.app.online.video"
                      defaultMessage="APP在线语音"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '19') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewTwitterIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.twitter"
                      defaultMessage="Twitter"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '20') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewTelegramIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.telegram"
                      defaultMessage="Telegram"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '21') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewWeChatMiniProgramIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.weChat.mini.program"
                      defaultMessage="微信小程序"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '22') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewShopifyIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.shopify"
                      defaultMessage="Shopify"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '23') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={NewGooglePlayIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.channel.type.google.play"
                      defaultMessage="Google Play"
                    />
                  </span>
                </div>
              );
            } else if (record.channelType == '24') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={TikTokIcon} />
                  <span>TikTok Shop</span>
                </div>
              );
            } else if (record.channelType == '25') {
              return (
                <div className={styles.marketingChannelTypeContent}>
                  <img src={DiscordIcon} />
                  <span>Discord</span>
                </div>
              );
            } else {
              return null;
            }
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.send.channel',
          defaultValue: '发送渠道',
        }),
        dataIndex: 'channelConfigName',
        key: 'channelConfigName',
        ellipsis: true,
        width: 150,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.events',
          defaultValue: '营销事件',
        }),
        dataIndex: 'eventName',
        key: 'eventName',
        ellipsis: true,
        sorter: (a, b) => {
          if (a.eventName) {
            if (b.eventName) {
              let aPinyinArray = pinyin(a.eventName, {
                pattern: 'first',
                toneType: 'none',
                type: 'array',
              });
              let bPinyinArray = pinyin(b.eventName, {
                pattern: 'first',
                toneType: 'none',
                type: 'array',
              });
              return aPinyinArray[0].localeCompare(bPinyinArray[0]);
            } else {
              let aPinyinArray = pinyin(a.eventName, {
                pattern: 'first',
                toneType: 'none',
                type: 'array',
              });
              let bPinyinArray = ['z'];
              return aPinyinArray[0].localeCompare(bPinyinArray[0]);
            }
          } else {
            if (b.eventName) {
              let aPinyinArray = ['z'];
              let bPinyinArray = pinyin(b.eventName, {
                pattern: 'first',
                toneType: 'none',
                type: 'array',
              });
              return aPinyinArray[0].localeCompare(bPinyinArray[0]);
            } else {
              let aPinyinArray = ['z'];
              let bPinyinArray = ['z'];
              return aPinyinArray[0].localeCompare(bPinyinArray[0]);
            }
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.methods',
          defaultValue: '营销方式',
        }),
        dataIndex: 'marketingType',
        key: 'marketingType',
        ellipsis: true,
        sorter: (a, b) => a.marketingType - b.marketingType,
        render: (text, record) => {
          if (record.marketingType == 1) {
            return (
              <span>
                <FormattedMessage
                  id="marketing.results.marketing.methods.1"
                  defaultMessage="标准测试"
                />
              </span>
            );
          } else if (record.marketingType == 2) {
            return (
              <span>
                <FormattedMessage
                  id="marketing.results.marketing.methods.2"
                  defaultMessage="A/B测试"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.test.type',
          defaultValue: 'A/B测试类型',
        }),
        dataIndex: 'eventDetailType',
        key: 'eventDetailType',
        sorter: (a, b) => a.eventDetailType - b.eventDetailType,
        ellipsis: true,
        render: (text, record) => {
          if (record.eventDetailType == 1) {
            return (
              <span>
                <FormattedMessage
                  id="marketing.results.marketing.methods.3"
                  defaultMessage="计划A"
                />
              </span>
            );
          } else if (record.eventDetailType == 2) {
            return (
              <span>
                <FormattedMessage
                  id="marketing.results.marketing.methods.4"
                  defaultMessage="计划B"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.event.batches',
          defaultValue: '营销事件批次',
        }),
        dataIndex: 'batchNum',
        key: 'batchNum',
        sorter: (a, b) => a.batchNum - b.batchNum,
        ellipsis: true,
        render: (text, record) => {
          return (
            <a onClick={() => this.handleJumpToMarketingDetail(record)}>
              {text}
            </a>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.number.customers',
          defaultValue: '客户总数',
        }),
        dataIndex: 'customerTotalCount',
        key: 'customerTotalCount',
        sorter: (a, b) => a.customerTotalCount - b.customerTotalCount,
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.delivery.rate',
          defaultValue: '送达率',
        }),
        dataIndex: 'deliveryPercent',
        key: 'deliveryPercent',
        sorter: (a, b) => a.deliveryPercent - b.deliveryPercent,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div className={styles.deliveryRate}>
              <span>{text}%</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.read.rate',
          defaultValue: '已读率',
        }),
        dataIndex: 'openPercent',
        key: 'openPercent',
        sorter: (a, b) => a.openPercent - b.openPercent,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div className={styles.readRate}>
              <span>{text}%</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.click.rate',
          defaultValue: '点击率',
        }),
        dataIndex: 'clickPercent',
        key: 'clickPercent',
        sorter: (a, b) => a.clickPercent - b.clickPercent,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div className={styles.clickRate}>
              <span>{text}%</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.complaint.rate',
          defaultValue: '投诉率',
        }),
        dataIndex: 'complainPercent',
        key: 'complainPercent',
        sorter: (a, b) => a.complainPercent - b.complainPercent,
        // width: 180,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div className={styles.complaintRate}>
              <span>{text}%</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.unsubscribe.rate',
          defaultValue: '退订率',
        }),
        dataIndex: 'unsubscribePercent',
        key: 'unsubscribePercent',
        sorter: (a, b) => a.unsubscribePercent - b.unsubscribePercent,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div className={styles.unsubscribeRate}>
              <span>{text}%</span>
            </div>
          );
        },
      },

      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.bounce.rate',
          defaultValue: '退信率',
        }),
        dataIndex: 'bouncePercent',
        key: 'bouncePercent',
        sorter: (a, b) => a.bouncePercent - b.bouncePercent,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div className={styles.bounceRate}>
              <span>{text}%</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.delivery.delay.rate',
          defaultValue: '延迟送达率',
        }),
        dataIndex: 'deliveryDelayPercent',
        key: 'deliveryDelayPercent',
        sorter: (a, b) => a.deliveryDelayPercent - b.deliveryDelayPercent,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div className={styles.deliveryDelayRate}>
              <span>{text}%</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.reject.rate',
          defaultValue: '拒绝率',
        }),
        dataIndex: 'rejectPercent',
        key: 'rejectPercent',
        sorter: (a, b) => a.rejectPercent - b.rejectPercent,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div>
              <span>{text}%</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.rendering.failure.rate',
          defaultValue: '呈现失败率',
        }),
        dataIndex: 'failurePercent',
        key: 'failurePercent',
        sorter: (a, b) => a.failurePercent - b.failurePercent,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div>
              <span>{text}%</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.operation',
          defaultValue: '操作',
        }),
        dataIndex: 'operation',
        key: 'operation',
        fixed: 'right',
        ellipsis: true,
        width: 180,
        render: (text, record) => {
          return (
            <HOCAuth authKey={'export_marketing_customer_list'}>
              {authAccess => (
                <div
                  onClick={() => this.handleExportCustomer(record)}
                  className={`${styles.operationContent} ${
                    authAccess ? 'disabled' : ''
                  }`}
                >
                  <img src={ExportCustomerIcon} />
                  <span>
                    <FormattedMessage
                      id="marketing.results.table.export.customer"
                      defaultMessage="导出客户清单"
                    />
                  </span>
                </div>
              )}
            </HOCAuth>
          );
        },
      },
    ];

    return (
      <Spin spinning={loadingDetail}>
        <div className={styles.marketingResults}>
          <div className={styles.selectMarketingResults}>
            <div className={styles.selectContent}>
              <label>
                <FormattedMessage
                  id="marketing.activities.activity.name"
                  defaultMessage="活动名称："
                />
              </label>
              <Select
                value={activityId}
                showSearch
                allowClear={true}
                options={activityList}
                fieldNames={{
                  label: 'activityName',
                  value: 'activityId',
                  key: 'activityId',
                }}
                filterOption={(inputValue, option) =>
                  option.activityName
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                onChange={this.handleChangeActivityName}
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.activity.name.placeholder',
                  defaultValue: '请选择活动名称',
                })}
              />
              <label>
                <FormattedMessage
                  id="marketing.results.marketing.channel.type"
                  defaultMessage="营销渠道类型："
                />
              </label>
              <ChannelTypeSelect
                // value={channelType}
                onChange={this.handleChangeChannelType}
                channelTypeList={channelTypeList}
                popupClassName="selectFilterContent"
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.channel.type.placeholder',
                  defaultValue: '请选择营销渠道类型',
                })}
              />
              {/* <Select
                allowClear={true}
                popupClassName="selectFilterContent"
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.channel.type.placeholder',
                  defaultValue: '请选择营销渠道类型',
                })}
                onChange={this.handleChangeChannelType}
              >
                {channelTypeList?.map(items => {
                  if (items.code == '1') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={EmailIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '3') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={FacebookIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '4') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={WhatsAppIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '5') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={TwitterIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '6') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={LineIcon} /> {items.name}
                      </Option>
                    );
                  } else if (items.code == '7') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={PhoneIcon} /> {items.name}
                      </Option>
                    );
                  } else if (items.code == '8') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={ChatIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '9') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={AppChatOutlinedIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '10') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={WebVideoOutlinedIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '11') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={AppVideoOutlinedIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '12') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={AwsChannelIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '13') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewInstagramIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '14') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewLineIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '15') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewWeComIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '16') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewWechatOfficialAccountIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '17') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewWebOnlineVoiceIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '18') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewAppOnlineVoiceIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '19') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewTwitterIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '20') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewTelegramIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '21') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewWeChatMiniProgramIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '22') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewShopifyIcon} />
                        {items.name}
                      </Option>
                    );
                  } else if (items.code == '23') {
                    return (
                      <Option value={items.code} key={items.code}>
                        <img src={NewGooglePlayIcon} />
                        {items.name}
                      </Option>
                    );
                  } else {
                    return (
                      <Option value={items.code} key={items.code}>
                        {items.name}
                      </Option>
                    );
                  }
                })}
              </Select> */}

              <label>
                <FormattedMessage
                  id="marketing.results.marketing.event.name"
                  defaultMessage="营销事件名称："
                />
              </label>
              <Select
                allowClear={true}
                value={eventId}
                options={eventNameList}
                showSearch
                fieldNames={{
                  label: 'eventName',
                  value: 'eventId',
                  key: 'eventId',
                }}
                filterOption={(inputValue, option) =>
                  option.eventName
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.event.name.placeholder',
                  defaultValue: '请选择营销事件名称',
                })}
                onChange={this.handleChangeEventName}
              />
            </div>
            <Button
              onClick={this.handleSearch}
              type="primary"
              icon={<SearchOutlined />}
            >
              <FormattedMessage
                id="marketing.activities.search.btn"
                defaultMessage="筛选"
              />
            </Button>
          </div>
          <div className={styles.marketingResultsTableContent}>
            <Table
              dataSource={marketingResultList}
              columns={columns}
              scroll={{
                x: 2000,
              }}
              pagination={{
                total: total,
                pageSize: pageSize,
                current: pageNum,
                showSizeChanger: true,
                pageSizeOptions: [10, 20, 50, 100],
                showTotal: total => (
                  <FormattedMessage
                    id="studentManagement.altogether"
                    defaultMessage={`共 ${total} 条`}
                    values={{ total }}
                  />
                ),
              }}
              onChange={this.onChangeStore}
            />
          </div>
        </div>
      </Spin>
    );
  }
}

const mapStateToProps = ({ statisticalResults }) => {
  return {
    ...statisticalResults,
  };
};
export default connect(mapStateToProps)(MarketingResultsContent);
