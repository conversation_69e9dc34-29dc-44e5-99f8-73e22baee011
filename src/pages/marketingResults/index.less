.marketingResults {
  margin: 20px 20px 0px 20px;
  height: 88vh;
  overflow: hidden;
  overflow-y: scroll;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;

  .selectMarketingResults {
    width: 100%;
    height: 124px;
    padding: 20px;
    background: #ffffff;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
    border-radius: 4px 4px 4px 4px;

    .selectContent {
      width: 100%;
      height: 32px;
      margin-bottom: 20px;
      label {
        font-size: 12px;
        color: #333333;
        float: left;
        margin-right: 4px;
        width: 90px;
        line-height: 30px;
        text-align: right;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      :global {
        .ant-select {
          width: 23%;
          float: left;
          background: #ffffff;
          margin-right: 20px;
          font-size: 12px;
        }
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          background: #ffffff;
          border-radius: 6px 6px 6px 6px;
          //border: 1px solid #e6e6e6;
          box-shadow: none;
        }
        .ant-select-selection-item {
          img {
            width: 16px;
            height: 16px;
            margin-right: 3px;
            margin-top: -2px;
          }
        }
      }
    }
    :global {
      .ant-btn {
        float: right;
        margin-right: 0.6%;
      }
      .ant-btn .anticon {
        margin-right: 4px;
      }
    }
  }
  .marketingResultsTableContent {
    width: 100%;
    min-height: 67vh;
    background: #ffffff;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
    border-radius: 4px 4px 4px 4px;
    padding: 20px;
    margin-top: 20px;

    .marketingResultsTableContent {
      :global {
        .ant-table-thead > tr > th {
          color: #000000;
        }
        .ant-table-tbody > tr > td {
          color: #333333;
        }
      }
    }
  }

  .operationContent {
    cursor: pointer;
    img {
      width: 16px;
      float: left;
      margin-top: 1px;
      margin-right: 3px;
    }
    span {
      font-size: 12px;
      color: #3463fc;
    }
  }
  .marketingChannelTypeContent {
    img {
      width: 16px;
      float: left;
      margin-top: 1px;
      margin-right: 3px;
    }
    span {
      font-size: 12px;
      color: #333333;
    }
  }
  .deliveryRate {
    span {
      font-size: 12px;
      color: #3463fc;
    }
  }
  .readRate {
    span {
      font-size: 12px;
      color: #8500bb;
    }
  }
  .clickRate {
    span {
      font-size: 12px;
      color: #64b9ff;
    }
  }
  .subscriptionRate {
    span {
      font-size: 12px;
      color: #13c825;
    }
  }
  .unsubscribeRate {
    span {
      font-size: 12px;
      color: #ffa500;
    }
  }
  .complaintRate {
    span {
      font-size: 12px;
      color: #f22417;
    }
  }
  .bounceRate {
    span {
      font-size: 12px;
      color: #d3d30d;
    }
  }
  .deliveryDelayRate {
    span {
      font-size: 12px;
      color: #07d1d1;
    }
  }
}
.marketingResults::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}
