import React, { Component } from 'react';
import { connect, FormattedMessage, getIntl, history } from 'umi';
import {
  Input,
  Button,
  Checkbox,
  Badge,
  Calendar,
  Modal,
  Row,
  Col,
  Form,
  Select,
  DatePicker,
  Tag,
  Tooltip,
  Timeline,
  Spin,
} from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import styles from './index.less';
import moment from 'moment';
import { notification } from '@/utils/utils';

import EmailMarketingIcon from '../../../assets/email-marketing-icon.png';
import WhatsAppMarketingIcon from '../../../assets/whatapp-marketing-icon.png';
import OtherMarketingIcon from '../../../assets/other-marketing-icon.png';
import SwitchLeftIcon from '../../../assets/switch-left-icon.png';
import SWitchRightIcon from '../../../assets/switch-right-icon.png';
import WhAtsAppMarketingIcon from '../../../assets/whatapp-marketing-icon.png';
import TableInfoIcon from '../../../assets/table-info-icon.png';
import MixEventIcon from '../../../assets/mix-event-icon.png';
import HOCAuth from '@/components/HOCAuth/index';

const { TextArea } = Input;

class ActivityCalendar extends Component {
  formReplayRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      dataList: [],
      isModalOpen: false,
      isModalOpen1: false,
      loadingBtn: false,
      loadingDetail: false,
      marketingTypeNum: 0,
      createEventList: [],
      // 通知邮箱
      emailInputValue: '',
      emailTags: [],
      activityItemNum: 0,
      openActivityListValue: true,
      selectItem: {
        /**
         * 活动名称
         */
        activityName: '',
        /**
         * 活动状态（1-未开始 2-进行中 3-已暂停 4-已结束 5-已取消）
         */
        activityStatus: '2',
        /**
         * 活动开始时间
         */
        activityStartTime: null,
        /**
         * 活动结束时间
         */
        activityEndTime: null,
        /**
         * 菜单类型 1、我的活动 2、活动日历 3、所有活动
         */
        menuType: 2,
      },
      marketingActivityList: [],
      activityTotalStartTime: '',
      isPanelChange: true,
      newDataList: [],
      activityId: '',
      channelList: [],
      activityStatusValue: '2',
      // activityInitStartTime:'',
      // activityInitEndTime:'',
    };
  }
  componentDidMount() {
    if (this.props.history.location.state) {
      let newId = this.props.history.location.state.activityId;
      this.setState(
        {
          activityId: newId,
        },
        () => {
          this.queryMarketing();
        },
      );
    } else {
      this.queryMarketing();
    }
    this.querySESChannel();
  }

  // 营销渠道下拉列表
  querySESChannel = () => {
    this.props.dispatch({
      type: 'emailMarketing/querySESChannel',
      callback: response => {
        if (response.code == 200) {
          this.setState({
            channelList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询营销活动列表
  queryMarketing = () => {
    this.setState({
      loadingDetail: true,
    });
    let { selectItem, activityId } = this.state;
    this.props.dispatch({
      type: 'marketingActivities/queryMarketing1',
      payload: selectItem,
      callback: response => {
        if (response.code == 200) {
          let list = response.data.records;
          if (list.length > 0) {
            if (activityId) {
              for (let i = 0; i < list.length; i++) {
                if (activityId == list[i].activityId) {
                  this.setState({
                    activityItemNum: i,
                    activityStatus: list[i].activityStatus,
                  });
                }
              }
              this.setState({
                marketingActivityList: list,
                // activityId:list[0].activityId,
                // loadingDetail: false,
              });
              this.activityNodeDetail(activityId);
              this.activityNode(activityId);
            } else {
              this.setState({
                marketingActivityList: list,
                activityId: list[0].activityId,
                loadingDetail: false,
                activityStatus: list[0].activityStatus,
              });
              this.activityNodeDetail(list[0].activityId);
              this.activityNode(list[0].activityId);
            }
          } else {
            this.setState({
              marketingActivityList: [],
              dataList: [],
              newDataList: [],
            });
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
        this.setState({
          loadingDetail: false,
        });
      },
    });
  };

  // 查询活动日历事件
  activityNodeDetail = params => {
    this.setState({
      loadingDetail: true,
    });
    this.props.dispatch({
      type: 'marketingActivities/activityNodeDetail',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let originalData = response.data.activityDate;
          const transformedData = [];
          if (Object.keys(originalData).length > 0) {
            for (const dateNum in originalData) {
              transformedData.push({
                dateNum: dateNum,
                listData: originalData[dateNum],
              });
            }
          }

          this.setState({
            loadingDetail: false,
            dataList: transformedData,
            activityTotalStartTime: response.data.activityStartTime,
            activityInitStartTime: response.data.activityStartTime,
            activityInitEndTime: response.data.activityEndTime,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询活动日历线事件
  activityNode = params => {
    this.setState({
      loadingDetail: true,
    });
    this.props.dispatch({
      type: 'marketingActivities/activityNode',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let originalData = response.data;
          const transformedData = [];
          let newDataList = [];
          if (Object.keys(originalData).length > 0) {
            for (const dateNum in originalData) {
              transformedData.push({
                dateNum: dateNum,
                listData: originalData[dateNum],
              });
            }
            for (let i = 0; i < transformedData.length; i++) {
              let itemData = transformedData[i].listData;
              let eventName = [];
              let eventType = [];
              for (let n = 0; n < itemData.length; n++) {
                eventName.push(itemData[n].eventName);
                eventType.push(itemData[n].eventType);
              }
              let str = eventName.join('、');
              let newItemList = {
                dateNum: transformedData[i].dateNum,
                eventName: str,
                eventType: eventType,
              };
              newDataList.push(newItemList);
            }
          }
          this.setState({
            loadingDetail: false,
            newDataList: newDataList,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  getMonthData = value => {
    if (value.month() === 8) {
      return 1394;
    }
  };

  // 渲染活动日历中的事件list
  dateCellRender = value => {
    const listData = this.getListData(value);
    return (
      <ul className="events">
        {listData?.map(item => {
          let eventType = item.eventType;
          if (eventType == 1) {
            // 邮件
            return (
              <li className="emailItem" key={item.batchId}>
                <div>
                  <img src={EmailMarketingIcon} />
                  <span>{item.specificTime}</span>
                  <p title={item.eventName}>{item.eventName}</p>
                </div>
              </li>
            );
          } else if (eventType == 2) {
            // 短信
            return (
              <li className="messageItem" key={item.batchId}>
                <div>
                  <img src={TableInfoIcon} />
                  <span>{item.specificTime}</span>
                  <p title={item.eventName}>{item.eventName}</p>
                </div>
              </li>
            );
          } else if (eventType == 3) {
            // WhatsApp
            return (
              <li className="whatsAppItem" key={item.batchId}>
                <div>
                  <img src={WhatsAppMarketingIcon} />
                  <span>{item.specificTime}</span>
                  <p title={item.eventName}>{item.eventName}</p>
                </div>
              </li>
            );
          } else if (eventType == 4) {
            // 自定义营销事件
            return (
              <li className="otherItem" key={item.batchId}>
                <div>
                  <img src={OtherMarketingIcon} />
                  <span>{item.specificTime}</span>
                  <p title={item.eventName}>{item.eventName}</p>
                </div>
              </li>
            );
          } else {
            return null;
          }
        })}
      </ul>
    );
  };

  getListData = value => {
    let { dataList } = this.state;
    let listData;
    let num = value.date();
    let time = moment(value).format('yyyy-MM-DD');
    for (let i = 0; i < dataList.length; i++) {
      if (time == dataList[i].dateNum) {
        listData = dataList[i].listData;
        return listData || [];
      }
    }
  };

  monthCellRender = value => {
    const num = this.getMonthData(value);
    return num ? (
      <div className="notes-month">
        <section>{num}</section>
        <span>Backlog number</span>
      </div>
    ) : null;
  };

  handleDateSelect = value => {
    let { activityTotalStartTime } = this.state;
    let changMonthValue = moment(activityTotalStartTime).isSame(value, 'month');
    let changYearValue = moment(activityTotalStartTime).isSame(value, 'year');
    if (changMonthValue && changYearValue) {
      this.setState({
        isModalOpen: true,
        selectTime: value,
        activityTotalStartTime: value,
      });
    } else {
      this.setState({
        // isModalOpen: true,
        // selectTime: value,
        activityTotalStartTime: value,
      });
    }
  };
  onChangeTime = value => {
    this.setState({
      isModalOpen: true,
      selectTime: value,
    });
  };

  // 添加营销事件
  addMarketingEvent = () => {
    let { activityInitStartTime } = this.state;
    this.setState({
      isModalOpen: true,
      // selectTime: moment(),
      selectTime: moment(activityInitStartTime),
    });
  };
  handleOk = () => {
    let {
      marketingTypeNum,
      selectTime,
      activityId,
      activityInitStartTime,
    } = this.state;
    let marketingEventList = {};
    marketingEventList.eventTime = moment(selectTime);
    if (marketingTypeNum == 1) {
      // 邮件营销
      this.setState({
        isModalOpen1: false,
        marketingTypeNum: 0,
      });
      history.push({
        pathname: 'addEmailMarketingEvent',
        state: {
          activityId: activityId,
        },
      });
      localStorage.setItem('activityId', activityId);
      localStorage.removeItem('marketingId');
    } else {
      // 自定义营销
      this.setState(
        {
          isModalOpen: false,
          isModalOpen1: true,
        },
        () => {
          localStorage.setItem('marketingId', activityId);
          this.formReplayRef.current?.setFieldsValue(marketingEventList);
        },
      );
    }
  };
  handleCancel = () => {
    localStorage.removeItem('marketingId');
    this.setState({
      isModalOpen: false,
      marketingTypeNum: 0,
    });
  };

  // 切换营销事件类型
  handleChangeMarketing = typeNum => {
    this.setState({
      marketingTypeNum: typeNum,
    });
  };

  // 输入框获取值
  handleInputChange1 = (name, e, index) => {
    let { createEventList } = this.state;
    createEventList[name] = e.target.value;
  };
  // 单选下拉框取值
  handleSelectChange = (name, value) => {
    let { createEventList } = this.state;
    createEventList[name] = value;
  };
  // 时间框获取值
  handleChangeTime = value => {};

  // 通知邮箱添加
  handleInputChange = e => {
    this.setState({
      emailInputValue: e.target.value,
    });
  };
  handleInputConfirm = e => {
    let { emailInputValue, emailTags } = this.state;
    let emailRegex = /^\s*([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\s*$/;
    let newValue = emailRegex.test(emailInputValue);
    if (newValue) {
      if (emailInputValue && emailTags.indexOf(emailInputValue) === -1) {
        this.setState({
          emailTags: [...emailTags, emailInputValue],
        });
      }
      this.setState({
        emailInputValue: '',
      });
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'email.channel.configuration.email.account.placeholder.error',
          defaultValue: '请输入正确的邮箱格式',
        }),
      });
    }
    e.preventDefault();
  };
  handleClose = removedTag => {
    let { emailTags } = this.state;
    const newTags = emailTags.filter(tag => tag !== removedTag);
    this.setState({
      emailTags: newTags,
    });
  };
  handleEnterPress = e => {
    // 中文输入状态下按下回车键判断
    if (e.key === 'Enter') {
      if (!e.nativeEvent.isComposing) {
        // 直接按下回车键
        this.handleInputConfirm(e);
      }
    }
  };

  // 添加营销事件
  onFinish = values => {
    let { emailTags, activityId } = this.state;
    let eventDescription = values.eventDescription;
    let eventName = values.eventName;
    let channelId = values.channelId;
    let time = values.eventTime;
    let eventTime = moment(time).format('yyyy-MM-DD HH:mm:ss');
    let autoEventNotificationEmail;
    if (emailTags.length > 0) {
      autoEventNotificationEmail = emailTags.join('、');
    } else {
      autoEventNotificationEmail = '';
    }
    let params = {
      eventType: 4,
      activityId: activityId,
      eventName: eventName,
      eventStartTime: eventTime,
      channelId: channelId,
      autoEventDescription: eventDescription,
      autoEventNotificationEmail: autoEventNotificationEmail,
    };
    // 添加自定义营销事件
    this.props.dispatch({
      type: 'marketingActivities/addMarketingEvent',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.formReplayRef.current?.resetFields();
          notification.success({
            message: response.msg,
          });
          this.activityNodeDetail(activityId);
          this.activityNode(activityId);
          this.setState({
            isModalOpen1: false,
            marketingTypeNum: 0,
            emailTags: [],
            emailInputValue: '',
          });
          localStorage.removeItem('marketingId');
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  handleCancel1 = () => {
    this.formReplayRef.current?.resetFields();
    localStorage.removeItem('marketingId');
    this.setState({
      isModalOpen1: false,
      marketingTypeNum: 0,
      emailTags: [],
      emailInputValue: '',
    });
  };

  // 活动状态下拉框取值
  handleSelectChange = (name, value) => {
    let { selectItem } = this.state;
    if (value == 'all') {
      selectItem.activityStatus = '';
    } else {
      selectItem.activityStatus = value;
    }

    this.setState(
      {
        activityStatusValue: value,
        selectItem: selectItem,
        activityId: '',
      },
      () => {
        this.queryMarketing();
      },
    );
  };

  // 切换活动列表选项
  handleChangeActivityItem = (num, activityId, activityStatus) => {
    this.setState({
      activityItemNum: num,
      activityId: activityId,
      activityStatus: activityStatus,
    });
    this.activityNodeDetail(activityId);
    this.activityNode(activityId);
  };
  // 展开活动列表
  handleOpenActivityList = () => {
    this.setState({
      openActivityListValue: true,
    });
  };
  // 收缩活动列表
  handleCloseActivityList = () => {
    this.setState({
      openActivityListValue: false,
    });
  };

  // 创建营销事件时间限制
  disabledDate = current => {
    let { activityInitStartTime, activityInitEndTime } = this.state;
    // 定义特定的日期范围
    let date = new Date(activityInitEndTime);
    // 设置时间为当天的23:59:59
    date.setHours(23, 59, 59);
    const startDate = moment(activityInitStartTime); // 开始日期
    const endDate = moment(date); // 结束日期

    // 禁用在特定日期范围内的日期
    return current && (current < startDate || current > date);
  };

  render() {
    let {
      isModalOpen,
      marketingTypeNum,
      isModalOpen1,
      loadingBtn,
      emailInputValue,
      emailTags,
      activityItemNum,
      openActivityListValue,
      loadingDetail,
      marketingActivityList,
      activityTotalStartTime,
      newDataList,
      activityInitStartTime,
      activityInitEndTime,
      activityStatus,
      channelList,
      activityStatusValue,
    } = this.state;
    const validRange = [
      moment(activityInitStartTime),
      moment(activityInitEndTime),
    ];

    return (
      <Spin spinning={loadingDetail}>
        <div className={styles.activityCalendarContent}>
          <div
            className={styles.activityCalendarLeftContent}
            style={{ display: openActivityListValue ? 'block' : 'none' }}
          >
            <img
              onClick={this.handleCloseActivityList}
              className={styles.switchContent}
              src={SwitchLeftIcon}
            />
            <div className={styles.activityListTitle}>
              <span className={styles.activityListTitleText}>
                <FormattedMessage
                  id="activity.calendar.activity.list.title"
                  defaultMessage="活动列表"
                />
              </span>
              <Select
                value={activityStatusValue}
                options={[
                  {
                    value: 'all',
                    label: getIntl().formatMessage({
                      id: 'work.order.management.status.all',
                      defaultValue: '全部',
                    }),
                  },
                  {
                    value: '1',
                    label: getIntl().formatMessage({
                      id: 'marketing.activities.not.start.status',
                      defaultValue: '未开始',
                    }),
                  },
                  {
                    value: '2',
                    label: getIntl().formatMessage({
                      id: 'marketing.activities.progress.status',
                      defaultValue: '进行中',
                    }),
                  },
                  {
                    value: '3',
                    label: getIntl().formatMessage({
                      id: 'marketing.activities.pause.status',
                      defaultValue: '已暂停',
                    }),
                  },
                  {
                    value: '4',
                    label: getIntl().formatMessage({
                      id: 'marketing.activities.time.end.status',
                      defaultValue: '到时结束',
                    }),
                  },
                  {
                    value: '5',
                    label: getIntl().formatMessage({
                      id: 'marketing.activities.early.termination.status',
                      defaultValue: '提前结束',
                    }),
                  },
                ]}
                placeholder={getIntl().formatMessage({
                  id: 'marketing.activities.activity.status.placeholder',
                  defaultValue: '请选择活动状态',
                })}
                onChange={value =>
                  this.handleSelectChange('activityStatusValue', value)
                }
              />
            </div>
            <div className={styles.activityListContent}>
              {marketingActivityList?.map((item, index) => {
                if (item.activityStatus == 1) {
                  // 未开始
                  return (
                    <div
                      className={styles.activityListItem}
                      onClick={() =>
                        this.handleChangeActivityItem(
                          index,
                          item.activityId,
                          item.activityStatus,
                        )
                      }
                      style={{
                        backgroundImage:
                          activityItemNum == index
                            ? `url(${require('../../../assets/activity.list-item-bg.png')})`
                            : `url(${require('../../../assets/activity.list-item-normal-bg.png')})`,
                        border:
                          activityItemNum == index
                            ? '1px solid #3463FC'
                            : '1px solid #E6E6E6',
                      }}
                    >
                      <p className={styles.activityListItemTitle}>
                        {item.activityName}
                      </p>
                      <p className={styles.activityListItemTime}>
                        <span>{item.activityStartTime}</span>
                        <span>
                          <FormattedMessage
                            id="marketing.activities.time.line"
                            defaultMessage="至"
                          />
                        </span>
                        <span>{item.activityEndTime}</span>
                      </p>
                      <div className={styles.activityListItemStatus}>
                        <div className={styles.circle2}></div>
                        <span className={styles.statusText2}>
                          <FormattedMessage
                            id="marketing.activities.status.not.start"
                            defaultMessage="未开始"
                          />
                        </span>
                      </div>
                    </div>
                  );
                } else if (item.activityStatus == 2) {
                  // 进行中
                  return (
                    <div
                      className={styles.activityListItem}
                      onClick={() =>
                        this.handleChangeActivityItem(
                          index,
                          item.activityId,
                          item.activityStatus,
                        )
                      }
                      style={{
                        backgroundImage:
                          activityItemNum == index
                            ? `url(${require('../../../assets/activity.list-item-bg.png')})`
                            : `url(${require('../../../assets/activity.list-item-normal-bg.png')})`,
                        border:
                          activityItemNum == index
                            ? '1px solid #3463FC'
                            : '1px solid #E6E6E6',
                      }}
                    >
                      <p className={styles.activityListItemTitle}>
                        {item.activityName}
                      </p>
                      <p className={styles.activityListItemTime}>
                        <span>{item.activityStartTime}</span>
                        <span>
                          <FormattedMessage
                            id="marketing.activities.time.line"
                            defaultMessage="至"
                          />
                        </span>
                        <span>{item.activityEndTime}</span>
                      </p>
                      <div className={styles.activityListItemStatus}>
                        <div className={styles.circle1}></div>
                        <span className={styles.statusText1}>
                          <FormattedMessage
                            id="marketing.activities.progress.status"
                            defaultMessage="进行中"
                          />
                        </span>
                      </div>
                    </div>
                  );
                } else if (item.activityStatus == 3) {
                  // 已暂停
                  return (
                    <div
                      className={styles.activityListItem}
                      onClick={() =>
                        this.handleChangeActivityItem(
                          index,
                          item.activityId,
                          item.activityStatus,
                        )
                      }
                      style={{
                        backgroundImage:
                          activityItemNum == index
                            ? `url(${require('../../../assets/activity.list-item-bg.png')})`
                            : `url(${require('../../../assets/activity.list-item-normal-bg.png')})`,
                        border:
                          activityItemNum == index
                            ? '1px solid #3463FC'
                            : '1px solid #E6E6E6',
                      }}
                    >
                      <p className={styles.activityListItemTitle}>
                        {item.activityName}
                      </p>
                      <p className={styles.activityListItemTime}>
                        <span>{item.activityStartTime}</span>
                        <span>
                          <FormattedMessage
                            id="marketing.activities.time.line"
                            defaultMessage="至"
                          />
                        </span>
                        <span>{item.activityEndTime}</span>
                      </p>
                      <div className={styles.activityListItemStatus}>
                        <div className={styles.circle2}></div>
                        <span className={styles.statusText2}>
                          <FormattedMessage
                            id="marketing.activities.pause.status"
                            defaultMessage="已暂停"
                          />
                        </span>
                      </div>
                    </div>
                  );
                } else if (item.activityStatus == 4) {
                  // 到时结束
                  return (
                    <div
                      className={styles.activityListItem}
                      onClick={() =>
                        this.handleChangeActivityItem(
                          index,
                          item.activityId,
                          item.activityStatus,
                        )
                      }
                      style={{
                        backgroundImage:
                          activityItemNum == index
                            ? `url(${require('../../../assets/activity.list-item-bg.png')})`
                            : `url(${require('../../../assets/activity.list-item-normal-bg.png')})`,
                        border:
                          activityItemNum == index
                            ? '1px solid #3463FC'
                            : '1px solid #E6E6E6',
                      }}
                    >
                      <p className={styles.activityListItemTitle}>
                        {item.activityName}
                      </p>
                      <p className={styles.activityListItemTime}>
                        <span>{item.activityStartTime}</span>
                        <span>
                          <FormattedMessage
                            id="marketing.activities.time.line"
                            defaultMessage="至"
                          />
                        </span>
                        <span>{item.activityEndTime}</span>
                      </p>
                      <div className={styles.activityListItemStatus}>
                        <div className={styles.circle3}></div>
                        <span className={styles.statusText3}>
                          <FormattedMessage
                            id="marketing.activities.time.end.status"
                            defaultMessage="到时结束"
                          />
                        </span>
                      </div>
                    </div>
                  );
                } else if (item.activityStatus == 5) {
                  // 提前结束
                  return (
                    <div
                      className={styles.activityListItem}
                      onClick={() =>
                        this.handleChangeActivityItem(
                          index,
                          item.activityId,
                          item.activityStatus,
                        )
                      }
                      style={{
                        backgroundImage:
                          activityItemNum == index
                            ? `url(${require('../../../assets/activity.list-item-bg.png')})`
                            : `url(${require('../../../assets/activity.list-item-normal-bg.png')})`,
                        border:
                          activityItemNum == index
                            ? '1px solid #3463FC'
                            : '1px solid #E6E6E6',
                      }}
                    >
                      <p className={styles.activityListItemTitle}>
                        {item.activityName}
                      </p>
                      <p className={styles.activityListItemTime}>
                        <span>{item.activityStartTime}</span>
                        <span>
                          <FormattedMessage
                            id="marketing.activities.time.line"
                            defaultMessage="至"
                          />
                        </span>
                        <span>{item.activityEndTime}</span>
                      </p>
                      <div className={styles.activityListItemStatus}>
                        <div className={styles.circle3}></div>
                        <span className={styles.statusText3}>
                          <FormattedMessage
                            id="marketing.activities.early.termination.status"
                            defaultMessage="提前结束"
                          />
                        </span>
                      </div>
                    </div>
                  );
                } else {
                  return null;
                }
              })}
            </div>
          </div>
          <div
            className={styles.activityCalendarContractLeftContent}
            style={{ display: openActivityListValue ? 'none' : 'block' }}
          >
            <img src={SWitchRightIcon} onClick={this.handleOpenActivityList} />
            {marketingActivityList?.map((item, index) => {
              if (index == activityItemNum) {
                return <p>{item.activityName}</p>;
              }
            })}
          </div>
          <div
            style={{ width: openActivityListValue ? '75%' : '95%' }}
            className={styles.activityCalendarRightContent}
          >
            <div className={styles.activityCalendarTimeLineContent}>
              <p className="blueBorder">
                <FormattedMessage
                  id="create.activity.time.nodes.title"
                  defaultMessage="活动关键时间节点"
                />
              </p>
              <div className={styles.horizontalTimeline}>
                <div className={styles.timeLineTextContent}>
                  <div className={styles.timeLineContent}></div>
                  {newDataList?.map((item, index) => {
                    let str = index / 2;
                    let isPositiveInteger = /^\+?\d+$/.test(str);
                    let eventType;
                    let duplicates = item.eventType.every(
                      val => val === item.eventType[0],
                    );
                    if (duplicates) {
                      eventType = item.eventType[0];
                    } else {
                      // eventType = item.eventType.toString();
                      eventType = 9;
                    }
                    if (isPositiveInteger) {
                      // 渲染在轴线上方
                      if (eventType == 1) {
                        // 邮件营销
                        return (
                          <div className={styles.topContent}>
                            <p className={styles.dateText}>{item.dateNum}</p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.timeLineIcon}>
                              <img src={EmailMarketingIcon} />
                            </p>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                          </div>
                        );
                      } else if (eventType == 2) {
                        // 短信营销
                        return (
                          <div className={styles.topContent}>
                            <p className={styles.dateText}>{item.dateNum}</p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.timeLineIcon}>
                              <img src={TableInfoIcon} />
                            </p>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                          </div>
                        );
                      } else if (eventType == 3) {
                        // WhatsApp营销
                        return (
                          <div className={styles.topContent}>
                            <p className={styles.dateText}>{item.dateNum}</p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.timeLineIcon}>
                              <img src={WhAtsAppMarketingIcon} />
                            </p>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                          </div>
                        );
                      } else if (eventType == 4) {
                        // 自定义营销
                        return (
                          <div className={styles.topContent}>
                            <p className={styles.dateText}>{item.dateNum}</p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.timeLineIcon}>
                              <img src={OtherMarketingIcon} />
                            </p>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                          </div>
                        );
                      } else if (eventType == 9) {
                        // 多类型营销事件
                        // 自定义营销
                        return (
                          <div className={styles.topContent}>
                            <p className={styles.dateText}>{item.dateNum}</p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.timeLineIcon}>
                              <img src={MixEventIcon} />
                            </p>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                          </div>
                        );
                      } else {
                        return null;
                      }
                    } else {
                      // 渲染在轴线下方
                      if (eventType == 1) {
                        return (
                          <div className={styles.bottomContent}>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                            <p className={styles.timeLineIcon}>
                              <img src={EmailMarketingIcon} />
                            </p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.dateText}>{item.dateNum}</p>
                          </div>
                        );
                      } else if (eventType == 2) {
                        return (
                          <div className={styles.bottomContent}>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                            <p className={styles.timeLineIcon}>
                              <img src={TableInfoIcon} />
                            </p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.dateText}>{item.dateNum}</p>
                          </div>
                        );
                      } else if (eventType == 3) {
                        return (
                          <div className={styles.bottomContent}>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                            <p className={styles.timeLineIcon}>
                              <img src={WhAtsAppMarketingIcon} />
                            </p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.dateText}>{item.dateNum}</p>
                          </div>
                        );
                      } else if (eventType == 4) {
                        return (
                          <div className={styles.bottomContent}>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                            <p className={styles.timeLineIcon}>
                              <img src={OtherMarketingIcon} />
                            </p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.dateText}>{item.dateNum}</p>
                          </div>
                        );
                      } else if (eventType == 9) {
                        return (
                          <div className={styles.bottomContent}>
                            <p
                              title={item.eventName}
                              className={styles.dataText}
                            >
                              {item.eventName}
                            </p>
                            <p className={styles.timeLineIcon}>
                              <img src={MixEventIcon} />
                            </p>
                            <div className={styles.smallLine}></div>
                            <p className={styles.dateText}>{item.dateNum}</p>
                          </div>
                        );
                      } else {
                        return null;
                      }
                    }
                  })}
                </div>
              </div>
            </div>
            <div className={styles.createActivityCalendar}>
              <p className="blueBorder">
                <span className="calendarTitle">
                  <FormattedMessage
                    id="create.activity.calendar.title"
                    defaultMessage="活动日历"
                  />
                </span>
                <HOCAuth authKey={'add_marketing_events'}>
                  {authAccess => (
                    <Button
                      className="marketingEventBtn"
                      type={'primary'}
                      icon={<PlusOutlined />}
                      onClick={this.addMarketingEvent}
                      disabled={authAccess}
                      style={{
                        display:
                          activityStatus == 4 ||
                          activityStatus == 5 ||
                          activityStatusValue == 4 ||
                          activityStatusValue == 5 ||
                          marketingActivityList.length < 1
                            ? 'none'
                            : 'inline-block',
                      }}
                    >
                      <FormattedMessage
                        id="create.activity.add.marketing.event"
                        defaultMessage="添加营销事件"
                      />
                    </Button>
                  )}
                </HOCAuth>
              </p>
              <div className={styles.calendarContent}>
                <Calendar
                  validRange={validRange}
                  value={moment(activityTotalStartTime)}
                  dateCellRender={this.dateCellRender}
                  monthCellRender={this.monthCellRender}
                  onSelect={
                    activityStatus == 4 ||
                    activityStatus == 5 ||
                    activityStatusValue == 4 ||
                    activityStatusValue == 5
                      ? null
                      : this.handleDateSelect
                  }
                />
              </div>

              {/*添加营销事件弹窗*/}
              <Modal
                title={getIntl().formatMessage({
                  id: 'create.activity.add.marketing.event',
                  defaultValue: '添加营销事件',
                })}
                open={isModalOpen}
                onOk={this.handleOk}
                onCancel={this.handleCancel}
                className="addMarketingEventModal"
              >
                <div className="addMarketingEventContent">
                  <div
                    className="emailMarketingContent"
                    onClick={() => this.handleChangeMarketing(1)}
                    style={{
                      backgroundImage:
                        marketingTypeNum == '1'
                          ? `url(${require('../../../assets/marketing-select-bg.png')})`
                          : `url(${require('../../../assets/marketing-normal-bg.png')})`,
                    }}
                  >
                    <img src={EmailMarketingIcon} />
                    <p>
                      <FormattedMessage
                        id="create.activity.email.marketing"
                        defaultMessage="邮件营销"
                      />
                    </p>
                  </div>
                  <div className="whatsappMarketingContent">
                    <img src={WhatsAppMarketingIcon} />
                    <p>
                      <FormattedMessage
                        id="create.activity.whatsapp.marketing"
                        defaultMessage="WhatsApp营销"
                      />
                    </p>
                  </div>
                  <div
                    className="otherMarketingContent"
                    onClick={() => this.handleChangeMarketing(3)}
                    style={{
                      backgroundImage:
                        marketingTypeNum == '3'
                          ? `url(${require('../../../assets/marketing-select-bg.png')})`
                          : `url(${require('../../../assets/marketing-normal-bg.png')})`,
                    }}
                  >
                    <img src={OtherMarketingIcon} />
                    <p>
                      <FormattedMessage
                        id="create.activity.other.marketing"
                        defaultMessage="自定义营销"
                      />
                    </p>
                  </div>
                </div>
              </Modal>

              {/*添加自定义事件弹窗*/}
              <Modal
                title={getIntl().formatMessage({
                  id: 'create.activity.add.marketing.custom.event',
                  defaultValue: '添加自定义事件',
                })}
                open={isModalOpen1}
                footer={null}
                className="addMarketingCustomEventModal"
                onCancel={this.handleCancel1}
              >
                <Form
                  name="basic"
                  autoComplete="off"
                  labelAlign="right"
                  ref={this.formReplayRef}
                  onFinish={this.onFinish}
                >
                  <Row gutter={24}>
                    <Col span={24}>
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'marketing.activities.event.name',
                          defaultValue: '事件名称：',
                        })}
                        name="eventName"
                        rules={[
                          {
                            required: true,
                            message: (
                              <FormattedMessage
                                id="marketing.activities.event.name.placeholder"
                                defaultValue="请输入事件名称"
                              />
                            ),
                          },
                          // {
                          //   pattern: '^[\u4e00-\u9fa5a-zA-Z\\s]+$',
                          //   message: getIntl().formatMessage({
                          //     id: 'marketing.activities.activity.name.tips',
                          //     defaultValue:
                          //       '活动名称只能包含中文，大小写字母，空格',
                          //   }),
                          // },
                        ]}
                      >
                        <Input
                          maxLength={40}
                          placeholder={getIntl().formatMessage({
                            id: 'marketing.activities.event.name.placeholder',
                            defaultValue: '请输入事件名称',
                          })}
                          onChange={e =>
                            this.handleInputChange1('eventName', e)
                          }
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'create.activity.event.time',
                          defaultValue: '事件时间：',
                        })}
                        name="eventTime"
                        rules={[
                          {
                            required: true,
                            message: (
                              <FormattedMessage
                                id="create.activity.activity.time.placeholder"
                                defaultValue="请选择活动时间"
                              />
                            ),
                          },
                        ]}
                      >
                        <DatePicker
                          onChange={this.handleChangeTime}
                          showTime
                          format={'yyyy-MM-DD HH:mm:ss'}
                          disabledDate={this.disabledDate}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'create.activity.email.sent.channel',
                          defaultValue: '邮箱发送渠道：',
                        })}
                        name="channelId"
                        rules={[
                          {
                            required: true,
                            message: (
                              <FormattedMessage id="emailMarketingEvent.step.1.form.label.8.placeholder" />
                            ),
                          },
                        ]}
                      >
                        <Select
                          placeholder={getIntl().formatMessage({
                            id:
                              'emailMarketingEvent.step.1.form.label.8.placeholder',
                          })}
                          options={channelList}
                          allowClear
                          fieldNames={{
                            value: 'channelId',
                            key: 'channelId',
                            label: 'name',
                          }}
                          showSearch
                          filterOption={(inputValue, option) =>
                            option.name
                              .toLowerCase()
                              .indexOf(inputValue.toLowerCase()) >= 0
                          }
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24} className="unidirectionalSynonymTag">
                    <Col span={24}>
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'create.activity.event.notification.email',
                          defaultValue: '通知邮箱：',
                        })}
                        name="terms"
                      >
                        <Input
                          type="text"
                          maxLength={80}
                          value={emailInputValue}
                          onChange={this.handleInputChange}
                          onKeyDown={this.handleEnterPress}
                          placeholder={getIntl().formatMessage({
                            id:
                              'create.activity.event.notification.email.placeholder',
                            defaultValue:
                              '输入通知邮箱，可输入多个，回车生成标签',
                          })}
                          // style={{marginBottom:'10px'}}
                        />
                        {emailTags?.map((tag, index) => {
                          const tagElem = (
                            <Tag
                              key={tag}
                              closable
                              onClose={() => this.handleClose(tag)}
                            >
                              <span>{tag}</span>
                            </Tag>
                          );
                          return tagElem;
                        })}
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'create.activity.event.description',
                          defaultValue: '事件描述：',
                        })}
                        name="eventDescription"
                        rules={[
                          {
                            required: false,
                            message: (
                              <FormattedMessage
                                id="create.activity.event.description.placeholder"
                                defaultValue="请输入事件描述"
                              />
                            ),
                          },
                        ]}
                      >
                        <TextArea
                          autoSize={{
                            minRows: 5,
                            maxRows: 5,
                          }}
                          maxLength={2000}
                          placeholder={getIntl().formatMessage({
                            id: 'create.activity.event.description.placeholder',
                            defaultValue: '请输入事件描述',
                          })}
                          onChange={e =>
                            this.handleInputChange1('eventDescription', e)
                          }
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24} style={{ textAlign: 'center' }}>
                    <Col span={24}>
                      <Button
                        style={{ marginRight: '15px' }}
                        onClick={this.handleCancel1}
                      >
                        <FormattedMessage
                          id="work.order.management.btn.cancel"
                          defaultMessage="取消"
                        />
                      </Button>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={loadingBtn}
                      >
                        <FormattedMessage
                          id="work.order.management.btn.sure"
                          defaultMessage="确定"
                        />
                      </Button>
                    </Col>
                  </Row>
                </Form>
              </Modal>
            </div>
          </div>
        </div>
      </Spin>
    );
  }
}

const mapStateToProps = ({ marketingActivities, emailMarketing }) => {
  return {
    ...marketingActivities,
    ...emailMarketing,
  };
};
export default connect(mapStateToProps)(ActivityCalendar);
