import React, { Component } from 'react';
import { connect, FormattedMessage, getIntl, history } from 'umi';
import {
  Input,
  Button,
  Checkbox,
  Badge,
  Calendar,
  Modal,
  Row,
  Col,
  Form,
  Select,
  DatePicker,
  Tag,
  Tooltip,
  Pagination,
  Spin,
} from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import styles from './index.less';
import moment from 'moment';
import HOCAuth from '@/components/HOCAuth/index';

import EmailMarketingIcon from '../../../assets/email-marketing-icon.png';
import WhatsAppMarketingIcon from '../../../assets/whatapp-marketing-icon.png';
import OtherMarketingIcon from '../../../assets/other-marketing-icon.png';
import TableInfoIcon from '../../../assets/table-info-icon.png';
import { notification } from '@/utils/utils';

const { TextArea } = Input;

class CreateActivityCalendar extends Component {
  formReplayRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      dataList: [],
      isModalOpen: false,
      isModalOpen1: false,
      loadingBtn: false,
      marketingTypeNum: 0,
      createEventList: [],
      // 通知邮箱
      emailInputValue: '',
      emailTags: [],
      marketingId: '',
      loadingDetail: false,
      activityTotalStartTime: '',
      channelList: [],
    };
  }
  componentDidMount() {
    if (this.props.history.location.state) {
      let newId = this.props.history.location.state.marketingId;
      this.setState({
        marketingId: newId,
      });
      this.activityNodeDetail(newId);
    } else {
      let marketingId = localStorage.getItem('marketingId');
      this.setState({
        marketingId: marketingId,
      });
      this.activityNodeDetail(marketingId);
    }
    this.querySESChannel();
  }

  // 营销渠道下拉列表
  querySESChannel = () => {
    this.props.dispatch({
      type: 'emailMarketing/querySESChannel',
      callback: response => {
        if (response.code == 200) {
          this.setState({
            channelList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询活动日历事件
  activityNodeDetail = params => {
    this.setState({
      loadingDetail: true,
    });
    this.props.dispatch({
      type: 'marketingActivities/activityNodeDetail',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let originalData = response.data.activityDate;
          const transformedData = [];
          if (Object.keys(originalData).length > 0) {
            for (const dateNum in originalData) {
              transformedData.push({
                dateNum: dateNum,
                listData: originalData[dateNum],
              });
            }
          }
          this.setState({
            loadingDetail: false,
            dataList: transformedData,
            activityTotalStartTime: response.data.activityStartTime,
            activityInitStartTime: response.data.activityStartTime,
            activityInitEndTime: response.data.activityEndTime,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  getMonthData = value => {
    if (value.month() === 8) {
      return 1394;
    }
  };

  // 渲染活动日历中的事件list
  dateCellRender = value => {
    const listData = this.getListData(value);
    return (
      <ul className="events">
        {listData?.map(item => {
          let eventType = item.eventType;
          if (eventType == 1) {
            // 邮件
            return (
              <li className="emailItem" key={item.batchId}>
                <div>
                  <img src={EmailMarketingIcon} />
                  <span>{item.specificTime}</span>
                  <p title={item.eventName}>{item.eventName}</p>
                </div>
              </li>
            );
          } else if (eventType == 2) {
            // 短信
            return (
              <li className="messageItem" key={item.batchId}>
                <div>
                  <img src={TableInfoIcon} />
                  <span>{item.specificTime}</span>
                  <p title={item.eventName}>{item.eventName}</p>
                </div>
              </li>
            );
          } else if (eventType == 3) {
            // WhatsApp
            return (
              <li className="whatsAppItem" key={item.batchId}>
                <div>
                  <img src={WhatsAppMarketingIcon} />
                  <span>{item.specificTime}</span>
                  <p title={item.eventName}>{item.eventName}</p>
                </div>
              </li>
            );
          } else if (eventType == 4) {
            // 自定义营销事件
            return (
              <li className="otherItem" key={item.batchId}>
                <div>
                  <img src={OtherMarketingIcon} />
                  <span>{item.specificTime}</span>
                  <p title={item.eventName}>{item.eventName}</p>
                </div>
              </li>
            );
          } else {
            return null;
          }
        })}
      </ul>
    );
  };

  getListData = value => {
    let { dataList } = this.state;
    let listData;
    let num = value.date();
    let time = moment(value).format('yyyy-MM-DD');
    for (let i = 0; i < dataList.length; i++) {
      if (time == dataList[i].dateNum) {
        listData = dataList[i].listData;
        return listData || [];
      }
    }
  };

  monthCellRender = value => {
    const num = this.getMonthData(value);
    return num ? (
      <div className="notes-month">
        <section>{num}</section>
        <span>Backlog number</span>
      </div>
    ) : null;
  };

  onSelect = value => {
    let { activityTotalStartTime } = this.state;
    let changMonthValue = moment(activityTotalStartTime).isSame(value, 'month');
    let changYearValue = moment(activityTotalStartTime).isSame(value, 'year');
    if (changMonthValue && changYearValue) {
      this.setState({
        isModalOpen: true,
        selectTime: value,
        activityTotalStartTime: value,
      });
    } else {
      this.setState({
        activityTotalStartTime: value,
      });
    }
  };

  // 添加营销事件
  addMarketingEvent = () => {
    let { activityInitStartTime } = this.state;
    this.setState({
      isModalOpen: true,
      // selectTime: moment(),
      selectTime: moment(activityInitStartTime),
    });
  };
  handleOk = () => {
    let {
      marketingTypeNum,
      selectTime,
      marketingId,
      activityInitStartTime,
    } = this.state;
    let marketingEventList = {};
    marketingEventList.eventTime = moment(selectTime);
    if (marketingTypeNum == 1) {
      // 邮件营销
      this.setState({
        isModalOpen1: false,
        marketingTypeNum: 0,
      });
      history.push({
        pathname: 'addEmailMarketingEvent',
        state: {
          activityId: marketingId,
        },
      });
      localStorage.setItem('activityId', marketingId);
      localStorage.removeItem('marketingId');
    } else {
      // 自定义营销
      this.setState(
        {
          isModalOpen: false,
          isModalOpen1: true,
        },
        () => {
          this.formReplayRef.current?.setFieldsValue(marketingEventList);
        },
      );
    }
  };
  handleCancel = () => {
    this.setState({
      isModalOpen: false,
      marketingTypeNum: 0,
    });
  };

  // 切换营销事件类型
  handleChangeMarketing = typeNum => {
    this.setState({
      marketingTypeNum: typeNum,
    });
  };

  // 输入框获取值
  handleInputChange1 = (name, e, index) => {
    let { createEventList } = this.state;
    createEventList[name] = e.target.value;
  };
  // 单选下拉框取值
  handleSelectChange = (name, value) => {
    let { createEventList } = this.state;
    createEventList[name] = value;
  };

  // 通知邮箱添加
  handleInputChange = e => {
    this.setState({
      emailInputValue: e.target.value,
    });
  };
  handleInputConfirm = e => {
    let { emailInputValue, emailTags } = this.state;

    let emailRegex = /^\s*([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\s*$/;
    let newValue = emailRegex.test(emailInputValue);
    if (newValue) {
      if (emailInputValue && emailTags.indexOf(emailInputValue) === -1) {
        this.setState({
          emailTags: [...emailTags, emailInputValue],
        });
      }
      this.setState({
        emailInputValue: '',
      });
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'email.channel.configuration.email.account.placeholder.error',
          defaultValue: '请输入正确的邮箱格式',
        }),
      });
    }
    e.preventDefault();
  };
  handleClose = removedTag => {
    let { emailTags } = this.state;
    const newTags = emailTags.filter(tag => tag !== removedTag);
    this.setState({
      emailTags: newTags,
    });
  };
  handleEnterPress = e => {
    // 中文输入状态下按下回车键判断
    if (e.key === 'Enter') {
      if (!e.nativeEvent.isComposing) {
        // 直接按下回车键
        this.handleInputConfirm(e);
      }
    }
  };

  // 添加营销事件
  onFinish = values => {
    let { emailTags, marketingId } = this.state;
    let eventDescription = values.eventDescription;
    let channelId = values.channelId;
    let eventName = values.eventName;
    let time = values.eventTime;
    let eventTime = moment(time).format('yyyy-MM-DD HH:mm:ss');
    let autoEventNotificationEmail;
    if (emailTags.length > 0) {
      autoEventNotificationEmail = emailTags.join('、');
    } else {
      autoEventNotificationEmail = '';
    }
    let params = {
      eventType: 4,
      activityId: marketingId,
      eventName: eventName,
      eventStartTime: eventTime,
      channelId: channelId,
      autoEventDescription: eventDescription,
      autoEventNotificationEmail: autoEventNotificationEmail,
    };

    // 添加自定义营销事件
    this.props.dispatch({
      type: 'marketingActivities/addMarketingEvent',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          notification.success({
            message: response.msg,
          });
          this.activityNodeDetail(marketingId);
          this.formReplayRef.current?.resetFields();
          this.setState({
            isModalOpen1: false,
            marketingTypeNum: 0,
            emailTags: [],
            emailInputValue: '',
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  handleCancel1 = () => {
    this.formReplayRef.current?.resetFields();
    this.setState({
      isModalOpen1: false,
      marketingTypeNum: 0,
      emailTags: [],
      emailInputValue: '',
    });
  };
  // 返回列表
  handleGoBack = () => {
    localStorage.removeItem('marketingId');
    let menuType = localStorage.getItem('newMenuType');
    if (menuType == '1') {
      history.push({
        pathname: 'myCampaign',
      });
    } else if (menuType == '3') {
      history.push({
        pathname: 'allCampaign',
      });
    }
  };

  // 创建营销事件时间限制
  disabledDate = current => {
    let { activityInitStartTime, activityInitEndTime } = this.state;
    // 定义特定的日期范围
    let date = new Date(activityInitEndTime);
    // 设置时间为当天的23:59:59
    date.setHours(23, 59, 59);
    const startDate = moment(activityInitStartTime); // 开始日期
    const endDate = moment(date); // 结束日期

    // 禁用在特定日期范围内的日期
    return current && (current < startDate || current > date);
  };

  render() {
    let {
      isModalOpen,
      marketingTypeNum,
      isModalOpen1,
      loadingBtn,
      emailInputValue,
      emailTags,
      loadingDetail,
      activityTotalStartTime,
      activityInitStartTime,
      activityInitEndTime,
      channelList,
    } = this.state;
    const validRange = [
      moment(activityInitStartTime),
      moment(activityInitEndTime),
    ];

    return (
      <Spin spinning={loadingDetail}>
        <div className={styles.createActivityCalendar}>
          <p className="blueBorder">
            <span className="calendarTitle">
              <FormattedMessage
                id="create.activity.calendar.title"
                defaultMessage="活动日历"
              />
            </span>
            <HOCAuth authKey={'add_marketing_events'}>
              {authAccess => (
                <Button
                  className="marketingEventBtn"
                  type={'primary'}
                  icon={<PlusOutlined />}
                  onClick={this.addMarketingEvent}
                  disabled={authAccess}
                >
                  <FormattedMessage
                    id="create.activity.add.marketing.event"
                    defaultMessage="添加营销事件"
                  />
                </Button>
              )}
            </HOCAuth>
          </p>
          <div className={styles.calendarContent}>
            <Calendar
              validRange={validRange}
              value={moment(activityTotalStartTime)}
              dateCellRender={this.dateCellRender}
              monthCellRender={this.monthCellRender}
              onSelect={this.onSelect}
            />

            <div
              className={styles.footerBtnContent}
              onClick={this.handleGoBack}
            >
              <Button>
                <FormattedMessage
                  id="work.order.return.list"
                  defaultMessage="返回列表"
                />
              </Button>
              {/*<Button style={{ marginRight: '12px' }}>*/}
              {/*  <FormattedMessage*/}
              {/*    id="chat.channel.configuration.cancel.btn"*/}
              {/*    defaultMessage="取消"*/}
              {/*  />*/}
              {/*</Button>*/}
              {/*<Button type="primary">*/}
              {/*  <FormattedMessage*/}
              {/*    id="work.order.management.btn.sure"*/}
              {/*    defaultMessage="确定"*/}
              {/*  />*/}
              {/*</Button>*/}
            </div>
          </div>

          {/*添加营销事件弹窗*/}
          <Modal
            title={getIntl().formatMessage({
              id: 'create.activity.add.marketing.event',
              defaultValue: '添加营销事件',
            })}
            open={isModalOpen}
            onOk={this.handleOk}
            onCancel={this.handleCancel}
            className="addMarketingEventModal"
          >
            <div className="addMarketingEventContent">
              <div
                className="emailMarketingContent"
                onClick={() => this.handleChangeMarketing(1)}
                style={{
                  backgroundImage:
                    marketingTypeNum == '1'
                      ? `url(${require('../../../assets/marketing-select-bg.png')})`
                      : `url(${require('../../../assets/marketing-normal-bg.png')})`,
                }}
              >
                <img src={EmailMarketingIcon} />
                <p>
                  <FormattedMessage
                    id="create.activity.email.marketing"
                    defaultMessage="邮件营销"
                  />
                </p>
              </div>
              <div className="whatsappMarketingContent">
                <img src={WhatsAppMarketingIcon} />
                <p>
                  <FormattedMessage
                    id="create.activity.whatsapp.marketing"
                    defaultMessage="WhatsApp营销"
                  />
                </p>
              </div>
              <div
                className="otherMarketingContent"
                onClick={() => this.handleChangeMarketing(3)}
                style={{
                  backgroundImage:
                    marketingTypeNum == '3'
                      ? `url(${require('../../../assets/marketing-select-bg.png')})`
                      : `url(${require('../../../assets/marketing-normal-bg.png')})`,
                }}
              >
                <img src={OtherMarketingIcon} />
                <p>
                  <FormattedMessage
                    id="create.activity.other.marketing"
                    defaultMessage="自定义营销"
                  />
                </p>
              </div>
            </div>
          </Modal>

          {/*添加自定义事件弹窗*/}
          <Modal
            title={getIntl().formatMessage({
              id: 'create.activity.add.marketing.custom.event',
              defaultValue: '添加自定义事件',
            })}
            open={isModalOpen1}
            footer={null}
            className="addMarketingCustomEventModal"
            onCancel={this.handleCancel1}
          >
            <Form
              name="basic"
              autoComplete="off"
              labelAlign="right"
              ref={this.formReplayRef}
              onFinish={this.onFinish}
            >
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'marketing.activities.event.name',
                      defaultValue: '事件名称：',
                    })}
                    name="eventName"
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage
                            id="marketing.activities.event.name.placeholder"
                            defaultValue="请输入事件名称"
                          />
                        ),
                      },
                      // {
                      //   pattern: '^[\u4e00-\u9fa5a-zA-Z\\s]+$',
                      //   message: getIntl().formatMessage({
                      //     id: 'marketing.activities.activity.name.tips',
                      //     defaultValue:
                      //       '活动名称只能包含中文，大小写字母，空格',
                      //   }),
                      // },
                    ]}
                  >
                    <Input
                      maxLength={40}
                      placeholder={getIntl().formatMessage({
                        id: 'marketing.activities.event.name.placeholder',
                        defaultValue: '请输入事件名称',
                      })}
                      onChange={e => this.handleInputChange1('eventName', e)}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'create.activity.event.time',
                      defaultValue: '事件时间：',
                    })}
                    name="eventTime"
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage
                            id="create.activity.activity.time.placeholder"
                            defaultValue="请选择活动时间"
                          />
                        ),
                      },
                    ]}
                  >
                    <DatePicker
                      showTime
                      format={'yyyy-MM-DD HH:mm:ss'}
                      name="eventTime"
                      disabledDate={this.disabledDate}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'emailMarketingEvent.step.1.form.label.8',
                    })}
                    name="channelId"
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage id="emailMarketingEvent.step.1.form.label.8.placeholder" />
                        ),
                      },
                    ]}
                  >
                    <Select
                      placeholder={getIntl().formatMessage({
                        id:
                          'emailMarketingEvent.step.1.form.label.8.placeholder',
                      })}
                      options={channelList}
                      allowClear
                      fieldNames={{
                        value: 'channelId',
                        key: 'channelId',
                        label: 'name',
                      }}
                      showSearch
                      filterOption={(inputValue, option) =>
                        option.name
                          .toLowerCase()
                          .indexOf(inputValue.toLowerCase()) >= 0
                      }
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24} className="unidirectionalSynonymTag">
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'create.activity.event.notification.email',
                      defaultValue: '通知邮箱：',
                    })}
                    name="terms"
                  >
                    <Input
                      type="text"
                      value={emailInputValue}
                      maxLength={80}
                      onChange={this.handleInputChange}
                      onKeyDown={this.handleEnterPress}
                      placeholder={getIntl().formatMessage({
                        id:
                          'create.activity.event.notification.email.placeholder',
                        defaultValue: '输入通知邮箱，可输入多个，回车生成标签',
                      })}
                      // style={{marginBottom:'10px'}}
                    />
                    {emailTags?.map((tag, index) => {
                      const tagElem = (
                        <Tag
                          key={tag}
                          closable
                          onClose={() => this.handleClose(tag)}
                        >
                          <span>{tag}</span>
                        </Tag>
                      );
                      return tagElem;
                    })}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'create.activity.event.description',
                      defaultValue: '事件描述：',
                    })}
                    name="eventDescription"
                    rules={[
                      {
                        required: false,
                        message: (
                          <FormattedMessage
                            id="create.activity.event.description.placeholder"
                            defaultValue="请输入事件描述"
                          />
                        ),
                      },
                    ]}
                  >
                    <TextArea
                      autoSize={{
                        minRows: 5,
                        maxRows: 5,
                      }}
                      maxLength={2000}
                      placeholder={getIntl().formatMessage({
                        id: 'create.activity.event.description.placeholder',
                        defaultValue: '请输入事件描述',
                      })}
                      onChange={e =>
                        this.handleInputChange1('eventDescription', e)
                      }
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24} style={{ textAlign: 'center' }}>
                <Col span={24}>
                  <Button
                    style={{ marginRight: '15px' }}
                    onClick={this.handleCancel1}
                  >
                    <FormattedMessage
                      id="work.order.management.btn.cancel"
                      defaultMessage="取消"
                    />
                  </Button>
                  <Button type="primary" htmlType="submit" loading={loadingBtn}>
                    <FormattedMessage
                      id="work.order.management.btn.sure"
                      defaultMessage="确定"
                    />
                  </Button>
                </Col>
              </Row>
            </Form>
          </Modal>
        </div>
      </Spin>
    );
  }
}

const mapStateToProps = ({ marketingActivities, emailMarketing }) => {
  return {
    ...marketingActivities,
    ...emailMarketing,
  };
};
export default connect(mapStateToProps)(CreateActivityCalendar);
