import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from 'react';
import styles from './index.less';
import {
  Input,
  Button,
  Table,
  Modal,
  notification,
  Select,
  Popconfirm,
  Form,
  Upload,
  Radio,
  Checkbox,
  Tag,
  Spin,
} from 'antd';
import { PlusOutlined, ToTopOutlined } from '@ant-design/icons';
import { ReactComponent as Search } from '@/assets/Search.svg';
import { ReactComponent as DeleteHome } from '@/assets/deleteHome.svg';
import { ReactComponent as Xfbqb } from '@/assets/xfbqb.svg';
import { ReactComponent as Xfdjb } from '@/assets/xfdjb.svg';
import { ReactComponent as Xfdrb } from '@/assets/xfdrb.svg';
import { ReactComponent as Xfdyb } from '@/assets/xfdyb.svg';
import { ReactComponent as Xfqbb } from '@/assets/xfqbb.svg';
import { ReactComponent as Xfsjb } from '@/assets/xfsjb.svg';
import { ReactComponent as Xfsxb } from '@/assets/xfsxb.svg';
import { ReactComponent as Xfzhb } from '@/assets/xfzhb.svg';
import Xf_btn_bg from '@/assets/xf_btn_bg.png';
import { ReactComponent as UploadCloud } from '@/assets/uploadCloud.svg';
import HOCAuth from '@/components/HOCAuth/index';

import {
  connect,
  useDispatch,
  useSelector,
  getIntl,
  FormattedMessage,
  history,
  getLocale,
} from 'umi';
const { TextArea } = Input;
const { Option, OptGroup } = Select;
export default ({ emailMarketing, backCustomer }) => {
  const dispatch = useDispatch();
  const stuFormRef = useRef(null);
  const excelINRef = useRef(null);
  const componentStatsRef = useRef(null);
  const componentLabelRef = useRef(null);
  const componentTimeRef = useRef(null);
  const componentSubscribeRef = useRef(null);
  const componentLvRef = useRef(null);
  const allComponentRef = useRef(null);
  const { user } = useSelector(({ layouts, customerDataGroupManagement }) => ({
    user: layouts.user,
  }));
  let [loading, setLoading] = useState(false);
  let [saveLoading, setSaveLoading] = useState(false);
  let [total, setTotal] = useState(0);
  let [isModalOpen, setIsModalOpen] = useState(false);
  let [pageNum, setPageNum] = useState(1);
  let [pageSize, setPageSize] = useState(10);
  const [leftList, setLeftList] = useState([
    {
      id: 1,
      icon: <Xfqbb />,
      content: getIntl().formatMessage({
        id: 'new.customerDataGroupManagement.left.btn.1',
        defaultValue: '全部客户',
      }),
      selected: true,
    },
    {
      id: 2,
      icon: <Xfdrb />,
      content: getIntl().formatMessage({
        id: 'new.customerDataGroupManagement.left.btn.2',
        defaultValue: 'Excel导入',
      }),
      code: 'bulk_import_customer_information',
      selected: false,
    },
    {
      id: 3,
      icon: <Xfbqb />,
      content: getIntl().formatMessage({
        id: 'new.customerDataGroupManagement.left.btn.3',
        defaultValue: '客户标签',
      }),
      code: 'add_customer_tag',
      selected: false,
    },
    {
      id: 4,
      icon: <Xfsxb />,
      content: getIntl().formatMessage({
        id: 'new.customerDataGroupManagement.left.btn.4',
        defaultValue: '客户属性',
      }),
      selected: false,
    },
    {
      id: 5,
      icon: <Xfsjb />,
      content: getIntl().formatMessage({
        id: 'new.customerDataGroupManagement.left.btn.5',
        defaultValue: '联系时间',
      }),
      selected: false,
    },
    {
      id: 6,
      icon: <Xfdyb />,
      content: getIntl().formatMessage({
        id: 'new.customerDataGroupManagement.left.btn.6',
        defaultValue: '客户订阅',
      }),
      selected: false,
    },
    {
      id: 7,
      icon: <Xfdjb />,
      content: getIntl().formatMessage({
        id: 'new.customerDataGroupManagement.left.btn.7',
        defaultValue: '客户等级',
      }),
      selected: false,
    },
    // {
    //   id: 8,
    //   icon: <Xfzhb />,
    //   content: getIntl().formatMessage({
    //     id: 'new.customerDataGroupManagement.left.btn.8',
    //     defaultValue: '规则组合',
    //   }),
    //   selected: false,
    // },
  ]);
  let [currentView, setCurrentView] = useState(1);
  let [querySubdivisionList, setSubdivisionList] = useState([]);
  let [searchList, setSearchList] = useState({ queryType: 1 });
  let [customerAgeRangeList, setCustomerAgeRangeList] = useState([]);
  let [countryDefList, setCountryDefList] = useState([]);
  let [careerDefList, setCareerDefList] = useState([]);
  let [customHobbyList, setCustomHobbyList] = useState([]);
  let [gradeList, setGradeList] = useState([]);
  let [selectedRowKeys, setSelectedRowKeys] = useState([]);
  let [deleteRowKeys, setDeleteRowKeys] = useState([]);
  let [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  let [uploadAttributeList, setUploadAttributeList] = useState(false);
  let [queryType, setQueryType] = useState(1);
  let [termsTagsEditorValue, setTermsTagsEditorValue] = useState([]);
  let [searchValue, setSearchValue] = useState(0);
  const [standardTagList, setStandardTasList] = useState([]); // 客户标签
  const [standardTagListRender, setStandardTasListRender] = useState([]); // 客户标签输入框渲染

  useEffect(() => {
    // 刷新浏览器保存当前功能状态
    if (history.location.query.tab) {
      // let data = leftList;
      // data.forEach(item => {
      //   item.selected = false;
      //   if (item.content == history.location.query.tab) {
      //     item.selected = true;
      //     console.log('-88888888888888-----',item.id);
      //     setCurrentView(item.id);
      //   }
      // });
      // setLeftList(data);
      history.push('/customerDataGroupManagement/addCustomerGroups');
    }
    customerAgeRange();
    countryDef();
    careerDef();
    customHobby();
    gradeListQuery();
    queryAllStandardTag();
  }, []);

  useEffect(() => {
    // 监听leftList，改变时保持对应功能页
    leftList.forEach(item => {
      if (item.selected) {
        if (item.id == 1 || item.id == 2) {
          setCurrentView(item.id);
          setSearchList({ queryType: 1 });
        } else {
          setCurrentView(item.id);
          setSubdivisionList([]);
          setTotal(0);
        }
      }
    });
    //重置分页数据
    setPageNum(1);
    setPageSize(10);
  }, [leftList]);

  // 查询客户细分
  useEffect(() => {
    if (currentView == 1 || searchValue == 1) {
      querySubdivisionCustomer(searchList);
    }
  }, [currentView, searchList, pageNum, pageSize]);
  const querySubdivisionCustomer = item => {
    setLoading(true);
    let params = {
      querySearchList: item,
      pageNum: pageNum,
      pageSize: pageSize,
    };
    dispatch({
      type: 'customerDataGroupManagement/querySubdivisionCustomer',
      payload: params,
      callback: response => {
        setLoading(false);
        let { code, data, msg } = response;
        if (200 === code) {
          let dataList = response.data.records;
          if (dataList) {
            for (let i = 0; i < dataList.length; i++) {
              dataList[i].key = dataList[i].customerId;
            }
          }
          setSubdivisionList(dataList);
          setTotal(response.data.total);
          setSearchValue(0);
        }
      },
    });
  };
  /**
   * 列表表头信息
   */
  const columns = useMemo(
    () => [
      {
        title: (
          <FormattedMessage
            id="customerList.table.customerName"
            defaultMessage="客户名称"
          />
        ),
        dataIndex: 'name',
        key: 'name',
        width: 200,
        ellipsis: true,
      },
      {
        title: (
          <FormattedMessage
            id="customerList.table.channel"
            defaultMessage="来源渠道"
          />
        ),
        dataIndex: 'channelName',
        key: 'channelName',
        width: 200,
        ellipsis: true,
      },
      {
        title: (
          <FormattedMessage
            id="customerInformation.table.nation"
            defaultMessage="国家"
          />
        ),
        dataIndex: 'countryName',
        key: 'countryName',
        width: 200,
        ellipsis: true,
      },
      {
        title: (
          <FormattedMessage
            id="customerInformation.table.customerLabel"
            defaultMessage="客户标签"
          />
        ),
        dataIndex: 'customerLabel',
        key: 'customerLabel',
        width: 200,
        ellipsis: true,
      },
      {
        title: (
          <FormattedMessage
            id="customerInformation.table.nation.contact.email"
            defaultMessage="联系邮箱"
          />
        ),
        dataIndex: 'emailAddress',
        key: 'emailAddress',
        width: 200,
        ellipsis: true,
      },
      {
        title: (
          <FormattedMessage
            id="customerInformation.table.contact.phone"
            defaultMessage="联系电话"
          />
        ),
        dataIndex: 'telephone',
        key: 'telephone',
        width: 200,
        ellipsis: true,
      },
      {
        title: (
          <FormattedMessage
            id="customerInformation.table.ContactTime"
            defaultMessage="最后联系时间"
          />
        ),
        dataIndex: 'customActiveTime',
        key: 'customActiveTime',
        width: 200,
        ellipsis: true,
      },
    ],
    [loading, pageNum, pageSize],
  );

  // 查询客户标签
  const queryAllStandardTag = () => {
    setLoading(true);
    dispatch({
      type: 'tagManagement/queryTagSelectList',
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          if (response.data) {
            setStandardTasList(response.data);
            setStandardTasListRender(response.data);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  /**
   * 返回对应的组件视图
   */
  const showComponent = id => {
    switch (id) {
      case 1:
        return (
          <AllComponent
            ref={allComponentRef}
            handleClickSearchAll={handleClickSearchAll}
          />
        );
      case 2:
        return <ExcelIN ref={excelINRef} />;
      case 3:
        return (
          <ComponentLabel
            // termsTagsEditorValue={termsTagsEditorValue}
            ref={componentLabelRef}
            handleClickSearch4={handleClickSearch4}
            standardTagList={standardTagList}
            standardTagListRender={standardTagListRender}
          />
        );
      case 4:
        return (
          <ComponentStats
            ref={componentStatsRef}
            handleClickSearch={handleClickSearch}
            customerAgeRangeList={customerAgeRangeList}
            countryDefList={countryDefList}
            careerDefList={careerDefList}
            customHobbyList={customHobbyList}
          />
        );
      case 5:
        return (
          <ComponentTime
            ref={componentTimeRef}
            handleClickSearch3={handleClickSearch3}
          />
        );
      case 6:
        return (
          <ComponentSubscribe
            ref={componentSubscribeRef}
            handleClickSearch2={handleClickSearch2}
          />
        );
      case 7:
        return (
          <ComponentLv
            ref={componentLvRef}
            handleClickSearch1={handleClickSearch1}
            gradeList={gradeList}
          />
        );
      // case 8:
      //   return <ComponentGroup />;
    }
  };
  /**
   * 切换细分功能
   */
  const switchFunc = i => {
    let data = [...leftList];
    data.forEach(item => {
      item.selected = false;
    });
    data[i].selected = true;
    setLeftList(data);

    history.push(`?tab=${data[i].content}`);
  };

  // 查询客户年龄范围
  const customerAgeRange = () => {
    dispatch({
      type: 'customerDataGroupManagement/customerAgeRange',
      callback: response => {
        if (response.code == 200) {
          setCustomerAgeRangeList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询客户年龄范围
  const countryDef = () => {
    dispatch({
      type: 'customerDataGroupManagement/countryDef',
      callback: response => {
        if (response.code == 200) {
          setCountryDefList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询客户职业
  const careerDef = () => {
    dispatch({
      type: 'customerDataGroupManagement/careerDef',
      callback: response => {
        if (response.code == 200) {
          setCareerDefList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询客户爱好下拉
  const customHobby = () => {
    dispatch({
      type: 'customerDataGroupManagement/customHobby',
      callback: response => {
        if (response.code == 200) {
          setCustomHobbyList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询客户等级接口
  const gradeListQuery = () => {
    dispatch({
      type: 'customerDataGroupManagement/gradeList',
      callback: response => {
        if (response.code == 200) {
          setGradeList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 全部客户点击筛选
  const handleClickSearchAll = unsubscribedValue => {
    let params = {
      excludeUnsubscribe: unsubscribedValue,
      customerIdList: deleteRowKeys,
      queryType: queryType,
    };

    setSearchList(params);
    setPageNum(1);
    setSearchValue(1);
  };
  // 客户属性点击筛选按钮
  const handleClickSearch = (
    customerAgeStart,
    customerAgeEnd,
    country,
    sex,
    career,
    customHobby,
    unsubscribedValue,
  ) => {
    let params = {
      customerAgeStart: customerAgeStart,
      customerAgeEnd: customerAgeEnd,
      customerCountryId: country,
      customerSex: sex,
      customerCareerId: career,
      customerHobby: customHobby,
      customerIdList: deleteRowKeys,
      queryType: queryType,
      excludeUnsubscribe: unsubscribedValue,
    };
    setSearchList(params);
    setPageNum(1);
    setSearchValue(1);
  };
  // 客户等级点击筛选
  const handleClickSearch1 = (gradeSelectList, unsubscribedValue) => {
    let params = {
      gradeId: gradeSelectList,
      customerIdList: deleteRowKeys,
      excludeUnsubscribe: unsubscribedValue,
      queryType: queryType,
    };
    setSearchList(params);
    setPageNum(1);
    setSearchValue(1);
  };
  // 客户订阅点击筛选
  const handleClickSearch2 = radioFlag => {
    let params = {
      subscribe: radioFlag,
      customerIdList: deleteRowKeys,
      queryType: queryType,
    };
    setSearchList(params);
    setPageNum(1);
    setSearchValue(1);
  };
  // 联系时间点击筛选
  const handleClickSearch3 = (contactDays, contactType, unsubscribedValue) => {
    let params = {
      contactDays: contactDays,
      contactType: contactType,
      customerIdList: deleteRowKeys,
      excludeUnsubscribe: unsubscribedValue,
      queryType: queryType,
    };
    setSearchList(params);
    setPageNum(1);
    setSearchValue(1);
  };
  // 客户标签点击筛选
  const handleClickSearch4 = (customerLabel, unsubscribedValue) => {
    let params = {
      customerLabel: customerLabel,
      customerIdList: deleteRowKeys,
      excludeUnsubscribe: unsubscribedValue,
      queryType: queryType,
    };
    setSearchList(params);
    setPageNum(1);
    setSearchValue(1);
  };
  // table多选事件
  const onSelectChange = newSelectedRowKeys => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 显示批量删除提示弹窗
  const showDeleteModal = () => {
    if (selectedRowKeys.length > 0) {
      setIsDeleteModalOpen(true);
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'work.order.batch.marking.tips',
          defaultValue: '至少选择一行数据！',
        }),
      });
    }
  };
  // 关闭批量删除提示弹窗
  const cancelDeleteModal = () => {
    setIsDeleteModalOpen(false);
  };
  /**
   * 批量删除
   */
  const deleteTableItem = () => {
    const mergedArray = [...deleteRowKeys, ...selectedRowKeys];
    let params = {
      customerIdList: mergedArray,
      queryType: queryType,
    };
    setDeleteRowKeys(mergedArray);
    setSearchList(params);
    setPageNum(1);
    setSearchValue(1);
    setIsDeleteModalOpen(false);
  };
  /**
   * 取消保存客户细分
   */
  const historyBack = () => {
    //邮件营销操作
    if (emailMarketing) {
      backCustomer();
    } else {
      history.push('/customerDataGroupManagement');
    }
    // history.goBack();
  };
  /**
   * 打开弹窗-添加分组
   * */
  const modalShow = () => {
    if (currentView == 2) {
      // 下拉框选择值
      const excelCol = excelINRef.current.excelCol;
      // 表头
      const meterHeaderList = excelINRef.current.meterHeaderList;
      if (meterHeaderList.length > 0) {
        let excelColLength = Object.keys(excelCol).length;
        if (excelColLength == meterHeaderList.length) {
          const result = [];
          // 遍历给定对象的键值对
          for (const key in excelCol) {
            // 创建新的对象，包含"excelHead"和"attribute"属性
            const newObj = {
              excelHead: key,
              attribute: excelCol[key].toString(), // 将属性值转换为字符串
            };
            result.push(newObj); // 将新对象添加到结果数组中
          }
          setUploadAttributeList(result);
          setIsModalOpen(true);
        } else {
          notification.warning({
            message: getIntl().formatMessage({
              id: 'new.customerDataGroupManagement.import.excel.tips.1',
              defaultValue: '请完成所有所有客户资料属性字段的选择',
            }),
          });
        }
      } else {
        notification.warning({
          message: getIntl().formatMessage({
            id: 'new.customerDataGroupManagement.import.excel.tips',
            defaultValue: '暂无Excel文件导入',
          }),
        });
      }
    } else {
      setIsModalOpen(true);
    }
  };
  /**
   * 关闭弹窗
   * */
  const handleCancel = () => {
    stuFormRef.current?.resetFields();
    setIsModalOpen(false);
  };
  /**
   * 保存细分-添加分组
   */
  const onSubmit = values => {
    const { customerGroupName, customerGroupDescribe } = values;
    let customerIdList = deleteRowKeys;
    setSaveLoading(true);
    if (currentView == 2) {
      const fileList = excelINRef.current.fileList;
      const fmData = new FormData();
      fmData.append('file', fileList);
      let items = {
        uploadAttributeList: uploadAttributeList,
        subdivisionName: customerGroupName,
        detailDescription: customerGroupDescribe,
        subdivisionMethod: currentView,
      };
      const json = JSON.stringify(items);
      const blob = new Blob([json], { type: 'application/json' });
      fmData.append('request', blob);

      // Excel导入保存客户细分
      dispatch({
        type: 'customerDataGroupManagement/saveUploadFileHeader',
        payload: fmData,
        callback: response => {
          if (response.code === 200) {
            notification.success({
              message: response.msg,
            });
            //邮件营销操作
            if (emailMarketing) {
              backCustomer(response.data);
            } else {
              history.push('/customerDataGroupManagement');
            }
          } else {
            notification.error({
              message: response.msg,
            });
          }
          handleCancel();
          stuFormRef.current?.resetFields();
          setSaveLoading(false);
        },
      });
    } else if (currentView == 1) {
      let { unsubscribedValue } = allComponentRef.current;

      const payload = {
        subdivisionName: customerGroupName,
        detailDescription: customerGroupDescribe,
        ruleConditions: [
          {
            code: 'excludeUnsubscribe',
            value: unsubscribedValue,
            opt: unsubscribedValue ? 6 : 5,
          },
        ],
        subdivisionMethod: currentView,
        queryCustomerCondition: {
          queryType: queryType,
          customerIdList: customerIdList,
          excludeUnsubscribe: unsubscribedValue,
        },
      };
      addSubdivisionCustomer(payload);
    } else if (currentView == 3) {
      let { termsTags, unsubscribedValue } = componentLabelRef.current;
      const payload = {
        subdivisionName: customerGroupName,
        detailDescription: customerGroupDescribe,
        subdivisionMethod: currentView,
        ruleConditions: [
          {
            code: 'customerLabel',
            value: termsTags,
            opt: 1,
          },
          {
            code: 'excludeUnsubscribe',
            value: unsubscribedValue,
            opt: unsubscribedValue ? 6 : 5,
          },
        ],
        queryCustomerCondition: {
          customerLabel: termsTags,
          queryType: queryType,
          customerIdList: customerIdList,
          excludeUnsubscribe: unsubscribedValue,
        },
      };
      addSubdivisionCustomer(payload);
    } else if (currentView == 4) {
      let {
        customerAgeStart,
        customerAgeEnd,
        country,
        sex,
        career,
        customHobby,
        unsubscribedValue,
      } = componentStatsRef.current;
      const payload = {
        subdivisionName: customerGroupName,
        detailDescription: customerGroupDescribe,
        ruleConditions: [
          {
            code: 'customerAgeStart',
            value: customerAgeStart,
            opt: 1,
          },
          {
            code: 'customerAgeEnd',
            value: customerAgeEnd,
            opt: 1,
          },
          {
            code: 'sex',
            value: sex,
            opt: 1,
          },
          {
            code: 'country',
            value: country,
            opt: 1,
          },
          {
            code: 'career',
            value: career,
            opt: 1,
          },
          {
            code: 'hobby',
            value: customHobby,
            opt: 5,
          },
          {
            code: 'excludeUnsubscribe',
            value: unsubscribedValue,
            opt: unsubscribedValue ? 6 : 5,
          },
        ],
        queryCustomerCondition: {
          customerAgeStart: customerAgeStart,
          customerAgeEnd: customerAgeEnd,
          customerCountryId: country,
          customerSex: sex,
          customerCareerId: career,
          customerHobby: customHobby,
          queryType: queryType,
          customerIdList: customerIdList,
          excludeUnsubscribe: unsubscribedValue,
        },
        subdivisionMethod: currentView,
      };
      addSubdivisionCustomer(payload);
    } else if (currentView == 5) {
      let { days, actions, unsubscribedValue } = componentTimeRef.current;
      const payload = {
        subdivisionName: customerGroupName,
        detailDescription: customerGroupDescribe,
        ruleConditions: [
          {
            code: actions,
            value: days,
            opt: 4,
          },
          {
            code: 'excludeUnsubscribe',
            value: unsubscribedValue,
            opt: unsubscribedValue ? 6 : 5,
          },
        ],
        subdivisionMethod: currentView,
        queryCustomerCondition: {
          queryType: queryType,
          contactDays: days,
          contactType: actions,
          customerIdList: customerIdList,
          excludeUnsubscribe: unsubscribedValue,
        },
      };
      addSubdivisionCustomer(payload);
    } else if (currentView == 6) {
      let { radioFlag } = componentSubscribeRef.current;
      const payload = {
        subdivisionName: customerGroupName,
        detailDescription: customerGroupDescribe,
        ruleConditions: [
          {
            code: 'subscribeStatus',
            value: radioFlag,
            opt: 1,
          },
        ],
        subdivisionMethod: currentView,
        queryCustomerCondition: {
          queryType: queryType,
          subscribe: radioFlag,
          customerIdList: customerIdList,
        },
      };
      addSubdivisionCustomer(payload);
    } else if (currentView == 7) {
      let { gradeSelectList, unsubscribedValue } = componentLvRef.current;
      const payload = {
        subdivisionName: customerGroupName,
        detailDescription: customerGroupDescribe,
        ruleConditions: [
          {
            code: 'gradeId',
            value: gradeSelectList,
            opt: 1,
          },
          {
            code: 'excludeUnsubscribe',
            value: unsubscribedValue,
            opt: unsubscribedValue ? 6 : 5,
          },
        ],
        subdivisionMethod: currentView,
        queryCustomerCondition: {
          queryType: queryType,
          gradeId: gradeSelectList,
          customerIdList: customerIdList,
          excludeUnsubscribe: unsubscribedValue,
        },
      };
      addSubdivisionCustomer(payload);
    }
  };
  // 其他tab保存客户细分
  const addSubdivisionCustomer = payload => {
    dispatch({
      type: 'customerDataGroupManagement/addSubdivisionCustomer',
      payload: payload,
      callback: response => {
        if (response.code === 200) {
          notification.success({
            message: response.msg,
          });
          //邮件营销操作
          if (emailMarketing) {
            backCustomer(response.data);
          } else {
            history.push('/customerDataGroupManagement');
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
        handleCancel();
        stuFormRef.current?.resetFields();
        setSaveLoading(false);
      },
    });
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  return (
    <Spin spinning={loading}>
      <div className={styles.contentBox} id="contentBox">
        <div className={styles.contentBoxDivLeft}>
          <p className={styles.contentBoxDivLeftTitle}>
            <FormattedMessage
              id="new.customerDataGroupManagement.left.title"
              defaultMessage="添加客户细分"
            />
          </p>
          <p className={styles.contentBoxDivLeftTitleTips}>
            <FormattedMessage
              id="new.customerDataGroupManagement.left.tips"
              defaultMessage="请选择一种客户细分方式"
            />
          </p>
          <div className={styles.gridContainer}>
            {leftList.map((item, index) => {
              return item.code ? (
                <HOCAuth authKey={item.code}>
                  {authAccess => (
                    <div
                      className={`${styles.gridItem} ${
                        authAccess ? 'disabled' : ''
                      }`}
                      style={{
                        backgroundImage: item.selected
                          ? `url(${Xf_btn_bg})`
                          : '',
                        color: item.selected ? '#fff' : '#3463FC',
                      }}
                      onClick={() => switchFunc(index)}
                    >
                      <span className={styles.iconSpan}>{item.icon}</span>
                      <span>{item.content}</span>
                    </div>
                  )}
                </HOCAuth>
              ) : (
                <div
                  className={styles.gridItem}
                  style={{
                    backgroundImage: item.selected ? `url(${Xf_btn_bg})` : '',
                    color: item.selected ? '#fff' : '#3463FC',
                  }}
                  onClick={() => switchFunc(index)}
                >
                  <span className={styles.iconSpan}>{item.icon}</span>
                  <span>{item.content}</span>
                </div>
              );
            })}
          </div>
        </div>
        <div className={styles.contentBoxDivRight}>
          <div className={styles.contentBoxDivRightTop}>
            <div>{showComponent(currentView)}</div>
            {currentView !== 2 ? (
              <div
                className={styles.contentBoxDivRightMain}
                // style={{ height: currentView === 1 ? '78vh' : '60vh' }}
              >
                <div className={styles.contentBoxDivTitle}>
                  <div className={styles.headContent}>
                    <div style={{ flex: 2 }}>
                      <Input
                        placeholder={getIntl().formatMessage({
                          id:
                            'new.customerDataGroupManagement.right.table.input',
                        })}
                        prefix={<Search />}
                        onPressEnter={e => {
                          let item = {
                            contactInformation: e.target.value,
                            customerIdList: deleteRowKeys,
                            queryType: queryType,
                          };
                          setSearchList(item);
                          setPageNum(1);
                        }}
                        // onBlur={e => {
                        //   let item={
                        //     contactInformation:e.target.value
                        //   }
                        //   setSearchList(item);
                        //   setPageNum(1);
                        // }}
                        style={{ width: '80%', height: 32 }}
                      />
                    </div>
                    <div
                      style={{
                        flex: 1,
                        display: 'flex',
                        justifyContent: 'flex-end',
                      }}
                    >
                      <Button
                        danger
                        onClick={showDeleteModal}
                        icon={<DeleteHome />}
                      >
                        <span style={{ marginLeft: 6 }}>
                          <FormattedMessage
                            id="new.customerDataGroupManagement.delete.modal.title"
                            defaultMessage="批量删除"
                          />
                        </span>
                      </Button>
                      {/* <Button onClick={() => historyAddQa} icon={<ToTopOutlined />}>
                <span style={{ marginLeft: 6 }}>
                  <FormattedMessage
                    id="new.customerDataGroupManagement.btn.2"
                    defaultMessage={'批量上传'}
                  />
                </span>
              </Button> */}
                      {/*<Button*/}
                      {/*  type={'primary'}*/}
                      {/*  onClick={() => historyAddQa}*/}
                      {/*  icon={<PlusOutlined />}*/}
                      {/*>*/}
                      {/*  <span style={{ marginLeft: 6 }}>*/}
                      {/*    <FormattedMessage*/}
                      {/*      id="new.customerDataGroupManagement.btn.3"*/}
                      {/*      defaultMessage={'添加客户'}*/}
                      {/*    />*/}
                      {/*  </span>*/}
                      {/*</Button>*/}
                    </div>
                  </div>
                </div>
                <div className={styles.tableContent}>
                  <Table
                    loading={loading}
                    rowSelection={rowSelection}
                    dataSource={querySubdivisionList}
                    columns={columns}
                    scroll={{
                      x: '100%',
                      // y: 'calc(100vh - 400px)',
                    }}
                    pagination={{
                      total: total,
                      showSizeChanger: true,
                      current: pageNum,
                      pageSize: pageSize,
                      pageSizeOptions: [10, 20, 50, 100],
                      showTotal: total => (
                        <FormattedMessage
                          id="page.total.num"
                          defaultMessage={`共 ${total} 条`}
                          values={{ total }}
                        />
                      ),
                      onChange: (pageNum, pageSize) => {
                        setSearchValue(1);
                        setSelectedRowKeys([]);
                        setPageNum(pageNum);
                        setPageSize(pageSize);
                      },
                    }}
                  />
                </div>
              </div>
            ) : (
              ''
            )}
          </div>
          <div className={styles.contentBoxDivRightFoot}>
            <Button onClick={() => historyBack()}>
              <span>
                <FormattedMessage
                  id="AIGC.Drawer.two.form.cancel"
                  defaultMessage={'取消'}
                />
              </span>
            </Button>
            <Button type={'primary'} onClick={() => modalShow()}>
              <span>
                <FormattedMessage
                  id="new.customerDataGroupManagement.right.com1.btn.save"
                  defaultMessage={'保存客户细分'}
                />
              </span>
            </Button>
          </div>
        </div>
        {/*保存客户细分弹窗*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'new.customerDataGroupManagement.modal.title',
            defaultValue: '客户细分基本信息',
          })}
          open={isModalOpen}
          width={800}
          footer={null}
          onCancel={handleCancel}
        >
          <div>
            <Form
              name="basic"
              labelCol={{
                span: 4,
              }}
              wrapperCol={{
                span: 17,
              }}
              autoComplete="off"
              ref={stuFormRef}
              onFinish={values => onSubmit(values)}
            >
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'new.customerDataGroupManagement.groupName.table',
                  defaultValue: '细分名称',
                })}
                name="customerGroupName"
                rules={[
                  // {
                  //   pattern: '^[a-zA-Z\\- \u4e00-\u9fa5]+$',
                  //   message: getIntl().formatMessage({
                  //     id:
                  //       'new.customerDataGroupManagement.groupName.table.pattern',
                  //     defaultValue: '细分名称只能由英文大小写、汉字和横线组成',
                  //   }),
                  // },
                  {
                    required: true,
                  },
                  {
                    max: 40,
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id:
                      'new.customerDataGroupManagement.groupName.table.placeholder',
                    defaultValue: '请输入细分名称',
                  })}
                />
              </Form.Item>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'new.customerDataGroupManagement.describe.detail',
                  defaultValue: '细分描述',
                })}
                name="customerGroupDescribe"
                rules={[
                  {
                    required: true,
                  },
                  {
                    max: 2000,
                  },
                ]}
              >
                <TextArea
                  showCount
                  maxLength={2000}
                  style={{
                    height: 200,
                    resize: 'none',
                  }}
                  // onChange={this.getDescribeValue}
                  placeholder={getIntl().formatMessage({
                    id:
                      'customerDataGroupManagement.describe.detail.placeholder',
                    defaultValue: '请填写备注内容，最多输入2000字',
                  })}
                />
              </Form.Item>

              <Form.Item
                wrapperCol={{
                  offset: 10,
                  span: 10,
                }}
                style={{ marginBottom: '0px' }}
              >
                <Button
                  onClick={() => handleCancel()}
                  style={{ marginRight: 20 }}
                >
                  <FormattedMessage
                    id="customerDataGroupManagement.cancel.btn"
                    defaultMessage="取消"
                  />
                </Button>
                <Button type="primary" htmlType="submit" loading={saveLoading}>
                  <FormattedMessage
                    id="work.order.management.btn.save"
                    defaultMessage="保存"
                  />
                </Button>
              </Form.Item>
            </Form>
          </div>
        </Modal>

        {/*批量删除提示弹窗*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'new.customerDataGroupManagement.delete.modal.title',
            defaultValue: '批量移除',
          })}
          className="deleteCustomerModal"
          open={isDeleteModalOpen}
          onCancel={cancelDeleteModal}
          onOk={deleteTableItem}
        >
          <p className="deleteText">
            <FormattedMessage
              id="new.customerDataGroupManagement.delete.modal.text"
              defaultMessage="您是否确定要批量移除客户？"
            />
          </p>
          <span className="deleteTips">
            <FormattedMessage
              id="new.customerDataGroupManagement.delete.modal.text.tips"
              defaultMessage="当把客户从列表移除后，将不会保存到当前客户细分。"
            />
          </span>
        </Modal>
      </div>
    </Spin>
  );
};
const { Dragger } = Upload;
// 全部客户
const AllComponent = forwardRef((props, ref) => {
  const [unsubscribedValue, setUnsubscribedValue] = useState(true);
  const changeUnsubscribed = e => {
    setUnsubscribedValue(e.target.checked);
  };
  useImperativeHandle(ref, () => ({
    unsubscribedValue: unsubscribedValue,
  }));

  return (
    <div className={styles.componentBox}>
      <p className="blueBorder">
        <FormattedMessage id="new.customerDataGroupManagement.right.com.title" />
      </p>
      <p className={styles.formItem}>
        <Checkbox checked={unsubscribedValue} onChange={changeUnsubscribed}>
          <FormattedMessage id="new.customerDataGroupManagement.customer.eliminate.unsubscribed" />
        </Checkbox>
      </p>
      <div className={styles.formItemBtm}>
        <span className={styles.formItemBtmFont}>
          <FormattedMessage id="new.customerDataGroupManagement.right.com2.tips.new.add.2" />
        </span>
        <Button
          type={'primary'}
          onClick={() => {
            props.handleClickSearchAll(unsubscribedValue);
          }}
          icon={<Search />}
        >
          <span style={{ marginLeft: 3 }}>
            <FormattedMessage
              id="new.customerDataGroupManagement.right.com2.btn"
              defaultMessage={'筛选'}
            />
          </span>
        </Button>
      </div>
    </div>
  );
});
// ****************************************************导入***************************************************************

const ExcelIN = forwardRef(({}, ref) => {
  const dispatch = useDispatch();
  const [excelCol, setExcelCol] = useState({});
  const [meterHeaderList, setMeterHeaderList] = useState([]);
  const [attributeDropdownList, setAttributeDropdownList] = useState([]);
  let [fileList, setFileList] = useState('');
  // 在子组件中暴露方法给父组件调用
  useImperativeHandle(ref, () => ({
    excelCol: excelCol,
    meterHeaderList: meterHeaderList,
    fileList: fileList,
  }));
  // 上传文件
  const uploadProps = {
    name: 'file',
    multiple: false,
    maxCount: 1,
    accept: '.xlsx,.xls',
    showUploadList: false,
    // // 上传前的校验
    beforeUpload: file => {
      let { size } = file;
      let fileSize = size / 1024 / 1024;
      if (fileSize > 10) {
        notification.error({
          message: getIntl().formatMessage(
            {
              id: 'message.upload.size',
            },
            {
              fileSize: 10,
            },
          ),
        });
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    // 文件上传中、上传成功、上传失败 都会回调这个函数
    onChange: info => {
      // setFileList(info.file);
    },
    // 自定义的上传接口
    customRequest: ({ file, onSuccess }) => {
      const fmData = new FormData();
      fmData.append('file', file);
      setFileList(file);
      dispatch({
        type: 'customerDataGroupManagement/uploadFileHeader',
        payload: fmData,
        callback: response => {
          if (200 === response.code) {
            queryAttributeDropdown();
            setMeterHeaderList(response.data);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    },
  };
  // 查询上传客户细分时属性下拉
  const queryAttributeDropdown = () => {
    dispatch({
      type: 'customerDataGroupManagement/queryAttributeDropdown',
      callback: response => {
        if (200 === response.code) {
          setAttributeDropdownList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 下拉列表点击事件
  const addFormQuestion = (e, name) => {
    excelCol[name] = e;
    setExcelCol(excelCol);
  };

  return (
    <div className={styles.componentBox} style={{ paddingBottom: 50 }}>
      <div>
        <Dragger {...uploadProps}>
          <div className="ant-upload-drag-icon">
            <UploadCloud />
          </div>
          <div className="ant-upload-text">
            <p>
              <span style={{ fontSize: 12, color: '#999' }}>
                <FormattedMessage id="new.customerDataGroupManagement.right.com1.upload.1" />
              </span>
              &nbsp;
              <span style={{ fontSize: 12, color: '#3463FC' }}>
                <FormattedMessage id="knowledge.QA.added.answer.edit.upload.2" />
              </span>
            </p>
            <p className="tipsTitle">
              <FormattedMessage
                id="new.customerDataGroupManagement.right.com1.upload.5"
                defaultMessage="文件说明："
              />
            </p>
            <p>
              <FormattedMessage
                id="new.customerDataGroupManagement.right.com1.upload.6"
                defaultMessage="1.仅支持一个文件上传"
              />
            </p>
            <p>
              <FormattedMessage
                id="new.customerDataGroupManagement.right.com1.upload.7"
                defaultMessage="2.Excel格式支持*.xls，*.xlsx两种格式"
              />
            </p>
            <p>
              <FormattedMessage
                id="new.customerDataGroupManagement.right.com1.upload.8"
                defaultMessage="3.请确保Excel中第一行为表头,例如:客户姓名，客户手机号等，具体表头您可以根据自己的实际情况进行自定义"
              />
            </p>
          </div>
        </Dragger>
        <p
          style={{ display: meterHeaderList.length > 0 ? 'block' : 'none' }}
          className={styles.uploadTips}
        >
          <FormattedMessage
            id="new.customerDataGroupManagement.right.com1.tips"
            defaultValue="请设置Excel的每一列在系统中对应的客户资料属性字段"
          />
        </p>
        {meterHeaderList.map((item, index) => {
          return (
            <p
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <span
                style={{
                  textAlign: 'end',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {/*<FormattedMessage*/}
                {/*  id="new.customerDataGroupManagement.right.com1.label"*/}
                {/*  defaultValue="Excel表头"*/}
                {/*/>*/}
                {item}：
              </span>
              <span style={{ width: '80%' }}>
                <Select
                  options={attributeDropdownList}
                  onChange={e => addFormQuestion(e, item)}
                  showSearch
                  fieldNames={{
                    label: 'customerExtDefName',
                    value: 'customerExtDefCode',
                    key: 'customerExtDefCode',
                  }}
                  filterOption={(inputValue, option) =>
                    option.customerExtDefName
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) >= 0
                  }
                />
              </span>
            </p>
          );
        })}
      </div>
    </div>
  );
});
// ****************************************************标签***************************************************************

const ComponentLabel = forwardRef((props, ref) => {
  // let termsTags = [];
  // if (childState) {
  //   // termsTags = childState.termsTags.join(',');
  //   termsTags = childState.termsTags;
  // }
  const [customerTagId, setCustomerTagId] = useState([]);
  const [unsubscribedValue, setUnsubscribedValue] = useState(true);
  const changeUnsubscribed = e => {
    setUnsubscribedValue(e.target.checked);
  };
  // 客户标签选择标签事件
  const handleSelectTag = value => {
    console.log('--------value--------------', value);
    setCustomerTagId(value);
  };
  useImperativeHandle(ref, () => ({
    termsTags: customerTagId,
    unsubscribedValue: unsubscribedValue,
  }));

  // 选中后tag标签颜色
  const tagRender = props => {
    const { label, value, closable, onClose } = props;
    let resultObj = findTagById(value);
    const onPreventMouseDown = event => {
      event.preventDefault();
      event.stopPropagation();
    };
    if (resultObj) {
      return (
        <Tag
          className={resultObj.tagColorCode || 'colorType1'}
          onMouseDown={onPreventMouseDown}
          closable={closable}
          onClose={onClose}
          style={{
            marginRight: 3,
          }}
        >
          {resultObj.tagContent}
        </Tag>
      );
    }
  };
  const findTagById = tagId => {
    for (let category of props.standardTagListRender) {
      for (let tag of category.tagList) {
        if (tag.tagId == tagId) {
          return tag;
        }
      }
    }
    return null; // 如果没有找到匹配的标签，返回 null
  };

  return (
    <div className={styles.componentBox}>
      <p className="blueBorder">
        <FormattedMessage id="new.customerDataGroupManagement.right.com.title" />
      </p>
      <p className={styles.formItem}>
        <FormattedMessage id="new.customerDataGroupManagement.left.btn.3" />
        {getLocale() === 'en-US' ? (
          <span style={{ marginRight: 8 }}>:</span>
        ) : (
          '：'
        )}
        <div className={styles.inputTagsBox}>
          <Select
            tagRender={tagRender}
            optionLabelProp="label"
            optionFilterProp="children"
            showArrow={false}
            style={{ marginRight: '8px' }}
            showSearch
            mode="multiple"
            filterOption={(inputValue, option) =>
              option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
            }
            allowClear
            onChange={handleSelectTag}
            placeholder={getIntl().formatMessage({
              id: 'allocation.please.select.customer.tag',
              defaultMessage: '请选择客户标签',
            })}
          >
            {props.standardTagList?.map(group => (
              <OptGroup
                key={group.categoryId}
                label={
                  group.categoryContent !== 'private_tag_category_code'
                    ? group.categoryContent
                    : getIntl().formatMessage({
                        id: 'tag.management.tab.private',
                        defaultValue: '私有标签',
                      })
                }
              >
                {group.tagList.map(option => (
                  <Option
                    key={option.tagId}
                    value={option.tagId}
                    label={option.tagContent}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <div
                        style={{
                          width: '12px',
                          height: '12px',
                          backgroundColor: option.tagColor,
                          marginRight: '4px',
                        }}
                      ></div>
                      <span>{option.tagContent}</span>
                    </div>
                  </Option>
                ))}
              </OptGroup>
            ))}
          </Select>
        </div>
        <Checkbox checked={unsubscribedValue} onChange={changeUnsubscribed}>
          <FormattedMessage id="new.customerDataGroupManagement.customer.eliminate.unsubscribed" />
        </Checkbox>
      </p>
      <div className={styles.formItemBtm}>
        <span className={styles.formItemBtmFont}>
          <FormattedMessage id="new.customerDataGroupManagement.right.com2.tips.new.add.1" />
          <br />
          <FormattedMessage id="new.customerDataGroupManagement.right.com2.tips.new.add" />
          <br />
          <FormattedMessage id="new.customerDataGroupManagement.right.com2.tips" />
        </span>
        <Button
          type={'primary'}
          onClick={() => {
            props.handleClickSearch4(customerTagId, unsubscribedValue);
          }}
          icon={<Search />}
        >
          <span style={{ marginLeft: 3 }}>
            <FormattedMessage
              id="new.customerDataGroupManagement.right.com2.btn"
              defaultMessage={'筛选'}
            />
          </span>
        </Button>
      </div>
    </div>
  );
});
// ****************************************************属性***************************************************************

const ComponentStats = forwardRef((props, ref) => {
  let [customerAgeStart, setCustomerAgeStart] = useState('');
  let [customerAgeEnd, setCustomerAgeEnd] = useState('');
  let [country, setCountry] = useState([]);
  let [sex, setSex] = useState('');
  let [career, setCareer] = useState([]);
  let [customHobby, setCustomHobby] = useState([]);
  const [unsubscribedValue, setUnsubscribedValue] = useState(true);
  const changeUnsubscribed = e => {
    setUnsubscribedValue(e.target.checked);
  };
  const changeCustomerAgeStart = e => {
    setCustomerAgeStart(e.target.value);
  };
  const changeCustomerAgeEnd = e => {
    setCustomerAgeEnd(e.target.value);
  };
  const changeCountryDef = value => {
    if (value !== undefined) {
      setCountry(value);
    } else {
      setCountry([]);
    }
  };
  const changeSex = value => {
    if (value !== undefined) {
      setSex(value);
    } else {
      setSex('');
    }
  };
  const changeCareer = value => {
    if (value !== undefined) {
      setCareer(value);
    } else {
      setCareer([]);
    }
  };
  const changeCustomHobby = value => {
    if (value !== undefined) {
      // setCustomHobby(value.join(','));
      setCustomHobby(value);
    } else {
      setCustomHobby([]);
    }
  };

  useImperativeHandle(ref, () => ({
    customerAgeStart: customerAgeStart,
    customerAgeEnd: customerAgeEnd,
    country: country,
    sex: sex,
    career: career,
    customHobby: customHobby,
    unsubscribedValue: unsubscribedValue,
  }));

  return (
    <div className={styles.componentBox}>
      <p className="blueBorder">
        <FormattedMessage id="new.customerDataGroupManagement.right.com.title" />
      </p>
      <div className={styles.formItemState}>
        <span className={styles.flexstate}>
          <span className={styles.wordKeep}>
            <FormattedMessage id="new.customerDataGroupManagement.right.com3.label.1" />
          </span>
          <div className={styles.wordInput}>
            <Input
              min={0}
              onChange={changeCustomerAgeStart}
              type={'number'}
              name="customerAgeStart"
              style={{ marginRight: '5px', width: '45%' }}
            />
            <FormattedMessage
              id="marketing.activities.time.line"
              defaultMessage="至"
            />
            <Input
              min={0}
              onChange={changeCustomerAgeEnd}
              type={'number'}
              name="customerAgeEnd"
              style={{ marginLeft: '5px', width: '45%' }}
            />
          </div>
        </span>
        <span className={styles.flexstate}>
          <span className={styles.wordKeep}>
            <FormattedMessage id="new.customerDataGroupManagement.right.com3.label.2" />
          </span>
          <div className={styles.wordInput}>
            <Select
              allowClear={true}
              style={{ width: '100%' }}
              options={[
                {
                  value: 1,
                  label: getIntl().formatMessage({
                    id: 'contact.customers.basic.information.sex1',
                    defaultValue: '男',
                  }),
                },
                {
                  value: 0,
                  label: getIntl().formatMessage({
                    id: 'contact.customers.basic.information.sex0',
                    defaultValue: '女',
                  }),
                },
                {
                  value: 2,
                  label: getIntl().formatMessage({
                    id: 'contact.customers.basic.information.sex2',
                    defaultValue: '其他',
                  }),
                },
              ]}
              onChange={value => changeSex(value)}
              placeholder={getIntl().formatMessage({
                id: 'new.customerDataGroupManagement.right.com3.placeholder.2',
              })}
            />
          </div>
        </span>
        <span className={styles.flexstate}>
          <span className={styles.wordKeep}>
            <FormattedMessage id="new.customerDataGroupManagement.right.com3.label.3" />
          </span>
          <div className={styles.wordInput}>
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              allowClear
              options={props.countryDefList}
              onChange={value => changeCountryDef(value)}
              showSearch
              placeholder={getIntl().formatMessage({
                id: 'new.customerDataGroupManagement.right.com3.placeholder.3',
              })}
              fieldNames={{
                label: 'countryName',
                value: 'countryId',
                key: 'countryId',
              }}
              filterOption={(inputValue, option) =>
                option.countryName
                  ?.toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              }
            />
          </div>
        </span>
      </div>
      <div className={styles.formItemState}>
        <span className={styles.flexstate}>
          <span className={styles.wordKeep}>
            <FormattedMessage id="new.customerDataGroupManagement.right.com3.label.4" />
          </span>
          <div className={styles.wordInput}>
            <Select
              mode="multiple"
              allowClear={true}
              style={{ width: '100%' }}
              options={props.careerDefList}
              onChange={e => changeCareer(e)}
              showSearch
              placeholder={getIntl().formatMessage({
                id: 'new.customerDataGroupManagement.right.com3.placeholder.4',
              })}
              fieldNames={{
                label: 'careerName',
                value: 'careerId',
                key: 'careerId',
              }}
              filterOption={(inputValue, option) =>
                option.careerName
                  ?.toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              }
            />
          </div>
        </span>
        <span className={styles.flexstate}>
          <span className={styles.wordKeep}>
            <FormattedMessage id="new.customerDataGroupManagement.right.com3.label.5" />
          </span>
          <div className={styles.wordInput}>
            <Select
              mode="multiple"
              allowClear={true}
              style={{ width: '100%' }}
              options={props.customHobbyList}
              onChange={e => changeCustomHobby(e)}
              showSearch
              placeholder={getIntl().formatMessage({
                id: 'new.customerDataGroupManagement.right.com3.placeholder.5',
              })}
              fieldNames={{
                label: 'hobbyName',
                value: 'hobbyId',
                key: 'hobbyId',
              }}
              filterOption={(inputValue, option) =>
                option.hobbyName
                  ?.toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              }
            />
          </div>
        </span>
        <span className={styles.flexstate}>
          <span className={styles.wordKeep} style={{ textAlign: 'left' }}>
            <Checkbox checked={unsubscribedValue} onChange={changeUnsubscribed}>
              <FormattedMessage id="new.customerDataGroupManagement.customer.eliminate.unsubscribed" />
            </Checkbox>
          </span>
        </span>
        {/*<div*/}
        {/*  style={{*/}
        {/*    flex: 1,*/}
        {/*    display: 'flex',*/}
        {/*    justifyContent: 'flex-end',*/}
        {/*    marginLeft: 16,*/}
        {/*  }}*/}
        {/*>*/}
        {/*  */}
        {/*</div>*/}
      </div>
      <div className={styles.formItemBtm}>
        <span className={styles.formItemBtmFont}>
          <FormattedMessage id="new.customerDataGroupManagement.right.com2.tips.new.add.2" />
        </span>
        <Button
          type={'primary'}
          onClick={() =>
            props.handleClickSearch(
              customerAgeStart,
              customerAgeEnd,
              country,
              sex,
              career,
              customHobby,
              unsubscribedValue,
            )
          }
          icon={<Search />}
        >
          <span style={{ marginLeft: 3 }}>
            <FormattedMessage
              id="new.customerDataGroupManagement.right.com2.btn"
              defaultMessage={'筛选'}
            />
          </span>
        </Button>
      </div>
    </div>
  );
});
// ****************************************************时间***************************************************************
const ComponentTime = forwardRef((props, ref) => {
  let [days, setDays] = useState(null);
  let [actions, setActions] = useState('');
  const [unsubscribedValue, setUnsubscribedValue] = useState(true);
  const changeUnsubscribed = e => {
    setUnsubscribedValue(e.target.checked);
  };
  const changeDays = e => {
    setDays(e.target.value);
  };
  const changeMove = value => {
    if (value !== undefined) {
      setActions(value);
    } else {
      setActions('');
    }
  };

  useImperativeHandle(ref, () => ({
    days: days,
    actions: actions,
    unsubscribedValue: unsubscribedValue,
  }));
  return (
    <div className={styles.componentBox}>
      <p className="blueBorder">
        <FormattedMessage id="new.customerDataGroupManagement.right.com.title" />
      </p>
      <div className={styles.formItemTime}>
        <span>
          <FormattedMessage id="new.customerDataGroupManagement.right.com4.jin" />
          <Input
            value={days}
            onChange={changeDays}
            style={{ width: 68, margin: '0 10px' }}
          />
          <FormattedMessage id="new.customerDataGroupManagement.right.com4.tiannei" />
          <Select
            allowClear={true}
            style={{ width: 350, margin: '0 10px' }}
            options={[
              {
                value: 'customActiveTime',
                label: getIntl().formatMessage({
                  id: 'customer.segmentation.customer.attitude.1',
                  defaultValue: '客户主动联系公司',
                }),
              },
              {
                value: 'systemPushTime',
                label: getIntl().formatMessage({
                  id: 'customer.segmentation.customer.attitude.2',
                  defaultValue: '系统推送营销消息给客户',
                }),
              },
              {
                value: 'companyActiveTime',
                label: getIntl().formatMessage({
                  id: 'customer.segmentation.customer.attitude.3',
                  defaultValue: '公司主动联系客户',
                }),
              },
              {
                value: 'customSubscribeTime',
                label: getIntl().formatMessage({
                  id: 'customer.segmentation.customer.attitude.4',
                  defaultValue: '客户订阅',
                }),
              },
              {
                value: 'customUnsubscribeTime',
                label: getIntl().formatMessage({
                  id: 'customer.segmentation.customer.attitude.5',
                  defaultValue: '客户退订',
                }),
              },
            ]}
            onChange={e => changeMove(e)}
            showSearch
            value={actions}
            // fieldNames={{
            //   label: 'q',
            //   value: 'qid',
            //   key: 'qid',
            // }}
            filterOption={(inputValue, option) =>
              option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
            }
          />
          <Checkbox checked={unsubscribedValue} onChange={changeUnsubscribed}>
            <FormattedMessage id="new.customerDataGroupManagement.customer.eliminate.unsubscribed" />
          </Checkbox>
        </span>
      </div>
      <div className={styles.formItemBtm}>
        <span className={styles.formItemBtmFont} style={{ marginTop: '10px' }}>
          <FormattedMessage id="new.customerDataGroupManagement.right.com2.tips.new.add.2" />
        </span>
        <Button
          type={'primary'}
          onClick={() =>
            props.handleClickSearch3(days, actions, unsubscribedValue)
          }
          icon={<Search />}
        >
          <span style={{ marginLeft: 3 }}>
            <FormattedMessage
              id="new.customerDataGroupManagement.right.com2.btn"
              defaultMessage={'筛选'}
            />
          </span>
        </Button>
      </div>
    </div>
  );
});
// ****************************************************订阅***************************************************************
const ComponentSubscribe = forwardRef((props, ref) => {
  const [radioFlag, setRadioFlag] = useState([]);
  const changeRadio = value => {
    if (value !== undefined) {
      setRadioFlag(value);
    } else {
      setRadioFlag([]);
    }
  };
  useImperativeHandle(ref, () => ({
    radioFlag: radioFlag,
  }));
  return (
    <div className={styles.componentBox}>
      <p className="blueBorder">
        <FormattedMessage id="new.customerDataGroupManagement.right.com.title" />
      </p>
      <div className={styles.formItemTime}>
        <span>
          <FormattedMessage id="new.customerDataGroupManagement.right.com5.label" />
          <Select
            mode="multiple"
            // value={radioFlag}
            allowClear={true}
            style={{ width: 232, margin: '0 10px' }}
            options={[
              {
                label: getIntl().formatMessage({
                  id:
                    'new.customerDataGroupManagement.customer.subscribed.select',
                  defaultMessage: '已订阅',
                }),
                value: 1,
              },
              {
                label: getIntl().formatMessage({
                  id: 'new.customerDataGroupManagement.customer.unsubscribed',
                  defaultMessage: '未订阅',
                }),
                value: 0,
              },
              {
                label: getIntl().formatMessage({
                  id:
                    'new.customerDataGroupManagement.customer.cancel.subscribed.select',
                  defaultMessage: '已退订',
                }),
                value: 2,
              },
            ]}
            onChange={value => changeRadio(value)}
            placeholder={getIntl().formatMessage({
              id:
                'new.customerDataGroupManagement.customer.subscribed.required',
              defaultMessage: '请选择客户订阅状态',
            })}
          />
        </span>
      </div>
      <div className={styles.formItemBtm}>
        <span className={styles.formItemBtmFont} style={{ marginTop: '10px' }}>
          <FormattedMessage id="new.customerDataGroupManagement.right.com2.tips.new.add.2" />
        </span>
        <Button
          type={'primary'}
          onClick={() => props.handleClickSearch2(radioFlag)}
          icon={<Search />}
        >
          <span style={{ marginLeft: 3 }}>
            <FormattedMessage
              id="new.customerDataGroupManagement.right.com2.btn"
              defaultMessage={'筛选'}
            />
          </span>
        </Button>
      </div>
    </div>
  );
});
// ****************************************************等级**************************************************************
const ComponentLv = forwardRef((props, ref) => {
  let [gradeSelectList, setGradeSelectList] = useState('');
  const [unsubscribedValue, setUnsubscribedValue] = useState(true);
  const changeUnsubscribed = e => {
    setUnsubscribedValue(e.target.checked);
  };
  const changeLv = value => {
    if (value !== undefined) {
      setGradeSelectList(value);
    } else {
      setGradeSelectList('');
    }
  };
  useImperativeHandle(ref, () => ({
    gradeSelectList: gradeSelectList,
    unsubscribedValue: unsubscribedValue,
  }));
  return (
    <div className={styles.componentBox}>
      <p className="blueBorder">
        <FormattedMessage id="new.customerDataGroupManagement.right.com.title" />
      </p>
      <div className={styles.formItemTime}>
        <span>
          <FormattedMessage id="new.customerDataGroupManagement.right.com6.label" />
          <Select
            allowClear={true}
            style={{ width: 232, margin: '0 10px' }}
            options={props.gradeList}
            onChange={value => changeLv(value)}
            showSearch
            placeholder={getIntl().formatMessage({
              id:
                'customerInformation.add.basicInformation.customerLevel.required',
              defaultMessage: '请选择客户等级',
            })}
            fieldNames={{
              label: 'gradeName',
              value: 'gradeId',
              key: 'gradeId',
            }}
            filterOption={(inputValue, option) =>
              option.gradeName
                .toLowerCase()
                .indexOf(inputValue.toLowerCase()) >= 0
            }
          />

          <Checkbox checked={unsubscribedValue} onChange={changeUnsubscribed}>
            <FormattedMessage id="new.customerDataGroupManagement.customer.eliminate.unsubscribed" />
          </Checkbox>
        </span>
      </div>
      <div className={styles.formItemBtm}>
        <span className={styles.formItemBtmFont} style={{ marginTop: '10px' }}>
          <FormattedMessage id="new.customerDataGroupManagement.right.com2.tips.new.add.2" />
        </span>
        <Button
          type={'primary'}
          onClick={() =>
            props.handleClickSearch1(gradeSelectList, unsubscribedValue)
          }
          icon={<Search />}
        >
          <span style={{ marginLeft: 3 }}>
            <FormattedMessage
              id="new.customerDataGroupManagement.right.com2.btn"
              defaultMessage={'筛选'}
            />
          </span>
        </Button>
      </div>
    </div>
  );
});
// ****************************************************自定义***************************************************************
const ComponentGroup = () => {
  let [radioFlag, setRadioFlag] = useState(1);
  const [conditionsList, setConditionsList] = useState([
    {
      left: '',
      middle: '',
      right: '',
    },
  ]);

  const changeRadio = () => {};
  const addConditions = () => {
    let data = [...conditionsList];
    data.push({
      left: '',
      middle: '',
      right: '',
    });
    setConditionsList(data);
  };
  return (
    <div className={styles.componentBox}>
      <p className="blueBorder">
        <FormattedMessage id="new.customerDataGroupManagement.right.com.title" />
      </p>
      <div className={styles.formItemTime} style={{ marginBottom: 20 }}>
        <span>
          <span className={styles.weightBold}>
            <FormattedMessage id="new.customerDataGroupManagement.right.com7.label" />
          </span>
          <Radio.Group onChange={() => changeRadio()} value={radioFlag}>
            <Radio value={1}>
              <FormattedMessage
                id="new.customerDataGroupManagement.right.com7.radio.1"
                defaultMessage="满足以下一个条件即可"
              />
            </Radio>
            <Radio value={2}>
              <FormattedMessage
                id="new.customerDataGroupManagement.right.com7.radio.2"
                defaultMessage="必须满足全部条件"
              />
            </Radio>
          </Radio.Group>
        </span>

        <Button
          type={'primary'}
          onClick={() => addConditions()}
          icon={<PlusOutlined />}
        >
          <span style={{ marginLeft: 6 }}>
            <FormattedMessage
              id="new.customerDataGroupManagement.btn.4"
              defaultMessage={'添加条件'}
            />
          </span>
        </Button>
      </div>
      {conditionsList.map((item, index) => {
        return (
          <div className={styles.formGroup}>
            <div className={styles.formGroupFlex}>
              <span style={{ fontWeight: 'bold' }}>
                <FormattedMessage id="new.customerDataGroupManagement.right.com7.map.title" />
                {index + 1}
              </span>
              {index !== 0 ? (
                <span>
                  <Button
                    type="primary"
                    danger
                    onClick={() => deleteContactLines(index)}
                    icon={<DeleteHome />}
                  >
                    <span style={{ marginLeft: 6 }}>
                      <FormattedMessage id="user.option.delete" />
                    </span>
                  </Button>
                </span>
              ) : (
                ''
              )}
            </div>
            <div className={styles.formGroupFlex}>
              <Select
                style={{ flex: 5, margin: '0 10px 0 0' }}
                options={[]}
                onChange={e => changeLv(e)}
                showSearch
                placeholder={getIntl().formatMessage({
                  id:
                    'customerInformation.add.basicInformation.customerLevel.required',
                  defaultMessage: '请选择客户等级',
                })}
                // fieldNames={{
                //   label: 'q',
                //   value: 'qid',
                //   key: 'qid',
                // }}
                filterOption={(inputValue, option) =>
                  option.value
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
              <Select
                style={{ flex: 2, margin: '0 10px 0 0' }}
                options={[]}
                onChange={e => changeLv(e)}
                showSearch
                placeholder={getIntl().formatMessage({
                  id:
                    'customerInformation.add.basicInformation.customerLevel.required',
                  defaultMessage: '请选择客户等级',
                })}
                // fieldNames={{
                //   label: 'q',
                //   value: 'qid',
                //   key: 'qid',
                // }}
                filterOption={(inputValue, option) =>
                  option.value
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
              <Select
                style={{ flex: 5 }}
                options={[]}
                onChange={e => changeLv(e)}
                showSearch
                placeholder={getIntl().formatMessage({
                  id:
                    'customerInformation.add.basicInformation.customerLevel.required',
                  defaultMessage: '请选择客户等级',
                })}
                // fieldNames={{
                //   label: 'q',
                //   value: 'qid',
                //   key: 'qid',
                // }}
                filterOption={(inputValue, option) =>
                  option.value
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
            </div>
          </div>
        );
      })}
      <div style={{ display: 'flex', justifyContent: 'end' }}>
        <Button
          type={'primary'}
          onClick={() => props.goSearch()}
          icon={<Search />}
        >
          <span style={{ marginLeft: 3 }}>
            <FormattedMessage
              id="new.customerDataGroupManagement.right.com2.btn"
              defaultMessage={'筛选'}
            />
          </span>
        </Button>
      </div>
    </div>
  );
};
