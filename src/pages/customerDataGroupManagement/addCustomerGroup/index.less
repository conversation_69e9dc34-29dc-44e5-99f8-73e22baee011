.contentBox {
  padding: 20px 0 0 0;
  height: calc(100vh - 60px);
  background-color: #f5f5f5;
  overflow: hidden;

  .headContent {
    // width: 100%;
    // height: 80px;
    // background: #fff;
    // border-radius: 6px;
    // padding: 24px 24px 0px;
    width: 100%;
    background: #ffffff;
    border-radius: 6px;
    border: 0px solid rgba(0, 0, 0, 0.88);
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;

    .ghostBtn {
      margin-right: 16px;
    }

    label {
      color: rgba(0, 0, 0, 0.88);
      float: left;
      line-height: 32px;
      margin-right: 8px;
    }

    :global {
      .ant-input-affix-wrapper {
        border-radius: 6px;
      }

      .ant-input {
        height: 24px;
      }

      .ant-select {
        width: 25%;
        margin-right: 20px;
      }

      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        border-radius: 6px;
      }
    }
  }

  .contentBoxDivLeft {
    width: 26%;
    padding: 24px 20px 24px;
    height: 100%;
    float: left;
    background-color: #f5f5f5;
    position: relative;
    z-index: 999;

    //position: fixed;

    .contentBoxDivLeftTitle {
      font-size: 20px;
      color: #333;
    }

    .contentBoxDivLeftTitleTips {
      font-size: 14px;
      color: #666;
    }

    .gridContainer {
      width: 80%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      /* 两列，每列平均分配剩余空间 */
      grid-template-rows: repeat(4, 1fr);
      /* 四行，每行平均分配剩余空间 */
      gap: 20px;
      /* 设置行和列之间的间距 */
      margin-top: 25px;
    }

    .gridItem {
      //display: flex;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 40px;
      border-radius: 4px;
      background-color: #fff;
      font-size: 12px;
      background-repeat: no-repeat;
      background-position: center;
      cursor: pointer;
      line-height: 40px;

      .iconSpan {
        float: left;
        margin-left: 5px;
        margin-top: 3px;
      }

      span {
        margin-right: 4px;
      }
    }
  }

  .contentBoxDivRight {
    float: right;
    width: 74%;
    background: rgba(52, 99, 252, 0.05);
    position: relative;
    height: 100%;
    overflow-y: scroll;

    .contentBoxDivRightTop {
      min-height: 100%;
      padding: 20px 0 0 20px;

      .componentBox {
        padding: 20px;
        background-color: #fff;
        border-radius: 6px;
        margin-bottom: 20px;
        // max-height: 78vh;
        // overflow-y: scroll;

        .uploadTips {
          margin-top: 30px;
          font-size: 14px;
          font-weight: bold;
          color: #333;
        }

        .formItem {
          display: flex;
          align-items: baseline;

          .inputTagsBox {
            //flex-grow: 1;
            margin-right: 8px;
            width: 75%;

            :global {
              .ant-select {
                width: 100%;
                margin-bottom: 10px;
                margin-top: 10px;
              }

              .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
                border-color: #40a9ff;
                border-right-width: 1px;
                box-shadow: none !important;
              }

              .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
                .ant-select-selector {
                box-shadow: none !important;
              }

              .ant-select-selector {
                border-radius: 6px;
                // border: 1px solid #e6e6e6;
                box-shadow: none !important;
                background: #fff;
                font-size: 12px;
                height: 32px;
                overflow: hidden;
                overflow-y: scroll;
                scrollbar-width: 0px;
                -ms-overflow-style: none;
              }
              .ant-select-selector::-webkit-scrollbar {
                display: none;
              }
              .ant-tag {
                font-size: 12px;
              }
              .ant-tag:first-child {
                margin-left: 0px;
                margin-top: 0px;
              }
            }
          }

          .wordKeep {
            word-break: keep-all;
            text-align: end;
            margin-right: 3px;
          }
        }

        .formItemBtm {
          display: flex;
          justify-content: space-between;
          align-items: end;

          .formItemBtmFont {
            font-size: 12px;
            color: #999;
            margin-right: 20px;
          }
        }

        .formItemTime {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .weightBold {
            font-weight: bold;
          }
        }

        .formItemState {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          .wordKeep {
            word-break: keep-all;
            text-align: end;
            margin-right: 8px;
            display: inline-block;
            flex: 1;
          }

          .wordInput {
            display: inline-block;
            flex: 6;
          }

          .flexstate {
            margin-left: 16px;
            flex: 1;
            display: flex;
            justify-content: flex-end;
            align-items: center;
          }

          :global {
            .ant-select {
              margin: 0;
            }
          }
        }

        .formGroup {
          background-color: #f9f9f9;
          margin-bottom: 20px;
          padding: 10px;

          .formGroupFlex {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }

          :global {
            .ant-btn-dangerous {
              color: #fff !important;
              border-color: #ff4d4f !important;
              background: #ff4d4f !important;
              display: flex;
              align-items: center;
              width: 60px;
              height: 28px;
              padding: 0 4px;

              svg {
                width: 1.3em;
                height: 1.3em;
              }
            }
          }
        }

        :global {
          .ant-btn {
            display: flex;
            align-items: center;

            svg path {
              fill: #fff;
            }
          }

          .ant-input {
            border-radius: 6px;
          }

          .ant-upload-drag {
            background-color: #fff;
          }

          .ant-upload-drag-container {
            display: flex;
            flex-direction: row;
            justify-content: center;
            margin: 20px 50px;
          }

          .ant-upload-drag-icon {
            svg:not(:root) {
              width: 120px !important;
              height: 120px !important;
            }
          }

          .ant-upload-text {
            text-align: left;
            padding-top: 10px;
            margin-left: 20px;
            line-height: 40px;

            .tipsTitle {
              font-weight: 500;
            }

            p {
              margin-bottom: 2px;
              line-height: 22px;
              font-size: 12px;
              color: #999;
            }
          }

          .ant-select {
            width: 100%;
            font-size: 12px;
          }

          .ant-select:not(.ant-select-customize-input) .ant-select-selector {
            border-radius: 6px;
          }

          //.ant-tag {
          //  border: 1px #13c825 solid;
          //  background-color: #f3fcf4;
          //  color: #13c825;
          //}
          //
          //.ant-tag-close-icon {
          //  color: #13c825 !important;
          //}
        }
      }

      .contentBoxDivRightMain {
        background: #fff;
        border-radius: 6px;
        padding: 24px 24px 24px;
        display: flex;
        flex-direction: column;
        // height: calc(100vh - 120px);
        overflow: scroll;
        // margin-top: 16px;
        // overflow: hidden;
        // overflow-y: scroll;
        // scrollbar-width: 0px;
        // -ms-overflow-style: none;
        // .contentBoxDivTitle{

        // }
        .contentBoxDivTitle {
          width: 100%;

          :global {
            .ant-btn-primary {
              float: right;
              font-size: 12px;
            }

            .ant-btn-background-ghost {
              color: #3463fc;
            }

            .ant-btn-primary:hover {
              color: #fff !important;
            }
          }
        }

        .tableContent {
          width: 100%;
          flex: 1 1 auto;
          margin-bottom: 35px;
          // height: 550px;
          // overflow: hidden;
          // overflow-y: scroll;
          // scrollbar-width: 0px;
          // -ms-overflow-style: none;

          .userType {
            color: #409eff;
          }

          .connectList {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .operationArray {
            display: flex;
            flex-direction: row;

            .operationText {
              margin-right: 12px;
            }
          }

          :global {
            .ant-table-thead > tr > th {
              background-color: #f3f7fe !important;
            }
          }
        }

        .tableContent::-webkit-scrollbar {
          display: none;
        }

        :global {
          .ant-btn {
            margin-left: 10px;
          }
        }
      }
    }

    .contentBoxDivRightFoot {
      position: fixed;
      bottom: 0;
      backdrop-filter: blur(40px);
      background: rgba(52, 99, 252, 0.05);
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 72px;
      width: -webkit-fill-available;
      right: 0;

      :global {
        .ant-btn {
          margin-right: 20px;
        }
      }
    }
  }

  :global {
    .ant-select-selection-item {
      font-size: 12px;
    }

    .ant-btn-dangerous {
      color: #ff4d4f !important;
      border-color: #ff4d4f !important;
      background: #fff !important;
      display: flex;
      align-items: center;

      svg {
        width: 1.3em;
        height: 1.3em;
      }
    }

    .ant-modal-body {
      padding-top: 0;

      .centerDetail {
        background-color: #f9f9f9;
        border-radius: 4px;
        height: 436px;
        width: 100%;
        overflow-y: scroll;
        padding: 20px;
      }

      .centerDetail::-webkit-scrollbar {
        width: 0;
      }

      .footerDetail {
        margin-top: 20px;
        text-align: center;
      }
    }
  }
}

.main {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  margin-bottom: 0.75rem;

  .avator {
  }

  .box {
    display: flex;
    flex-direction: column;
    margin-left: 0.5rem;

    .box_role {
      font-size: 12px;
    }

    .box_content {
      margin-top: 0.5rem;
      padding: 0.5rem;
      font-size: 12px;
      background-color: rgba(173, 48, 229, 0.05);
      border-radius: 0.2rem 0.5rem 0.5rem 0.5rem;
      border: 0.1rem solid #ad30e5;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      max-width: 373px;

      img {
        width: 100%;
        height: auto;
      }

      :global {
        p {
          margin-bottom: 0;
        }
      }
    }

    .box_content_img {
      margin-top: 0.5rem;
      border-radius: 0.2rem 0.5rem 0.5rem 0.5rem;
      width: fit-content;

      img {
        width: 93%;
        height: auto;
      }

      video {
        width: 93%;
        height: auto;
      }
    }

    .box_list {
      .box_list_it {
        margin: 0.5rem 0.5rem 0 0;
        background-color: #ad30e5;
        color: #fff;
        font-size: 12px;
        padding: 0.4rem;
        border-radius: 0.25rem;
        width: fit-content;
        display: inline-block;
      }
    }
  }
}

.userMain {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  margin-bottom: 0.75rem;
  justify-content: flex-end;

  .avator {
  }

  .box {
    display: flex;
    flex-direction: column;
    margin-right: 0.5rem;

    .box_role {
      font-size: 12px;
      text-align: end;
    }

    .box_content {
      margin-top: 0.5rem;
      padding: 0.5rem;
      color: #fff;
      font-size: 12px;
      background-color: #ad30e5;
      border-radius: 0.5rem 0.2rem 0.5rem 0.5rem;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);

      img {
        width: 100%;
        height: auto;
      }
    }
  }
}
