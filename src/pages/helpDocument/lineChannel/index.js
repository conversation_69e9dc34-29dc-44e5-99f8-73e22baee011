import React, { useState, useRef, useEffect } from 'react';
import { Button, notification } from 'antd';
import styles from '../index.less';
import {
  useDispatch,
  getIntl,
  FormattedMessage,
  Link as LinkUrl,
  useIntl,
} from 'umi';
import Header from '@/components/Header';
import LeftMenu from '../components/leftMenu';
import { Element, Events, Link } from 'react-scroll';
import { isCNDomain } from '@/utils/utils';

const LineChannelDocument = () => {
  const dispatch = useDispatch();
  const editorRef = useRef(null);
  const [htmlContent, setHtmlContent] = useState('');

  useEffect(() => {
    Events.scrollEvent.register('begin', (...args) => {
      console.log('begin', args);
    });

    Events.scrollEvent.register('end', (...args) => {
      console.log('end', args);
    });

    return () => {
      Events.scrollEvent.remove('begin');
      Events.scrollEvent.remove('end');
    };
  }, []);
  const intl = useIntl();
  useEffect(() => {}, [intl]);

  return (
    <div className={styles.helpDocumentContainer}>
      <Header />
      <div className={styles.placeholder}></div>
      <div className={styles.contentContainer}>
        <div className={styles.leftNav}>
          <div className={styles.navTitle}>
            <FormattedMessage id="help.document.title" />
          </div>
          <LeftMenu />
        </div>
        <div className={styles.rightContainer}>
          <div className={styles.privacyPolicy}>
            <div className={styles.privacyPolicyTitle}>
              <FormattedMessage id="help.document.line.title" />
            </div>
            <div className={styles.contentWrapper}>
              <div className={styles.content}>
                <Element name="one" className={styles.element}>
                  <h2>
                    <FormattedMessage id="userTerms.section.one.prefix" />
                    <FormattedMessage id="help.document.line.left.menu" />
                  </h2>
                  <h3>
                    <FormattedMessage id="help.document.line.step.1.title" />
                  </h3>
                  <p>
                    <FormattedMessage
                      id="help.document.line.step.1.text.1"
                      values={{
                        a: chunks => (
                          <a
                            href="https://tw.linebiz.com/account/"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {chunks}
                          </a>
                        ),
                      }}
                    />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/252ce75f-ed27-4a9a-a7ea-df91c804aada.jpg" />
                  <h3>
                    <FormattedMessage id="help.document.line.step.2.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.1.text.2" />
                  </p>
                  <p>
                    <FormattedMessage
                      id="help.document.line.step.1.text.3"
                      values={{
                        a: chunks => (
                          <a
                            href="https://help2.line.me/business_id/web?lang=zh-TW"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {chunks}
                          </a>
                        ),
                      }}
                    />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/29b97149-cec3-4797-be80-7f724b746c4a.jpg" />
                  <h3>
                    <FormattedMessage id="help.document.line.step.3.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.1.text.4" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/8c573f5e-9efd-4a2e-9671-a286a49ebc7a.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.1.text.5" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/6b8a91f3-affc-4e4e-a161-cb7a6b65042b.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.1.text.6" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/5e4c3e81-c26a-4e8c-99d1-ea3f9a353ac2.jpg" />
                </Element>
                <Element name="two" className={styles.element}>
                  <h2>
                    <FormattedMessage id="userTerms.section.two.prefix" />
                    <FormattedMessage id="help.document.line.left.menu.1" />
                  </h2>
                  <p>
                    <FormattedMessage
                      id="help.document.line.step.2.text"
                      values={{
                        a: chunks => (
                          <a
                            href="https://account.line.biz/login?redirectUri=https%3A%2F%2Fmanager.line.biz%2F"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {chunks}
                          </a>
                        ),
                      }}
                    />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.1" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/fe11aa40-65c1-4798-840b-b20ea5c01c8d.jpg" />
                  <h3>
                    <FormattedMessage id="help.document.line.step.1.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.2" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/9e4ebe6f-87f4-435e-9125-8dcfb533e5ed.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.3" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/da59717a-51c8-47ba-b00e-52701c0baa6e.jpg" />
                  <h3>
                    <FormattedMessage id="help.document.line.step.2.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.4" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/2d132db4-d1f7-467b-8e3f-46a89af804a6.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.5" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/69a5c234-aca3-4da8-af92-d0174a479272.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.6" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/690ea1b9-df5e-4fee-b535-de8e3b87565b.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.7" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/f605702a-e49e-43e0-976b-a115340a7c10.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.8" />
                  </p>
                  <h3>
                    <FormattedMessage id="help.document.line.step.3.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.9" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.10" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.11" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.12" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.13" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.14" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.15" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/12134add-18fb-4492-ab02-07b3b06aabbb.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.16" />
                  </p>
                  <h3>
                    <FormattedMessage id="help.document.line.step.4.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.17" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/76124ed2-4c3e-4d9e-a2a9-797932bef73d.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.18" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/22d0e44c-4a95-4060-997b-c2e587643911.jpg" />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.19" />
                  </p>
                  <img src="https://www.connectnowai.com/doc/line/6e8d7f5d-f4b7-4352-8b77-1f0abda9c47e.jpg" />
                  <h3>
                    <FormattedMessage id="help.document.line.step.5.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.20" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.10" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.11" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.12" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.13" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.21" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/ab9b32d6-8a88-4fe4-8fe4-2cc284bd81d8.jpg'
                    }
                  />
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/6d0ede38-266b-4550-9ce9-af047b84327b.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.6.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.22" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/0ffcec08-5b96-46d6-a611-7cfa60ab7819.jpg'
                    }
                  />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.23" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/8dd2111b-8edf-4721-9100-54ec64ef4a52.jpg'
                    }
                  />
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.24" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/bb002537-e419-4b62-80b6-57c633376dfc.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.7.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.25" />
                  </p>
                  <p>
                    <FormattedMessage
                      id="help.document.line.step.2.text.26"
                      values={{
                        a: chunks => (
                          <a
                            href="https://manager.line.biz"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {chunks}
                          </a>
                        ),
                      }}
                    />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.2.text.27" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/be7fae52-a14a-4a48-ac56-427a6abd20c9.jpg'
                    }
                  />
                </Element>
                <Element name="three" className={styles.element}>
                  <h2>
                    <FormattedMessage id="userTerms.section.three.prefix" />
                    <FormattedMessage id="help.document.line.left.menu.2" />
                  </h2>
                  <h3>
                    <FormattedMessage id="help.document.line.step.1.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/6f9c4fc3-fc3c-4881-9eaa-4c4f9304442b.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.2.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.1" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/9e9448e9-aad2-4ba1-9c45-3b6151a8f1b2.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.3.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.2" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/0f8f4082-1142-448c-86aa-695b0891d217.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.4.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.3" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/b808048b-d082-4b73-9b0d-ffb365dfab15.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.5.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.4" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/16cec86f-c5af-4963-81a2-db6c2404c788.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.6.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.5" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.6" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/d7d1e3fa-01dd-4997-8943-ffdc679075f3.jpg'
                    }
                  />
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.7" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/28e1f9f6-1fe4-4410-8f29-17ad71d232ef.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.7.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.8" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.9" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/debd4f10-0f5e-41cc-b7d7-c15e7a823e79.jpg'
                    }
                  />
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/a144fc6b-6ce6-4e3d-a642-a987a1bc404d.jpg'
                    }
                  />
                  <h3>
                    <FormattedMessage id="help.document.line.step.9.title" />
                  </h3>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.10" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.11" />
                  </p>
                  <img
                    src={
                      'https://www.connectnowai.com/doc/line/f827ab0f-f79e-4f6b-85bb-a86e8e3efe61.jpg'
                    }
                  />
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.12" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.13" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.14" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.15" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.16" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.17" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.18" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.19" />
                  </p>
                  <p>
                    <FormattedMessage id="help.document.line.step.3.text.20" />
                  </p>
                </Element>
                {/* 日期 */}
                {/*<div className={styles.date}>*/}
                {/*  <div className={styles.updateDate}>*/}
                {/*    <FormattedMessage id="document.updateDate" />*/}
                {/*  </div>*/}
                {/*  <div className={styles.effectiveDate}>*/}
                {/*    <FormattedMessage id="document.effectiveDate" />*/}
                {/*  </div>*/}
                {/*</div>*/}
              </div>
              <div className={styles.nav}>
                {/*<div className={styles.navTitle}>*/}
                {/*  <FormattedMessage id="userTerms.nav.title" />*/}
                {/*</div>*/}
                <ul>
                  <li>
                    <Link
                      activeClass={styles.active}
                      to="one"
                      spy={true}
                      smooth={true}
                    >
                      <FormattedMessage id="userTerms.section.one.prefix" />
                      <span>
                        <FormattedMessage id="help.document.line.left.menu" />
                      </span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      activeClass={styles.active}
                      to="two"
                      spy={true}
                      smooth={true}
                    >
                      <FormattedMessage id="userTerms.section.two.prefix" />
                      <span>
                        <FormattedMessage id="help.document.line.left.menu.1" />
                      </span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      activeClass={styles.active}
                      to="three"
                      spy={true}
                      smooth={true}
                    >
                      <FormattedMessage id="userTerms.section.three.prefix" />
                      <span>
                        <FormattedMessage id="help.document.line.left.menu.2" />
                      </span>
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LineChannelDocument;
