// 分享码弹窗组件样式
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContainer {
  // width: 638px;
  // height: 243px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  padding: 20px;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .modalTitle {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }

  .closeButton {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.modalTitleLevel {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 10px 0;
}

.inputGroup {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 10px;
  border-radius: 10px;
  border: 1px solid #e6e6e6;
  padding: 10px;
}

.codeInput {
  width: 48px;
  height: 48px;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  border-radius: 8px;
  outline: none;
  transition: all 0.2s ease;

  // 编辑模式样式
  &.editMode {
    border: 2px solid #d1d5db;
    background-color: #f9f9f9;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
      transform: scale(1.02);
    }

    &:hover:not(:focus) {
      border-color: #9ca3af;
    }
  }

  // 展示模式样式
  &.displayMode {
    border: none;
    color: #1f2937;
    cursor: default;
    font-weight: 700;
    letter-spacing: 1px;
    font-size: 40px;
    font-family: 'Microsoft YaHei';
  }
}

.remarks {
  color: #999;
  font-family: 'Microsoft YaHei';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px;
}

.buttonGroup {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0 0;

  .button {
    cursor: pointer;
    font-family: 'Microsoft YaHei';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
    /* 100% */
    padding: 9px 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    &:nth-child(1) {
      background: #fff;
      border: 1px solid #3463fc;
      color: #3463fc;
    }

    &:nth-child(2) {
      background: #3463fc;
      border: none;
      color: #fff;
    }
  }
}
