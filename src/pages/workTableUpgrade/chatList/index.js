import React, {
  useImperativeHandle,
  forwardRef,
  useState,
  useEffect,
  useRef,
} from 'react';
import styles from './index.less';
import { v4 as uuidv4 } from 'uuid';

import HOCAuth from '@/components/HOCAuth/index';
import {
  Input,
  Button,
  notification,
  Select,
  Tabs,
  Radio,
  Spin,
  Checkbox,
  DatePicker,
  Dropdown,
  Popover,
  Modal,
  Form,
  Row,
  Col,
  Tooltip,
  Tag,
} from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import NoDataImg from '@/assets/no-data.png';

import {
  PhoneSvg,
  ChatSvg,
  UnselectChatSvg,
  SortSvg,
  SortSvgChecked,
  ChooseOrderIcon,
  DownIconChecked,
  UpIconChecked,
  UpIcon,
  DownIcon,
  RefreshSvg,
  SelectTriangle,
  UnSelectTriangle,
  EmailSvg,
  Ticket,
  EmailNormalSvg,
  UnPhoneSvg,
  SettingSvg,
  UnSettingSvg,
  Fenpei,
  timeSurplusSolve,
  timeSurplusReplay,
  timeOverSolve,
  timeOverReplay,
  PinMessage,
  BatchReply,
  BatchTag,
  PinMessageNo,
} from './icon.js';
import { parsePhoneNumberFromString } from 'libphonenumber-js';

import Avatar from '@/assets/user-avatar.png';
import CallComponents from './callComponents/index';
import TwilioComponents from './twilioComponents/index';

import SettingComponents from './settingComponents/index';

import { ReactComponent as Search } from '@/assets/Search.svg';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';

import AppChatOutlinedIcon, {
  ReactComponent as AppChatOutlined,
} from '@/assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon, {
  ReactComponent as WebVideoOutlined,
} from '@/assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon, {
  ReactComponent as AppVideoOutlined,
} from '@/assets/AppVideoOutlined.svg';
import AwsChannelIcon, {
  ReactComponent as AmazonOutlined,
} from '@/assets/aws-channel-icon.svg';

import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';
import EmailIcon from '@/assets/email.svg';
import FacebookIcon from '@/assets/facebook.svg';
import WhatsAppIcon from '@/assets/whats-app.svg';
import TwitterIcon from '@/assets/twitter.svg';
import LineIcon from '@/assets/line.svg';
import PhoneIcon from '@/assets/phone.svg';
import ChatIcon from '@/assets/chat-icon.jpg';
import TikTokIcon from '@/assets/new-tiktok-icon.svg';
import DiscordIcon from '@/assets/new-discord-icon.svg';
import {
  connect,
  useDispatch,
  useSelector,
  getIntl,
  FormattedMessage,
  history,
} from 'umi';
import { displayDateTime } from '@/utils/utils';
import moment from 'moment';
import Picker from '@emoji-mart/react';
import data from '@emoji-mart/data';

const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Option, OptGroup } = Select;

import { IMWebClient } from '../../../pages/agentImChat/sdk/im.web.sdk.js';

const ChatList = forwardRef(
  (
    {
      handleSessionItemClick,
      handleSessionItemPendingClick,
      handleSessionItemRobotClick,
      handleChangeTab,
      globalList,
      setGlobalList,
      setWaitPendingList,
      handledPendingList,
      setRobotListParent,
      robotListParent,
      num,
      onLeftWidth,
      getPhoneInfo,
      workTableTabProcesOrPendValue,
      deleteTicket,
      handleAddMessageNew,
      handleGetTranslateText,
      handleSetTopTicket,
      handleSetNoTopTicket,
    },
    ref,
  ) => {
    const [indeterminate, setIndeterminate] = useState(false);
    const [checkAll, setCheckAll] = useState(false);
    const callComponentRef = useRef(null);
    // 未分配下全选等状态初始值
    const [checkedPendingList, setCheckedPendingList] = useState([]); //  全选
    const [checkedList, setCheckedList] = useState([]); //  全选
    const [indeterminatePending, setIndeterminatePending] = useState(false);
    const [checkAllPending, setCheckAllPending] = useState(false);
    const [phoneCurrentInfo, setPhoneCurrentInfo] = useState({});
    const dispatch = useDispatch();
    const {
      user,
      languageListR,
      channelTypeList,
      emailSelectTicketData,
      refreshLeftEmailList,
    } = useSelector(({ layouts, knowledgeQA, worktable }) => ({
      user: layouts.user,
      languageListR: knowledgeQA.languageList,
      channelTypeList: layouts.channelTypeList,
      emailSelectTicketData: worktable.emailSelectTicketData,
      refreshLeftEmailList: worktable.refreshLeftEmailList,
    }));
    const [leftWidth, setLeftWidth] = useState(290); // 第一栏初始宽度
    let [loading, setLoading] = useState(false);
    let [formHide, setFormHide] = useState(true);
    let [workRecordTypeList, setWorkRecordTypeList] = useState([]);
    let [commentList, setCommentList] = useState([]); //globalList
    let [total, setTotal] = useState(0);
    let [hasMore, setHasMore] = useState(true);
    let [nextData, setNextData] = useState(true);
    let [pageNum, setPageNum] = useState(1);

    let [pendingList, setPendList] = useState([]); // 未分配列表数据
    let [pendTotal, setPendTotal] = useState(0);
    let [pendTotalTab, setPendTotalTab] = useState(0); // 未分配总数,不包括筛选
    let [robotList, setRobotList] = useState([]); // 机器人列表数据
    let [robotTotal, setRobotTotal] = useState(0);
    let [robotTotalTab, setRobotTotalTab] = useState(0);

    let [processTotalTab, setProcessTotalTab] = useState(0); // 处理中总数,不包括筛选
    let [channelType, setChannelType] = useState('');
    let [search, setSearch] = useState('');
    let [orderType, setOrderType] = useState(1);
    let [workRecordTypeCode, setWorkRecordTypeCode] = useState('');
    let [customerTagIds, setCustomerTagIds] = useState([]);
    let [ticketTagIds, setTicketTagIds] = useState([]);

    let [startTime, setStartTime] = useState(null);
    let [endTime, setEndTime] = useState(null);
    const [open, setOpen] = useState(false);
    let [ticketType, setTicketType] = useState();
    let [checkValue, setCheckValue] = useState('1'); // 处理中分配中机器人
    let [workTableTabValue, setWorkTableTabValue] = useState();
    let [emailTicketList, setEmailTicketList] = useState([]);
    let [selectEmailTicketData, setSelectEmailTicketData] = useState({});
    let [emailTicketNum, setEmailTicketNum] = useState(0);
    let [emailSearch, setEmailSearch] = useState(false);
    let [chatSearch, setChatSearch] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [isHoveredDown, setIsHoveredDown] = useState(false);
    const [isAllocateModal, setAllocateModal] = useState(false);
    const [allocateLoading, setAllocateLoading] = useState(false);
    const [isBathReplyModal, setIsBathReplyModal] = useState(false);

    const [loadingModal, setLoadingModal] = useState(false);
    const [ruleTypeValue, setRuleTypeValue] = useState(0);
    // 座席list
    const [seatsUserList, setSeatsUserList] = useState([]);
    // 座席团队list
    const [callDeptList, setCallDeptList] = useState([]);
    const [isHoveredUp, setIsHoveredUp] = useState([]);
    const [selectedTicketIds, setSelectedTicketIds] = useState([]);
    const [rangeValue, setRangeValue] = useState(null); // 初始值为
    const formWorkOrderUpgradeRef = useRef(null);
    const [inputMessage, setInputMessage] = useState('');
    const [showEmoji, setShowEmoji] = useState(false);
    const [standardTagListRender, setStandardTasListRender] = useState([]); // 客户标签输入框渲染
    const [standardTagList, setStandardTasList] = useState([]); // 客户标签
    const [orginStandardTagList, setOriginStandardTasList] = useState([]); // 原始客户标签
    const [ticketTagListRender, setTicketTagListRender] = useState([]); // 工单标签输入框渲染
    const [ticketTagList, setTicketTagList] = useState([]); // 工单标签
    const [orginTicketTagList, setOriginTicketTagList] = useState([]); // 原始工单标签
    // null，表示未选择
    // // 监听工作台打开
    // useEffect(() => {
    //   //查询处理中、未分配数量
    //   queryWorkBenchTicketTotalCount();
    //   console.log('isShowWorkTable');
    // }, [isShowWorkTable]);
    useEffect(() => {
      const getActiveType = async () => {
        const result = await IMWebClient.getActiveType({
          companyId: user?.companyId,
          agentId: user?.userId,
        });
        if (result.code === 200) {
          console.log(result, user, 'getActiveType');

          if (user.roleList[0].roleId === '1005') {
            setTicketType(3);
            //查询机器人数量
            queryRobotTicketTotalCount();
          } else {
            setTicketType(+result.data);
          }
        }
      };
      getActiveType();
    }, [user]);
    useEffect(() => {
      //查询工单类型列表
      queryWorkRecordType();
      //查询处理中、未分配数量
      queryWorkBenchTicketTotalCount();
      // 查询客服列表
      querySeatsUserList();
      // 查询座席团队列表
      queryCallDeptList();
      // 查询客户标签
      queryAllStandardTag();
      //查询工单标签
      queryTicketTagSelectList();
    }, []);

    useEffect(() => {
      queryEmailTotalCount();
      const timer = setInterval(() => {
        queryEmailTotalCount();
      }, 1000 * 60 * 2);
      return () => clearInterval(timer);
    }, []);
    // //坐席没有电话权限时默认选择已有权限 后端存储：3，电话；2，邮件；1：聊天。前后端数据存储对应不一致，这里对齐
    useEffect(() => {
      if (user.agentAccessChannel?.search('3') === -1) {
        if (user.agentAccessChannel?.search('1') === -1) {
          setWorkTableTabValue(3);
          dispatch({
            type: 'worktable/saveWorkTableTabValue',
            payload: 3,
          }); // tab值存入全局
        } else {
          setWorkTableTabValue(2);
          dispatch({
            type: 'worktable/saveWorkTableTabValue',
            payload: 2,
          }); // tab值存入全局
        }
      } else {
        setWorkTableTabValue(1);
        dispatch({
          type: 'worktable/saveWorkTableTabValue',
          payload: 1,
        }); // tab值存入全局
      }
    }, [user.agentAccessChannel]);
    // 存在重复代码，暂时注释 3.21
    // useEffect(() => {
    //   console.log('setCheckValue', workTableTabValue, checkValue);
    //   if (workTableTabValue == 2) {
    //     if (+checkValue === 1) {
    //       setPageNum(1);
    //       //获取处理中列表
    //       queryProcessingWorkOrderList(1, false); //处理中
    //     } else if (+checkValue === 2) {
    //       setPageNum(1);
    //       //获取处理中列表
    //       queryPendingWorkOrderList(1, false); //处理中
    //     } else if (+checkValue === 3) {
    //       setPageNum(1);
    //       //获取处理中列表
    //       queryRobotTicketList(1, false); //处理中
    //     }
    //   }
    // }, [
    //   // search,
    //   channelType,
    //   orderType,
    //   workRecordTypeCode,
    //   startTime,
    //   endTime,
    //   workTableTabValue,
    // ]);
    /**
     * 监听工单接口返回的用户列表，重组更新到父组件
     */
    useEffect(() => {
      setGlobalList(commentList);
      //查询处理中、未分配数量
      queryWorkBenchTicketTotalCount();
    }, [commentList]);
    /**
     * 处理转派或者解决后列表页数量未计算问题
     */
    useEffect(() => {
      console.log('globalList发生变化', globalList);
      //查询处理中、未分配数量
      queryWorkBenchTicketTotalCount();
    }, [globalList.length]);
    // 查询处理中、未分配数量
    const queryWorkBenchTicketTotalCount = () => {
      dispatch({
        type: 'worktable/queryWorkBenchTicketTotalCount',
        callback: response => {
          let { code, msg, data } = response;
          if (code === 200) {
            // setPendTotal(data.pendingCount);
            setPendTotalTab(data.pendingCount);
            setProcessTotalTab(data.processingCount);
            // setTotal(data.processingCount);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    };
    // 查询机器人数量
    const queryRobotTicketTotalCount = () => {
      dispatch({
        type: 'worktable/queryRobotTicketTotalCount',
        callback: response => {
          let { code, msg, data } = response;
          if (code === 200) {
            setRobotTotalTab(data);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    };
    //自动领取
    const options = [
      {
        label: (
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span className={styles.statusBox} style={{ color: '#fff' }}>
              <FormattedMessage
                id="work.order.ticket.02.1"
                defaultValue="处理中"
              />
            </span>
            <span
              style={{
                borderRadius: 4,
                background:
                  'linear-gradient(0deg, rgba(0, 185, 0, 0.10) 0%, rgba(0, 185, 0, 0.10) 100%), #FFF',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: 5,
                color: '#00B900',
                fontSize: 12,
                padding: 4,
              }}
            >
              {/* {total} */}
              {processTotalTab}
            </span>
          </span>
        ),
        value: '1',
      },
    ];
    //手动领取
    const newOption = [
      {
        label: (
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span
              className={styles.statusBox}
              style={{
                color:
                  checkValue == '1'
                    ? '#fff'
                    : checkValue == '2'
                    ? '#3463FC'
                    : '',
              }}
            >
              <FormattedMessage
                id="work.order.ticket.02.1"
                defaultValue="处理中"
              />
            </span>
            <span
              style={{
                borderRadius: 4,
                background:
                  'linear-gradient(0deg, rgba(0, 185, 0, 0.10) 0%, rgba(0, 185, 0, 0.10) 100%), #FFF',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: 5,
                color: '#00B900',
                fontSize: 12,
                padding: 4,
              }}
            >
              {/* {total} */}
              {processTotalTab}
            </span>
          </span>
        ),
        value: '1',
      },
      {
        label: (
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span
              className={styles.statusBox}
              style={{
                color:
                  checkValue === '2'
                    ? '#fff'
                    : checkValue === '1'
                    ? '#3463FC'
                    : '',
              }}
            >
              <FormattedMessage
                id="work.order.management.status.undistributed"
                defaultValue="未分配"
              />
            </span>
            <span
              style={{
                borderRadius: 4,
                background:
                  'linear-gradient(0deg, rgba(242, 36, 23, 0.10) 0%, rgba(242, 36, 23, 0.10) 100%), #FFF',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: 5,
                color: '#F22417',
                fontSize: 12,
                padding: 4,
              }}
            >
              {/* {pendTotal} */}
              {pendTotalTab}
            </span>
          </span>
        ),
        value: '2',
      },
    ];
    //坐席管理员视角
    const adminOption = [
      {
        label: (
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span
              className={styles.statusBox}
              style={{
                color: checkValue == '1' ? '#fff' : '#3463FC',
                whiteSpace: 'nowrap',
              }}
            >
              <FormattedMessage
                id="work.order.ticket.02.1"
                defaultValue="处理中"
              />
            </span>
            <span
              style={{
                borderRadius: 4,
                background:
                  'linear-gradient(0deg, rgba(0, 185, 0, 0.10) 0%, rgba(0, 185, 0, 0.10) 100%), #FFF',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: 5,
                color: '#00B900',
                fontSize: 12,
                padding: 4,
              }}
            >
              {/* {total} */}
              {processTotalTab}
            </span>
          </span>
        ),
        value: '1',
      },
      {
        label: (
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span
              className={styles.statusBox}
              style={{
                color: checkValue === '2' ? '#fff' : '#3463FC',
                whiteSpace: 'nowrap',
              }}
            >
              <FormattedMessage
                id="work.order.management.status.queue"
                defaultValue="排队中"
              />
            </span>
            <span
              style={{
                borderRadius: 4,
                background:
                  'linear-gradient(0deg, rgba(242, 36, 23, 0.10) 0%, rgba(242, 36, 23, 0.10) 100%), #FFF',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: 5,
                color: '#F22417',
                fontSize: 12,
                padding: 4,
              }}
            >
              {/* {pendTotal} */}
              {pendTotalTab}
            </span>
          </span>
        ),
        value: '2',
      },
      {
        label: (
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span
              className={styles.statusBox}
              style={{
                color: checkValue == '3' ? '#fff' : '#3463FC',
                whiteSpace: 'nowrap',
              }}
            >
              <FormattedMessage
                id="work.order.management.status.robot"
                defaultValue="机器人"
              />
            </span>
            <span
              style={{
                borderRadius: 4,
                background:
                  'linear-gradient(0deg, rgba(0, 185, 0, 0.10) 0%, rgba(0, 185, 0, 0.10) 100%), #FFF',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: 5,
                color: '#00B900',
                fontSize: 12,
                padding: 4,
              }}
            >
              {/* {total} */}
              {robotTotalTab}
            </span>
          </span>
        ),
        value: '3',
      },
    ];

    const dropdownItems = [
      {
        key: 2,
        label: <FormattedMessage id="work.order.ticket.sort.1" />,
      },
      {
        key: 1,
        label: <FormattedMessage id="work.order.ticket.sort.2" />,
      },
      {
        key: 4,
        label: <FormattedMessage id="work.order.ticket.sort.3" />,
      },
      {
        key: 3,
        label: <FormattedMessage id="work.order.ticket.sort.4" />,
      },
    ];
    const dropdownOptionItems = [
      {
        key: 1,
        label: (
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 5, marginTop: 5 }}>{BatchReply()}</span>
            <FormattedMessage id="work.order.ticket.options.1" />
          </span>
        ),
      },
      // {
      //   key: 2,
      //   label: (
      //     <span style={{ display: 'flex', alignItems: 'center' }}>
      //       <span style={{ marginRight: 5, marginTop: 5 }}>{BatchTag()}</span>
      //       <FormattedMessage id="work.order.ticket.options.2" />
      //     </span>
      //   ),
      // },
      {
        key: 3,
        label: (
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 5, marginTop: 5 }}>{PinMessage()}</span>
            <FormattedMessage id="work.order.ticket.options.3" />
          </span>
        ),
      },
      {
        key: 4,
        label: (
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 5, marginTop: 5 }}>
              {PinMessageNo()}
            </span>
            <FormattedMessage id="work.order.ticket.options.4" />
          </span>
        ),
      },
    ];
    useEffect(() => {
      setIndeterminate(
        checkedList.length > 0 && checkedList.length < commentList.length,
      );
      setCheckAll(checkedList.length === commentList.length);
    }, [commentList.length, checkedList]);
    // 选中单个
    const onChangeGroup = list => {
      let checkList = list;
      setCheckedList(checkList);
    };

    /**=========================工单领取逻辑============================== */
    //分配中数据传给父组件
    useEffect(() => {
      setWaitPendingList(pendingList);
      //领取状态默认false
      dispatch({
        type: 'worktable/saveWorkTableCollectTicket',
        payload: false,
      });
    }, [pendingList]);
    //机器人数据传给父组件
    useEffect(() => {
      setRobotListParent(robotList);
    }, [robotList]);
    // 未分配下全选逻辑
    useEffect(() => {
      setIndeterminatePending(
        checkedPendingList.length > 0 &&
          checkedPendingList.length < pendingList.length,
      );
      setCheckAllPending(
        checkedPendingList.length === pendingList.length && pendingList.length,
      );
    }, [pendingList.length, checkedPendingList]);

    // 未分配全选中
    const onCheckAllChange = e => {
      if (+checkValue === 2) {
        const ids = pendingList.map(item => item.workRecordId);
        setCheckedPendingList(e.target.checked ? ids : []);
      }
    };
    // 处理中全选中
    const onCheckAllChangeProcess = e => {
      const ids = commentList.map(item => item.workRecordId);
      setCheckedList(e.target.checked ? ids : []);
    };
    const onChangeGroupPending = list => {
      let checkList = list;
      setCheckedPendingList(checkList);
    };
    // 处理中 点击每个工单
    const handleCurrentSessionGlobal = item => {
      handleSessionItemClick(item);
    };
    // 未分配下 点击每个工单
    const handleCurrentSessionPending = item => {
      handleSessionItemPendingClick(item);
      setSelectedTicketIds(prevSelected => [...prevSelected, item.ticketId]);
    };
    // 机器人下 点击每个工单
    const handleCurrentSessionRobot = item => {
      handleSessionItemRobotClick(item);
    };
    const handleCheckboxChange = e => {
      console.log(e, 'e');
      e.stopPropagation();
      // event.nativeEvent.stopImmediatePropagation();
      // if (e.target.checked) {
      //   setCheckedPendingList([...checkedPendingList, ...e]);
      // } else {
      //   setCheckedPendingList(
      //     checkedPendingList.filter(id => id !== e)
      //   );
      // }
      console.log(checkedPendingList, 'checkedPendingList');
      // setCheckedPendingList(e.target.checked ? ids : []);
      // setCheckedPendingList(checkedValues);
    };
    // 切换处理中、分配中
    const onChangeCheck = e => {
      setCheckValue(e.target.value);
      dispatch({
        type: 'worktable/saveWorkTableTabProcesOrPendValue',
        payload: e.target.value,
      });
      if (+e.target.value === 1) {
        queryProcessingWorkOrderList(1, false);
        //查询处理中、未分配数量
        queryWorkBenchTicketTotalCount();
      } else if (+e.target.value === 3) {
        queryRobotTicketList(1, false);
        queryRobotTicketTotalCount();
      } else {
        queryPendingWorkOrderList(1, false);
        //查询处理中、未分配数量
        queryWorkBenchTicketTotalCount();
      }

      setCheckAllPending(false);
      setCheckedPendingList([]);
      setIndeterminatePending(false);
      setCheckAll(false);
      setCheckedList([]);
      setIndeterminate(false);

      setChannelType('');
      setOrderType(1);
      setWorkRecordTypeCode('');
      setCustomerTagIds([]);
      setTicketTagIds([]);
      setStartTime('');
      setEndTime('');
      setSearch('');
      setRangeValue(null);
    };

    // 领取工单
    const handleCollectTicket = async () => {
      if (checkValue == '2' && ticketType === 2) {
        console.log(checkedPendingList, 'checkedPendingList');
        if (checkedPendingList && checkedPendingList.length > 0) {
          setLoading(true);
          const result = await IMWebClient.collectTicket({
            sessionIds: checkedPendingList.join(','),
            companyId: user?.companyId,
            agentId: user?.userId,
          });
          if (result.code == 200) {
            setLoading(false);
            // 领取成功，tab切换处理中且刷新接口
            setCheckValue('1');
            //查询处理中、未分配数量
            queryWorkBenchTicketTotalCount();
            queryProcessingWorkOrderList(1, false);
            //全局tab值更新
            dispatch({
              type: 'worktable/saveWorkTableTabProcesOrPendValue',
              payload: '1',
            });
            dispatch({
              type: 'worktable/saveWorkTableCollectTicket',
              payload: true,
            });
            console.log('---------result.data-----------', result.data);
            if (result.data) {
              let totalNum = result.data.false.length + result.data.true.length;
              if (result.data.true && result.data.true.length > 0) {
                notification.success({
                  message: getIntl().formatMessage(
                    { id: 'new.worktable.ticket.collected.success1' },
                    { idValueSucess: result.data.true.join(',') },
                  ),
                });
              }
              if (result.data.false && result.data.false.length > 0) {
                notification.warning({
                  message: getIntl().formatMessage(
                    { id: 'new.worktable.ticket.collected.error1' },
                    {
                      totalNum: totalNum,
                      idValueError: result.data.false.length,
                    },
                  ),
                });
              }
            }
          } else {
            notification.error({
              message: getIntl().formatMessage({
                id: 'new.worktable.ticket.collected.error',
                default: result.message,
              }),
            });
            dispatch({
              type: 'worktable/saveWorkTableCollectTicket',
              payload: false,
            });
          }
        } else {
          setLoading(false);
          notification.error({
            message: getIntl().formatMessage({
              id: 'new.worktable.ticket.collected',
              default: '请选中要领取的工单',
            }),
          });
        }
        setCheckedPendingList([]);
      }
    };
    //分配工单
    const allocateTicket = async () => {
      if (checkedPendingList && checkedPendingList.length > 0) {
        setAllocateModal(true);
        formWorkOrderUpgradeRef.current?.resetFields();
        setRuleTypeValue(0);
        await querySeatsUserList();
        await queryCallDeptList();
      } else {
        notification.error({
          message: getIntl().formatMessage({
            id: 'new.worktable.ticket.assign',
            default: '请选中要分配的工单',
          }),
        });
      }
    };
    const onFinishWorkOrderUpgrade = async values => {
      setLoadingModal(true);
      let params = {};
      if (values.ruleType === 1) {
        params = {
          workRecordId: checkedPendingList.join(','),
          priorityLevelId: '',
          agentId: values.agentId,
          operationLogReason: values.operationLogReason,
          ruleType: values.ruleType,
          position: 1, //1、工作台 2、详情
          assignType: 2,
        };
      } else {
        params = {
          workRecordId: checkedPendingList.join(','),
          priorityLevelId: '',
          teamId: values.teamId,
          operationLogReason: values.operationLogReason,
          ruleType: values.ruleType,
          position: 1, //1、工作台 2、详情
          assignType: 2,
        };
      }
      dispatch({
        type: 'worktable/queryAllocationTicket',
        payload: params,
        callback: response => {
          setLoadingModal(false);
          if (response.code === 200) {
            //查询处理中、未分配数量
            setAllocateModal(false);
            setCheckedPendingList([]);
            queryWorkBenchTicketTotalCount();
            queryPendingWorkOrderList(1, false);
            //排队中、待分配;分配完工单清空会话窗口

            handleSessionItemClick();
            if (values.ruleType === 1) {
              IMWebClient.assign({
                tenantId: user.companyId, // 公司id
                sessionId: checkedPendingList.join(','), // 工单会话id
                uid: user.userId, // 用户id
                agentId: values.agentId, // 要转派的座席id，teamId/agentId二选一填写
              });
            } else {
              IMWebClient.assign({
                tenantId: user.companyId, // 公司id
                sessionId: checkedPendingList.join(','), // 工单会话id
                uid: user.userId, // 用户id
                teamId: values.teamId, // 要转派的团队，teamId/agentId二选一填写
              });
            }
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    // 切换指定团队\座席
    const onChangeRuleType = e => {
      setRuleTypeValue(e.target.value);
    };
    // 查询客服列表
    const querySeatsUserList = async () => {
      dispatch({
        type: 'workOrderCenter/querySeatsUserList',
        callback: response => {
          if (response.code == 200) {
            // const newData = response.data.filter(
            //   item => item.userId !== detailData.agentId,
            // );
            setSeatsUserList(response.data);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    // 查询座席团队列表
    const queryCallDeptList = async () => {
      setAllocateLoading(true);
      dispatch({
        type: 'workOrderCenter/queryCallDeptList',
        callback: response => {
          setAllocateLoading(false);

          if (response.code == 200) {
            setCallDeptList(response.data);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    /**
     * 切换工单领取：手动or自动
     */
    const handleTicketCollect = async data => {
      setTicketType(data);
      // 切换时默认处理中更新到全局
      dispatch({
        type: 'worktable/saveWorkTableTabProcesOrPendValue',
        payload: '1',
      });
      setCheckValue('1'); // 默认处理中
      queryProcessingWorkOrderList(1, false);
      setOpen(false);
      const result = await IMWebClient.maualOrautoTicket({
        receiveType: data,
        companyId: user?.companyId,
        agentId: user?.userId,
      });
      if (result.code !== 200) {
        notification.error(result.message);
      }
    };

    // 未分配列表接口
    const queryPendingWorkOrderList = (num, flag) => {
      setLoading(true);
      dispatch({
        type: 'worktable/queryPendingWorkOrderList',
        payload: {
          data: {
            channelType: channelType,
            search: search,
            orderType: orderType,
            workRecordTypeCode: workRecordTypeCode,
            startTime: startTime,
            endTime: endTime,
            customerTagIds:
              customerTagIds.length > 0 ? customerTagIds.join(',') : '',
            ticketTagIds: ticketTagIds.length > 0 ? ticketTagIds.join(',') : '',
          },
          page: {
            pageSize: 20,
            pageNum: num,
          },
        },
        callback: response => {
          setLoading(false);
          if (response.code == 200) {
            let total = response.data.total;
            let list = [];
            //有筛选条件下获取新列表，下滑获取拼接后的列表
            if (flag) {
              list = [...pendingList, ...response.data.rows];
            } else {
              list = [...response.data.rows];
            }
            setPendList(list);
            setPendTotal(total);
            setPageNum(num);
            if (Number(total) > list?.length) {
              setHasMore(true);
            } else {
              setHasMore(false);
            }
          }
        },
      });
    };
    // 机器人列表接口

    const queryRobotTicketList = (num, flag) => {
      setLoading(true);
      dispatch({
        type: 'worktable/queryRobotTicketList',
        payload: {
          data: {
            channelType: channelType,
            search: search,
            orderType: orderType,
            workRecordTypeCode: workRecordTypeCode,
            startTime: startTime,
            endTime: endTime,
            customerTagIds:
              customerTagIds.length > 0 ? customerTagIds.join(',') : '',
            ticketTagIds: ticketTagIds.length > 0 ? ticketTagIds.join(',') : '',
          },
          page: {
            pageSize: 20,
            pageNum: num,
          },
        },
        callback: response => {
          setLoading(false);
          if (response.code == 200) {
            let total = response.data.total;
            let list = [];
            //有筛选条件下获取新列表，下滑获取拼接后的列表
            if (flag) {
              list = [...robotList, ...response.data.rows];
            } else {
              list = [...response.data.rows];
            }
            setRobotList(list);
            setRobotTotal(total);
            setPageNum(num);
            if (Number(total) > list?.length) {
              setHasMore(true);
            } else {
              setHasMore(false);
            }
          }
        },
      });
    };
    // 未分配上滑加载
    const fetchMoreDataPending = () => {
      let result = Number(pendTotal) > pendingList?.length;
      let newPageNum = Number(pageNum) + 1;
      if (result) {
        setPageNum(newPageNum);
        queryPendingWorkOrderList(newPageNum, true);
      } else {
        setHasMore(false);
      }
    };
    // 机器人上滑加载
    const fetchMoreDataRobot = () => {
      let result = Number(robotTotal) > robotList?.length;
      let newPageNum = Number(pageNum) + 1;
      if (result) {
        setPageNum(newPageNum);
        queryRobotTicketList(newPageNum, true);
      } else {
        setHasMore(false);
      }
    };

    // 查询邮件工单总数
    const queryEmailTotalCount = () => {
      dispatch({
        type: 'worktable/queryEmailTotalCount',
        callback: response => {
          if (response.code == 200) {
            setEmailTicketNum(response.data);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };

    const handleLeftResize = event => {
      let menuWidth =
        sessionStorage.getItem('menu_collapsed_state') === 'true' ? 100 : 228;
      const newWidth = event.clientX - menuWidth; // 获取鼠标位置需要减去左侧菜单的宽度
      setLeftWidth(newWidth > 405 ? 405 : newWidth);
      onLeftWidth(newWidth > 405 ? 405 : newWidth);
    };
    // 切换tab
    const onChangeTabs = e => {
      console.log('setCheckValue', e, checkValue);
      setWorkTableTabValue(e);
      dispatch({
        type: 'worktable/saveWorkTableTabValue',
        payload: e,
      }); // tab值存入全局
      if (+e === 2) {
        setCheckValue('1');
        //这里不需要重复调用，去effect调
        // queryProcessingWorkOrderList(1, false);
      } else if (+e === 1) {
        //把电话侧的工单id存在该组件中，切换tabs传递给父组件不同tickedId
        getPhoneInfo(phoneCurrentInfo);
      }
      setSelectEmailTicketData({});
      dispatch({
        type: 'worktable/saveEmailSelectTicketData',
        payload: {},
      });
      handleChangeTab();
      setPageNum(1);
      setChannelType('');
      setOrderType(1);
      setWorkRecordTypeCode('');
      setCustomerTagIds([]);
      setTicketTagIds([]);
      setStartTime('');
      setEndTime('');
      setSearch('');
      setRangeValue(null);
    };
    /**
     *筛选 展示隐藏
     */
    const showForm = () => {
      let a = !formHide;
      setFormHide(a);
    };
    // const handleCurrentSession = item => {
    //   console.log(item, '列表为空===当前会话消息');
    //   handleSessionItemClick(item);
    // };
    /**
     * 查询工单类型列表
     * */
    const queryWorkRecordType = () => {
      dispatch({
        type: 'workOrderCenter/queryWorkRecordType',
        callback: response => {
          if (response.code == 200) {
            let workRecordTypeList = response.data;
            setWorkRecordTypeList(workRecordTypeList);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    /**
     * search搜索
     */
    const searchChange = e => {
      setSearch(e.target.value);
    };
    const searchPressEnter = () => {
      if (workTableTabValue === 3) {
        // setPageNum(1);
        // queryEmailWorkOrderList(1,false);
        setEmailSearch(!emailSearch);
      } else if (workTableTabValue === 2) {
        setChatSearch(!chatSearch);
      }
    };
    /**
     * 筛选客户标签
     */
    const handleAddSelectChange = e => {};
    /**
     * 筛选工单类型
     */

    const handleChangeSelectWorker = e => {
      setWorkRecordTypeCode(e, false);
    };
    /**
     * 筛选时间
     */
    const onRangePickerChange = (e, data) => {
      setRangeValue(e);
      if (data && data.length > 1) {
        setStartTime(data[0]);
        setEndTime(data[1]);
      }
    };
    /**
     * 筛选渠道类型
     */
    const handleChangeSelectChannel = e => {
      setChannelType(e);
    };
    const onDropdown = ({ key }) => {
      setOrderType(key);
    };
    const onDropdownOption = ({ key }) => {
      console.log(key, 'key');
      if (checkedList && checkedList.length > 0) {
        if (+key === 1) {
          //根据选中的工单id拿到完整的数据
          const checkedItemsMap = new Map();
          checkedList.forEach(item => {
            checkedItemsMap.set(item, true);
          });

          const filteredGlobalList = globalList?.filter(item =>
            checkedItemsMap.has(item.ticketId),
          );
          console.log('匹配的对象:', filteredGlobalList);
          const failureInfoList = []; //失败列表

          filteredGlobalList?.forEach(item => {
            if (+item?.ticketStatus === 2 || +item?.ticketStatus === 3) {
              failureInfoList.push(item?.username);
            }
          });
          if (failureInfoList.length > 0) {
            notification.error({
              message: getIntl().formatMessage(
                {
                  id: 'new.worktable.chatList.batch.reply.error',
                },
                { list: failureInfoList.join(',') },
              ),
            });
            return;
          } else {
            setIsBathReplyModal(true);
          }
        } else if (+key === 3) {
          postIsTop();
        } else if (+key === 4) {
          postIsTopNo();
        }
      } else {
        notification.error({
          message: getIntl().formatMessage({
            id: 'new.worktable.ticket.batch.reply',
            default: '请选中需要批量回复的工单',
          }),
        });
      }
    };
    /**
     * 置顶会话
     */
    const postIsTop = () => {
      dispatch({
        type: 'worktable/postIsTop',
        payload: {
          wordRecordId: checkedList.join(','),
          isTop: true,
        },
        callback: response => {
          if (response.code === 200) {
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.operation.success',
              }),
            });
            handleSetTopTicket(checkedList);
            setCheckedList([]);
            setIndeterminate(false);
            setCheckAll(false);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    const postIsTopNo = () => {
      dispatch({
        type: 'worktable/postIsTop',
        payload: {
          wordRecordId: checkedList.join(','),
          isTop: false,
        },
        callback: response => {
          if (response.code === 200) {
            handleSetNoTopTicket(checkedList);
            setCheckedList([]);
            setIndeterminate(false);
            setCheckAll(false);
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.operation.success',
              }),
            });
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    useEffect(() => {
      if (isBathReplyModal) {
        const textArea = document.querySelector(`.${styles.chatTextarea}`);
        if (textArea) {
          textArea.focus();
        }
      }
    }, [isBathReplyModal]);
    /**
   * 处理中的会话列表
   * channelType：渠道类型代号，从下拉列表那个code中选择
      search：搜索关键字
      orderType ：排序类型：1-按消息到达时间倒序 2-按消息到达时间正序 3-按回复消息时间倒序 4-按回复消息时间正序 （默认是1）
   */
    const queryProcessingWorkOrderList = (num, flag) => {
      setLoading(true);
      dispatch({
        type: 'worktable/queryProcessingWorkOrderList',
        payload: {
          data: {
            channelType: channelType,
            search: search,
            orderType: orderType,
            workRecordTypeCode: workRecordTypeCode,
            startTime: startTime,
            endTime: endTime,
            customerTagIds:
              customerTagIds.length > 0 ? customerTagIds.join(',') : '',
            ticketTagIds: ticketTagIds.length > 0 ? ticketTagIds.join(',') : '',
          },
          page: {
            pageSize: 20,
            pageNum: num,
          },
        },
        callback: response => {
          setLoading(false);
          if (response.code == 200) {
            let total = response.data.total;
            let list = [];
            //有筛选条件下获取新列表，下滑获取拼接后的列表
            if (flag) {
              list = [...commentList, ...response.data.rows];
            } else {
              list = [...response.data.rows];
            }
            //这里需要循环list，给每个item加入参数用于倒计时
            /**queryProcessingWorkOrDerListSchedule返回内容
             * workRecordId 工单id
             *  timeStatus 当前时间状态 是否超时  1 - 超出，2 - 剩余
             * scheduleTime  时间
             * timeType  时间类型   1 - 响应  2-解决
             */
            queryProcessingWorkOrDerListSchedule(list, {
              workOrderId: '',
              workRecordTypeCode: '',
              priorityLevelId: '',
              customerTagIds: '',
              ticketTagIds: '',
            });
            console.log(list, 'globalList=====333');
            setCommentList(list);

            setTotal(total);
            setPageNum(num);
            if (Number(total) > list?.length) {
              setHasMore(true);
            } else {
              setHasMore(false);
            }
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    //     入参:
    // "workOrderId"工单id
    // "workRecordTypeCode":修改的工单类型
    // "priorityLevelId":"修改的优先级id
    const queryProcessingWorkOrDerListSchedule = (list, payload) => {
      setLoading(true);
      dispatch({
        type: 'worktable/queryProcessingWorkOrDerListSchedule',
        payload: payload,
        callback: response => {
          setLoading(false);
          if (response.code == 200) {
            let scheduleList = response.data;
            let newList = list?.map(item => {
              scheduleList.forEach(schedule => {
                if (schedule.workRecordId === item.workRecordId) {
                  item.timeStatus = schedule?.timeStatus;
                  item.scheduleTime = schedule?.scheduleTime;
                  item.timeType = schedule?.timeType;
                }
              });
              return item;
            });
            console.log(newList, 'globalList=====444');
            setCommentList(newList);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    /**
     *  上滑加载更多
     */
    const fetchMoreData = () => {
      let result = Number(total) > commentList?.length;
      let newPageNum = Number(pageNum) + 1;
      if (result) {
        setPageNum(newPageNum);
        queryProcessingWorkOrderList(newPageNum, true);
      } else {
        setHasMore(false);
      }
    };
    /**
     * 判断数据是不是JSON
     */
    const isValidJson = (content, item) => {
      if (typeof content === 'string') {
        try {
          // 检查是否为数字字符串
          if (!isNaN(content) && content.trim() !== '') {
            return false;
          }
          JSON.parse(content);
          return true; // 解析成功，说明是有效 JSON
        } catch (e) {
          return false; // 解析失败，说明不是有效 JSON
        }
      }
      return false; // 不是字符串，返回 false
    };
    /**
     * 打开工单选择弹框
     */
    const handleOpenChange = newOpen => {
      setOpen(newOpen);
    };

    useEffect(() => {
      let timer = null;
      if (workTableTabValue === 3) {
        setSelectEmailTicketData({});
        dispatch({
          type: 'worktable/saveEmailSelectTicketData',
          payload: {},
        });
        queryEmailWorkOrderList(1, false);
        timer = setInterval(() => {
          setPageNum(1);
          queryEmailWorkOrderList(1, false);
        }, 1000 * 60 * 2);
      } else if (workTableTabValue === 2) {
        if (+checkValue === 1) {
          setPageNum(1);
          //获取处理中列表
          queryProcessingWorkOrderList(1, false); //处理中
        } else if (+checkValue === 2) {
          setPageNum(1);
          //获取处理中列表
          queryPendingWorkOrderList(1, false); //处理中
        } else if (+checkValue === 3) {
          setPageNum(1);
          //获取处理中列表
          queryRobotTicketList(1, false); //处理中
        }
      }
      return () => clearInterval(timer);
    }, [
      channelType,
      orderType,
      workRecordTypeCode,
      startTime,
      endTime,
      workTableTabValue,
      emailSearch,
      chatSearch,
      ticketTagIds,
      customerTagIds,
    ]);
    useEffect(() => {
      if (workTableTabValue === 3 && refreshLeftEmailList) {
        queryEmailWorkOrderList(1, false);
        queryEmailTotalCount();
        dispatch({
          type: 'worktable/saveEmailSelectTicketData',
          payload: {},
        });
      }
      // 刷新chat聊天数量
      if (+workTableTabValue === 2 && refreshLeftEmailList) {
        //查询处理中、未分配数量
        if (+workTableTabProcesOrPendValue === 1) {
          queryProcessingWorkOrderList(1, false);
          queryWorkBenchTicketTotalCount();
        } else if (+workTableTabProcesOrPendValue === 3) {
          queryRobotTicketList(1, false);
          queryRobotTicketTotalCount();
        } else {
          queryPendingWorkOrderList(1, false);
          queryWorkBenchTicketTotalCount();
        }
      }
    }, [refreshLeftEmailList]);

    /**
     *  邮件上滑加载更多
     */
    const fetchMoreData1 = () => {
      let result = Number(total) > commentList?.length;
      let newPageNum = Number(pageNum) + 1;
      if (result) {
        setPageNum(newPageNum);
        queryEmailWorkOrderList(newPageNum, true);
      } else {
        setHasMore(false);
      }
    };

    // 查询邮件类型工单列表
    const queryEmailWorkOrderList = (num, flag) => {
      setLoading(true);
      let params = {
        data: {
          channelType: 1,
          search: search,
          orderType: orderType,
          workRecordTypeCode: workRecordTypeCode,
          startTime: startTime,
          endTime: endTime,
          customerTagIds:
            customerTagIds.length > 0 ? customerTagIds.join(',') : '',
          ticketTagIds: ticketTagIds.length > 0 ? ticketTagIds.join(',') : '',
        },
        pageNum: num,
        pageSize: 20,
      };
      dispatch({
        type: 'worktable/queryEmailWorkOrderList',
        payload: params,
        callback: response => {
          setLoading(false);
          if (response.code === 200) {
            dispatch({
              type: 'worktable/setRefreshLeftEmailList',
              payload: false,
            });
            let total = response.data.total;
            let list = [];
            //有筛选条件下获取新列表，下滑获取拼接后的列表
            if (flag) {
              list = [...emailTicketList, ...response.data.rows];
            } else {
              list = [...response.data.rows];
            }
            setEmailTicketList(list);
            setTotal(total);
            setPageNum(num);
            if (Number(total) > list?.length) {
              setHasMore(true);
            } else {
              setHasMore(false);
            }
          } else {
            notification.error({
              message: response.msg,
            });
          }
          // if (response.code == 200) {
          //   setTotal(response.data.total);
          //   setEmailTicketList(response.data.rows);
          // }
        },
      });
    };

    // 邮件选中左侧列表会话
    const handleCurrentSessionEmail = item => {
      setSelectEmailTicketData(item);
      dispatch({
        type: 'worktable/saveEmailSelectTicketData',
        payload: item,
      });
      handleSessionItemClick(item);
      if (item?.unreadCount > 0) {
        updateReadStatus(item);
      }
    };
    // 未读变已读
    const updateReadStatus = (item, type) => {
      let params = {
        workOrderId: item.workRecordId,
      };
      dispatch({
        type: 'worktable/updateReadStatus',
        payload: params,
        callback: response => {
          if (response.code === 200) {
            setTimeout(() => {
              queryEmailWorkOrderList(1, false);
            }, 500);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };
    //拿到电话tab的工单id
    const getPhoneCurrentInfo = e => {
      setPhoneCurrentInfo(e);
      getPhoneInfo(e);
    };
    const handleCallOut = e => {
      onChangeTabs(1);
      callComponentRef.current?.componentCallOut(e);
    };
    const handleChangeChat = () => {
      onChangeTabs(2);
    };
    //处理中列表对话+1
    const setToTalNum = e => {
      setTotal(pre => {
        return Number(pre) + 1;
      });
    };
    useImperativeHandle(ref, () => ({
      handleCallOut,
      setToTalNum,
      handleChangeChat,
    }));

    // 添加引用
    const emojiTriggerRef = useRef(null);
    const emojiPickerRef = useRef(null);

    // 在组件中添加effect
    useEffect(() => {
      // 点击外部关闭表情选择器
      const handleOutsideClick = e => {
        if (
          showEmoji &&
          emojiTriggerRef.current &&
          !emojiTriggerRef.current.contains(e.target) &&
          emojiPickerRef.current &&
          !emojiPickerRef.current.contains(e.target)
        ) {
          setShowEmoji(false);
        }
      };

      document.addEventListener('mousedown', handleOutsideClick);
      return () => {
        document.removeEventListener('mousedown', handleOutsideClick);
      };
    }, [showEmoji]);

    // 在相关位置添加处理函数
    const handleSendMessage = async () => {
      if (!inputMessage.trim()) return;

      // 这里添加发送消息的逻辑，可以调用已有的接口
      console.log('发送消息:', inputMessage, checkedList, globalList);
      //根据选中的工单id拿到完整的数据
      const checkedItemsMap = new Map();
      checkedList.forEach(item => {
        checkedItemsMap.set(item, true);
      });

      const filteredGlobalList = globalList.filter(item =>
        checkedItemsMap.has(item.ticketId),
      );
      console.log('匹配的对象:', filteredGlobalList);
      const ticketInfoList = []; //成功列表

      filteredGlobalList.forEach(item => {
        ticketInfoList.push({
          ticketId: item?.ticketId,
          channelId: item?.channelId,
          customPhone: '',
          translateContent: '',
          translateLanguage: item?.translateCode,
          translationStatus: item?.translateStatus,
          customerSourceLanguageCode: item?.customerSourceLanguageCode,
          contactId: item?.contactId,
        });
      });
      let payload = {
        ticketInfoList: ticketInfoList,
        userId: user.userId, // 当前坐席id
        replyType: user.roleList[0].roleId === '1005' ? '8' : '1', // 回复类型
        chatUserName: user.userName, // 当前坐席name
        tenantId: user.companyId, // 公司id
        sessionId: '', // 暂不传
        role: user.roleList[0].roleId === '1005' ? 'agent_admin' : 'agent', // 当前坐席角色  agent、agent_admin
        contentType: 'text', // 内容类型
        content: inputMessage, // 内容
        contentId: '', // 暂不传
        attachmentName: '', // 暂不传
        displayName: '', // 暂不传
      };
      //前端循环去加数据
      forHandleSendMessage(payload);
    };
    const forHandleSendMessage = async data => {
      setLoadingModal(true);
      // 调用批量发送接口
      const result = await IMWebClient.batchSend(data);

      if (result.code === 200) {
        setInputMessage('');
        setLoadingModal(false);
        setIsBathReplyModal(false);
        setCheckedList([]);
        setIndeterminate(false);
        setCheckAll(false);
        data?.ticketInfoList?.forEach(async item => {
          let messageData = {
            ticketId: item.ticketId,
            content: data.content,
            contactId: item.contactId, //联系id
            contentType: 'text',
            role: data.role,
            type: 'role',
            username: data.chatUserName,
            uid: data.userId,
            id: uuidv4(),
            uploadStatus: 1,
            translationContent: '', // 翻译的内容
            translationStatus:
              item.translateStatus && item.customerSourceLanguageCode ? 1 : 0, // 0不翻译，1翻译中，2翻译成功，3翻译失败
          };
          // 添加消息数据到到消息列表中
          const messageId = await handleAddMessageNew(messageData, false);
          // 判断是否开启了实时翻译功能，是否存在存在客服源语言code
          if (item.translateStatus && item.customerSourceLanguageCode) {
            let translateResponse = await handleGetTranslateText(
              item.ticketId,
              messageId,
              data.content,
              item.customerSourceLanguageCode,
              'agent',
            );
            if (translateResponse.code === 200) {
            }
          }
        });
        notification.success({
          message: result.message,
        });
      } else {
        setLoadingModal(false);
        notification.error({
          message: result.message,
        });
      }
    };
    // 选中后tag标签颜色
    const tagRender = props => {
      const { label, value, closable, onClose } = props;
      let resultObj = findTagById(value);
      const onPreventMouseDown = event => {
        event.preventDefault();
        event.stopPropagation();
      };
      if (resultObj) {
        return (
          <Tag
            className={resultObj.tagColorCode || 'colorType1'}
            onMouseDown={onPreventMouseDown}
            closable={closable}
            onClose={onClose}
            style={{
              marginRight: 3,
            }}
          >
            {resultObj.tagContent}
          </Tag>
        );
      }
    };
    const ticketTagRender = props => {
      const { label, value, closable, onClose } = props;
      let resultObj = findTicketTagById(value);
      const onPreventMouseDown = event => {
        event.preventDefault();
        event.stopPropagation();
      };
      if (resultObj) {
        return (
          <Tag
            className={resultObj.tagColorCode || 'colorType1'}
            onMouseDown={onPreventMouseDown}
            closable={closable}
            onClose={onClose}
            style={{
              marginRight: 3,
            }}
          >
            {resultObj.tagContent}
          </Tag>
        );
      }
    };
    const findTagById = tagId => {
      for (let category of standardTagListRender) {
        for (let tag of category.tagList) {
          if (tag.tagId == tagId) {
            return tag;
          }
        }
      }
      return null; // 如果没有找到匹配的标签，返回 null
    };
    const findTicketTagById = tagId => {
      for (let category of ticketTagListRender) {
        for (let tag of category.tagList) {
          if (tag.tagId == tagId) {
            return tag;
          }
        }
      }
      return null; // 如果没有找到匹配的标签，返回 null
    };
    //查询工单标签

    const queryTicketTagSelectList = () => {
      dispatch({
        type: 'tagManagement/queryTicketTagSelectList',
        payload: { ticketId: '' },
        callback: response => {
          let { code, data, msg } = response;
          if (code === 200) {
            setTicketTagListRender(data);
            setTicketTagList(data);
            setOriginTicketTagList(data);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    };
    // 查询客户标签
    const queryAllStandardTag = () => {
      dispatch({
        type: 'tagManagement/queryTagSelectList',
        callback: response => {
          if (response.code == 200) {
            if (response.data) {
              setStandardTasList(response.data);
              setStandardTasListRender(response.data);
              setOriginStandardTasList(response.data);
            }
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    };

    // 选择客户标签
    const hanldeChangeCustomerTag = async selectedValue => {
      console.log('hanldeChangeCustomerTag', selectedValue);
      if (selectedValue) {
        const conditionValues = [...customerTagIds]; // 复制当前的状态
        conditionValues.push(selectedValue);

        const filterOption = orginStandardTagList.map(category => ({
          ...category,
          tagList: category.tagList.filter(
            tag => !conditionValues.includes(tag.tagId),
          ),
        }));
        setStandardTasList(filterOption);
        setCustomerTagIds(conditionValues);
      }
    };
    // s删除客户标签
    const handleDeselect = selectedValue => {
      if (selectedValue) {
        let conditionValues = [...customerTagIds];

        let findIndex = conditionValues.findIndex(
          item => item == selectedValue,
        );
        if (findIndex > -1) {
          conditionValues.splice(findIndex, 1);
        }
        const filterOption = orginStandardTagList.map(category => ({
          ...category,
          tagList: category.tagList.filter(
            tag => !conditionValues.includes(tag.tagId),
          ),
        }));
        setStandardTasList(filterOption); // 删除时回复下拉
        setCustomerTagIds(conditionValues);
      }
    };
    // 清除所有选项
    const handleClearAllOption = selectedValue => {
      setStandardTasList(orginStandardTagList); // 删除时回复下拉
      setCustomerTagIds([]);
    };
    // 选择客户标签
    const hanldeChangeTicketTag = async selectedValue => {
      if (selectedValue) {
        const conditionValues = [...ticketTagIds]; // 复制当前的状态
        conditionValues.push(selectedValue);

        const filterOption = orginTicketTagList.map(category => ({
          ...category,
          tagList: category.tagList.filter(
            tag => !conditionValues.includes(tag.tagId),
          ),
        }));
        setTicketTagList(filterOption);
        setTicketTagIds(conditionValues);
      }
    };
    // s删除客户标签
    const handleTicketTagDeselect = selectedValue => {
      if (selectedValue) {
        let conditionValues = [...ticketTagIds];

        let findIndex = conditionValues.findIndex(
          item => item == selectedValue,
        );
        if (findIndex > -1) {
          conditionValues.splice(findIndex, 1);
        }
        const filterOption = orginTicketTagList.map(category => ({
          ...category,
          tagList: category.tagList.filter(
            tag => !conditionValues.includes(tag.tagId),
          ),
        }));
        setTicketTagList(filterOption); // 删除时回复下拉
        setTicketTagIds(conditionValues);
      }
    };
    // 清除所有选项
    const handleTicketTagClearAllOption = selectedValue => {
      setTicketTagList(orginTicketTagList); // 删除时回复下拉
      setTicketTagIds([]);
    };

    // 电话号码
    // 对敏感信息(邮箱和电话)进行脱敏处理
    const maskSensitiveInfo = content => {
      if (!content) return content;
      let uuid = uuidv4();
      // 先处理邮箱脱敏
      let result = maskEmails(content);
      // 再处理电话号码脱敏
      let resultPhone = maskPhoneNumbers(result, uuid);

      if (resultPhone.includes(`__${uuid}__`)) {
        // 查找并替换所有匹配的模式 __uuid__masked__
        const pattern = new RegExp(`__${uuid}__([^_]+)__`, 'g');
        // 使用正则表达式匹配所有实例
        const matches = [...resultPhone.matchAll(pattern)];

        if (matches.length > 0) {
          // 构建带有Popover的JSX结构
          let parts = [];
          let lastIndex = 0;

          matches.forEach(match => {
            // 添加匹配前的文本
            if (match.index > lastIndex) {
              parts.push(resultPhone.substring(lastIndex, match.index));
            }

            // 添加带Popover的脱敏值
            const maskedValue = match[1]; // 获取脱敏后的值
            parts.push(<span>{maskedValue}</span>);

            // 更新lastIndex为当前匹配结束位置
            lastIndex = match.index + match[0].length;
          });

          // 添加最后一个匹配之后的文本
          if (lastIndex < resultPhone.length) {
            parts.push(resultPhone.substring(lastIndex));
          }

          return <span>{parts}</span>;
        }
        return resultPhone;
      } else {
        return <span>{resultPhone}</span>;
      }
    };

    // 邮箱脱敏处理
    const maskEmails = content => {
      if (!content) return content;

      // 邮箱正则表达式 - 匹配文本中的所有邮箱
      const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;

      return content.replace(emailRegex, email => {
        return maskEmail(email);
      });
    };

    // 对单个邮箱进行脱敏
    const maskEmail = email => {
      if (!email) return email;
      // 判断是否需要脱敏
      const shouldMaskPhone = user.workbenchPhoneHide === '1';
      if (!shouldMaskPhone) return email;
      const parts = email.split('@');
      if (parts.length !== 2) return email;

      const localPart = parts[0];
      const domainPart = parts[1];

      // 对本地部分进行脱敏处理
      let maskedLocalPart = '';
      if (localPart.length > 2) {
        maskedLocalPart =
          localPart[0] + '******' + localPart[localPart.length - 1];
      } else {
        maskedLocalPart = localPart[0] + '******';
      }

      // 对域名部分进行脱敏处理
      const domainParts = domainPart.split('.');
      const domainName = domainParts[0];
      const domainSuffix = domainParts.slice(1).join('.');

      let maskedDomainName = '***';
      if (domainName.length > 0) {
        maskedDomainName = '***' + domainName.substr(-3);
      }

      return maskedLocalPart + '@' + maskedDomainName + '.' + domainSuffix;
    };

    // 电话号码脱敏处理
    const maskPhoneNumbers = (content, uuid) => {
      if (!content) return content;

      // 判断是否需要脱敏
      const shouldMaskPhone = user.workbenchPhoneHide === '1';
      if (!shouldMaskPhone) return content;

      // 国际电话号码正则表达式
      const phoneRegex = /(\+\d{1,4})?(\d{5,15})/g;
      return content.replace(
        phoneRegex,
        (match, countryCode = '', phoneNumber) => {
          // 验证是否为有效电话号码
          let validPhoneNumber = false;

          try {
            const fullNumber = (countryCode || '+') + phoneNumber;

            const phoneNumberInfo = parsePhoneNumberFromString(fullNumber);
            validPhoneNumber = phoneNumberInfo?.number;
            // phoneNumberInfo?.country && phoneNumberInfo?.number;
          } catch (error) {
            validPhoneNumber = false;
          }

          if (
            validPhoneNumber &&
            phoneNumber.length >= 5 &&
            phoneNumber.length <= 15
          ) {
            const maskedNumber =
              match[0] + match[1] + '******' + match[match.length - 1];
            return `__${uuid}__${maskedNumber}__`;
          }
          return match;
        },
      );
    };
    return (
      <div
        className={styles.contentBox}
        id="contentBox"
        style={{
          width: +workTableTabValue === 4 ? '100%' : leftWidth,
          maxWidth: +workTableTabValue === 4 ? '100%' : '',
        }}
      >
        <Spin spinning={loading}>
          <Tabs
            onChange={e => onChangeTabs(e)}
            activeKey={workTableTabValue}
            type="card"
            items={[
              // *****************************************************电话*****************************************************
              user.agentAccessChannel?.search('3') != -1 && {
                label: (
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    {+workTableTabValue === 1 ? PhoneSvg() : UnPhoneSvg()}
                    {
                      <span
                        style={{
                          color: +workTableTabValue === 1 ? '#3463FC' : '#333',
                          fontWeight: +workTableTabValue === 1 ? 700 : 400,
                          fontSize: +workTableTabValue === 1 ? '14px' : '12px',
                        }}
                      >
                        <FormattedMessage
                          id="marketing.channel.type.phone"
                          defaultMessage="电话"
                        />
                      </span>
                    }
                  </span>
                ),
                key: 1,
                children:
                  user.voiceSupplierRealName === 'Connect' ? (
                    <TwilioComponents
                      getPhoneCurrentInfo={e => getPhoneCurrentInfo(e)}
                      ref={callComponentRef}
                    />
                  ) : (
                    // <CallComponents
                    //   getPhoneCurrentInfo={e => getPhoneCurrentInfo(e)}
                    //   ref={callComponentRef}
                    // />
                    <TwilioComponents
                      getPhoneCurrentInfo={e => getPhoneCurrentInfo(e)}
                      ref={callComponentRef}
                    />
                  ),
              },
              // *****************************************************聊天*****************************************************
              user.agentAccessChannel?.search('1') != -1 && {
                label: (
                  <span
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '4px',
                    }}
                  >
                    {+workTableTabValue === 2 ? ChatSvg() : UnselectChatSvg()}
                    {
                      <span
                        style={{
                          color: +workTableTabValue === 2 ? '#3463FC' : '#333',
                          fontWeight: +workTableTabValue === 2 ? 700 : 400,
                          fontSize: +workTableTabValue === 2 ? '14px' : '12px',
                        }}
                      >
                        <FormattedMessage
                          id="new.worktable.chat.title"
                          defaultMessage="聊天"
                        />
                      </span>
                    }
                    {['2', '1,2', '2,1'].includes(user.receiveTicketType) &&
                    ticketType === 2 ? (
                      <span
                        style={{ fontSize: +workTableTabValue === 2 ? 14 : 12 }}
                      >
                        <FormattedMessage
                          id="new.worktable.chat.ticket.manual.collection.checked1"
                          defaultMessage="(手工领取)"
                        />
                      </span>
                    ) : ['2', '1,2', '2,1'].includes(user.receiveTicketType) &&
                      ticketType === 1 ? (
                      <span
                        style={{ fontSize: +workTableTabValue === 2 ? 14 : 12 }}
                      >
                        <FormattedMessage
                          id="new.worktable.chat.ticket.auto.collection.checked2"
                          defaultMessage="(自动领取)"
                        />
                      </span>
                    ) : (
                      ''
                    )}

                    {/* // {ticketType === 2 && (
                    //   <FormattedMessage
                    //     id="new.worktable.chat.ticket.manual.collection.checked1"
                    //     defaultMessage="(手工领取)"
                    //   />
                    // )} */}
                    <Popover
                      content={
                        <ul className="ticketCollectList">
                          {user.receiveTicketType &&
                            ['2', '1,2', '2,1'].includes(
                              user.receiveTicketType,
                            ) && (
                              <>
                                <li onClick={() => handleTicketCollect(2)}>
                                  <FormattedMessage
                                    id="new.worktable.chat.ticket.manual.collection"
                                    defaultMessage="手工领取"
                                  />
                                </li>
                                <li onClick={() => handleTicketCollect(1)}>
                                  <FormattedMessage
                                    id="new.worktable.chat.ticket.auto.collection"
                                    defaultMessage="自动领取"
                                  />
                                </li>
                              </>
                            )}
                        </ul>
                      }
                      placement="bottomLeft"
                      title={null}
                      trigger="click"
                      open={open}
                      onOpenChange={handleOpenChange}
                      overlayClassName="ticketContent"
                    >
                      {/* 只配置自动领取不展示箭头 */}
                      {!['1'].includes(user.receiveTicketType)
                        ? +workTableTabValue === 2 &&
                          ['2', '1,2', '2,1'].includes(user.receiveTicketType)
                          ? SelectTriangle()
                          : UnSelectTriangle()
                        : ''}
                    </Popover>
                  </span>
                ),
                key: 2,
                children: (
                  <div className={styles.contentBoxTab}>
                    <div className={styles.contentBoxTabTop}>
                      <Radio.Group
                        onChange={e => onChangeCheck(e)}
                        block
                        options={
                          ticketType === 2
                            ? newOption
                            : ticketType === 3
                            ? adminOption
                            : options
                        }
                        value={checkValue}
                        optionType="button"
                        buttonStyle="solid"
                      />
                      <div className={styles.formDiv}>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: '100%',
                          }}
                        >
                          <Input
                            value={search ? search : null}
                            placeholder={getIntl().formatMessage({
                              id: 'document.knowledge.base.input.tips',
                            })}
                            onChange={e => searchChange(e)}
                            onPressEnter={() => searchPressEnter()}
                            prefix={<Search />}
                          />
                          {formHide ? (
                            <Button
                              onClick={() => showForm()}
                              type="text"
                              style={{
                                backgroundColor: isHoveredDown ? '#fff' : '', // 动态背景颜色
                              }}
                              onMouseEnter={() => setIsHoveredDown(true)}
                              onMouseLeave={() => setIsHoveredDown(false)}
                              icon={
                                isHoveredDown ? DownIconChecked() : DownIcon()
                              }
                            ></Button>
                          ) : (
                            <Button
                              onClick={() => showForm()}
                              type="text"
                              style={{
                                backgroundColor: isHoveredUp ? '#fff' : '', // 动态背景颜色
                              }}
                              onMouseEnter={() => setIsHoveredUp(true)}
                              onMouseLeave={() => setIsHoveredUp(false)}
                              icon={isHoveredUp ? UpIconChecked() : UpIcon()}
                            ></Button>
                          )}
                        </div>

                        {formHide ? null : (
                          <div>
                            <Select
                              tagRender={ticketTagRender}
                              optionLabelProp="label"
                              optionFilterProp="children"
                              showArrow={false}
                              showSearch
                              mode="multiple"
                              filterOption={(inputValue, option) =>
                                option.label
                                  .toLowerCase()
                                  .indexOf(inputValue.toLowerCase()) >= 0
                              }
                              style={{ marginBottom: 0 }}
                              allowClear
                              placeholder={getIntl().formatMessage({
                                id: 'allocation.please.select.ticket.tag',
                                defaultMessage: '请选择工单标签',
                              })}
                              value={ticketTagIds}
                              onClear={value =>
                                handleTicketTagClearAllOption(value)
                              }
                              onDeselect={value =>
                                handleTicketTagDeselect(value)
                              }
                              onSelect={value => hanldeChangeTicketTag(value)}
                            >
                              {ticketTagList.map(group => (
                                <OptGroup
                                  key={group.categoryId}
                                  label={
                                    group.categoryContent !==
                                    'private_tag_category_code'
                                      ? group.categoryContent
                                      : getIntl().formatMessage({
                                          id: 'tag.management.tab.private',
                                          defaultValue: '私有标签',
                                        })
                                  }
                                >
                                  {group.tagList.map(option => (
                                    <Option
                                      key={option.tagId}
                                      value={option.tagId}
                                      label={option.tagContent}
                                    >
                                      <div
                                        style={{
                                          display: 'flex',
                                          alignItems: 'center',
                                        }}
                                      >
                                        <div
                                          style={{
                                            width: '12px',
                                            height: '12px',
                                            backgroundColor: option.tagColor,
                                            marginRight: '4px',
                                          }}
                                        ></div>
                                        <span>{option.tagContent}</span>
                                      </div>
                                    </Option>
                                  ))}
                                </OptGroup>
                              ))}
                            </Select>
                            <Select
                              tagRender={tagRender}
                              optionLabelProp="label"
                              optionFilterProp="children"
                              showArrow={false}
                              showSearch
                              mode="multiple"
                              filterOption={(inputValue, option) =>
                                option.label
                                  .toLowerCase()
                                  .indexOf(inputValue.toLowerCase()) >= 0
                              }
                              style={{ marginBottom: 0 }}
                              allowClear
                              placeholder={getIntl().formatMessage({
                                id: 'allocation.please.select.customer.tag',
                                defaultMessage: '请选择客户标签',
                              })}
                              value={customerTagIds}
                              onClear={value => handleClearAllOption(value)}
                              onDeselect={value => handleDeselect(value)}
                              onSelect={value => hanldeChangeCustomerTag(value)}
                            >
                              {standardTagList.map(group => (
                                <OptGroup
                                  key={group.categoryId}
                                  label={
                                    group.categoryContent !==
                                    'private_tag_category_code'
                                      ? group.categoryContent
                                      : getIntl().formatMessage({
                                          id: 'tag.management.tab.private',
                                          defaultValue: '私有标签',
                                        })
                                  }
                                >
                                  {group.tagList.map(option => (
                                    <Option
                                      key={option.tagId}
                                      value={option.tagId}
                                      label={option.tagContent}
                                    >
                                      <div
                                        style={{
                                          display: 'flex',
                                          alignItems: 'center',
                                        }}
                                      >
                                        <div
                                          style={{
                                            width: '12px',
                                            height: '12px',
                                            backgroundColor: option.tagColor,
                                            marginRight: '4px',
                                          }}
                                        ></div>
                                        <span>{option.tagContent}</span>
                                      </div>
                                    </Option>
                                  ))}
                                </OptGroup>
                              ))}
                            </Select>

                            <Select
                              value={
                                workRecordTypeCode ? workRecordTypeCode : null
                              }
                              showSearch
                              placeholder={getIntl().formatMessage({
                                id:
                                  'create.work.order.work.order.type.required',
                                defaultValue: '请选择工单类型',
                              })}
                              allowClear
                              fieldNames={{
                                label: 'workRecordTypeName',
                                value: 'workRecordTypeId',
                                key: 'workRecordTypeId',
                              }}
                              filterOption={(inputValue, option) =>
                                option.workRecordTypeName
                                  .toLowerCase()
                                  .indexOf(inputValue.toLowerCase()) >= 0
                              }
                              options={workRecordTypeList}
                              onChange={value =>
                                handleChangeSelectWorker(value)
                              }
                            />
                            <RangePicker
                              value={rangeValue}
                              showTime={{ format: 'HH:mm:ss' }}
                              format="YYYY-MM-DD HH:mm:ss"
                              onChange={(value, dateString) => {
                                onRangePickerChange(value, dateString);
                              }}
                            />
                          </div>
                        )}
                      </div>
                      <div className={styles.featuresDiv}>
                        {+checkValue === 2 ? (
                          <Checkbox
                            style={{ marginLeft: 5 }}
                            indeterminate={indeterminatePending}
                            onChange={onCheckAllChange}
                            checked={checkAllPending}
                          >
                            <FormattedMessage
                              id="new.worktable.ticket.all.select"
                              defaultMessage="全选"
                            />
                          </Checkbox>
                        ) : (
                          <Checkbox
                            style={{ marginLeft: 5 }}
                            indeterminate={indeterminate}
                            onChange={onCheckAllChangeProcess}
                            checked={checkAll}
                          >
                            <FormattedMessage
                              id="new.worktable.ticket.all.select"
                              defaultMessage="全选"
                            />
                          </Checkbox>
                        )}
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'flex-end',
                          }}
                        >
                          <Select
                            value={channelType ? channelType : null}
                            className={styles.workSelect}
                            popupClassName="selectFilterContent"
                            allowClear
                            bordered={false}
                            placeholder={getIntl().formatMessage({
                              id: 'merge.work.order.configuration.channel.type',
                            })}
                            onChange={value => handleChangeSelectChannel(value)}
                            suffixIcon={
                              <CaretDownOutlined style={{ color: '#d9d9d9' }} />
                            }
                          >
                            {channelTypeList?.map(items => {
                              // if (items.code == '1') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={EmailIcon} />
                              //       {items.name}
                              //     </Option>
                              //   );
                              // } else
                              if (items.code == '3') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={FacebookIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '4') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={WhatsAppIcon} />
                                    {items.name}
                                  </Option>
                                );
                              }
                              // else if (items.code == '5') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={TwitterIcon} />
                              //       {items.name}
                              //     </Option>
                              //   );
                              // } else if (items.code == '6') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={LineIcon} /> {items.name}
                              //     </Option>
                              //   );
                              // } else if (items.code == '7') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={PhoneIcon} /> {items.name}
                              //     </Option>
                              //   );
                              // }
                              else if (items.code == '8') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={ChatIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '9') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={AppChatOutlinedIcon} />
                                    {items.name}
                                  </Option>
                                );
                              }
                              // else if (items.code == '10') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={WebVideoOutlinedIcon} />
                              //       {items.name}
                              //     </Option>
                              //   );
                              // } else if (items.code == '11') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={AppVideoOutlinedIcon} />
                              //       {items.name}
                              //     </Option>
                              //   );
                              // }
                              else if (items.code == '12') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={AwsChannelIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '13') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={NewInstagramIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '14') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={NewLineIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '15') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={NewWeComIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '16') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={NewWechatOfficialAccountIcon} />
                                    {items.name}
                                  </Option>
                                );
                              }
                              // else if (items.code == '17') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={NewWebOnlineVoiceIcon} />
                              //       {items.name}
                              //     </Option>
                              //   );
                              // } else if (items.code == '18') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={NewAppOnlineVoiceIcon} />
                              //       {items.name}
                              //     </Option>
                              //   );
                              // }
                              else if (items.code == '19') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={NewTwitterIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '20') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={NewTelegramIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '21') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={NewWeChatMiniProgramIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '22') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={NewShopifyIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '24') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={TikTokIcon} />
                                    {items.name}
                                  </Option>
                                );
                              } else if (items.code == '25') {
                                return (
                                  <Option value={items.code} key={items.code}>
                                    <img src={DiscordIcon} />
                                    {items.name}
                                  </Option>
                                );
                              }
                              // else if (items.code == '23') {
                              //   return (
                              //     <Option value={items.code} key={items.code}>
                              //       <img src={NewGooglePlayIcon} />
                              //       {items.name}
                              //     </Option>
                              //   );
                              // }
                              else {
                                return (
                                  // <Option value={items.code} key={items.code}>
                                  //   {items.name}
                                  // </Option>
                                  null
                                );
                              }
                            })}
                          </Select>

                          <Dropdown
                            menu={{
                              items: dropdownItems,
                              onClick: onDropdown,
                              defaultSelectedKeys: [1],
                            }}
                            getPopupContainer={() =>
                              document.getElementById('contentBox')
                            }
                          >
                            <Button
                              onMouseEnter={() => setIsHovered(true)}
                              onMouseLeave={() => setIsHovered(false)}
                              style={{
                                backgroundColor: isHovered ? '#3463fc' : '', // 动态背景颜色
                                flex: 2,
                              }}
                              icon={isHovered ? SortSvgChecked() : SortSvg()}
                            ></Button>
                          </Dropdown>

                          {+checkValue === 1 && (
                            <Dropdown
                              menu={{
                                items: dropdownOptionItems,
                                onClick: onDropdownOption,
                                defaultSelectedKeys: [1],
                              }}
                              getPopupContainer={() =>
                                document.getElementById('contentBox')
                              }
                            >
                              <span className={styles.chooseOrderClass}>
                                {ChooseOrderIcon()}
                              </span>
                            </Dropdown>
                          )}
                        </div>
                      </div>
                    </div>
                    <div
                      className={styles.contentBoxTabBottom}
                      id={'contentBoxSon'}
                    >
                      {/*待处理渲染 */}
                      {+checkValue === 1 &&
                      globalList &&
                      globalList.length > 0 ? (
                        <Checkbox.Group
                          style={{ width: '100%' }}
                          value={checkedList}
                          onChange={onChangeGroup}
                        >
                          <InfiniteScroll
                            height={'calc(100vh - 220px)'}
                            dataLength={globalList?.length}
                            style={{
                              width: '100%',
                            }}
                            next={() => fetchMoreData()}
                            hasMore={hasMore}
                            scrollableTarget={'contentBoxSon'}
                            loader={
                              <p
                                style={{
                                  textAlign: 'center',
                                  color: '#3463FC',
                                  fontSize: 12,
                                }}
                              >
                                <b>
                                  <FormattedMessage
                                    id="definition.synonyms.upload.tips"
                                    defaultMessage="上滑加载中..."
                                  />
                                </b>
                              </p>
                            }
                            // 加载结束提示信息
                            endMessage={
                              <p
                                style={{
                                  textAlign: 'center',
                                  color: '#999',
                                  fontSize: 12,
                                }}
                              >
                                <b>
                                  <FormattedMessage
                                    id="definition.synonyms.upload.nodata.tips"
                                    defaultMessage="没有更多了~"
                                  />
                                </b>
                              </p>
                            }
                          >
                            {globalList &&
                              globalList.length > 0 &&
                              globalList?.map(item => {
                                return (
                                  <div
                                    className={styles.contentBoxTabList}
                                    style={{
                                      background:
                                        item?.isTop === '2' ? '#eee' : '000',
                                    }}
                                  >
                                    <div
                                      className={styles.contentBoxTabListItem}
                                      style={{
                                        borderRadius:
                                          item?.selected === true ? 4 : '',
                                        background:
                                          item?.selected === true
                                            ? 'linear-gradient(180deg, #C2DFFC 0%, #C7D0FB 100%)'
                                            : '',
                                      }}
                                      // onClick={() => handleCurrentSession(item)}
                                    >
                                      <Checkbox
                                        checked={checkedList.includes(
                                          item.ticketId,
                                        )}
                                        value={item.ticketId}
                                        style={{
                                          marginRight: 5,
                                          display: 'flex',
                                          alignItems: 'center',
                                          width: '100%',
                                          marginLeft: 5,
                                          flex: 1,
                                        }}
                                        onChange={e => handleCheckboxChange(e)}
                                        key={item.ticketId}
                                      ></Checkbox>
                                      <div
                                        onClick={() =>
                                          handleCurrentSessionGlobal(item)
                                        }
                                        style={{
                                          width: '100%',
                                          float: 'left',
                                        }}
                                      >
                                        <div
                                          className={
                                            styles.contentBoxTabListItemImg
                                          }
                                        >
                                          {/* <img src={Avatar} /> */}
                                          <div
                                            className={styles.headAva}
                                            style={{
                                              backgroundColor: item.customerAvatorColor
                                                ? item.customerAvatorColor
                                                : '#3463FC',
                                            }}
                                          >
                                            <span>
                                              {item?.username?.substr(0, 1)}
                                            </span>
                                          </div>
                                          {item?.unReadCount ? (
                                            <span className={styles.noReadNum}>
                                              {item?.unReadCount}
                                            </span>
                                          ) : null}
                                        </div>
                                        <div
                                          className={
                                            styles.contentBoxTabListItemFlex
                                          }
                                        >
                                          <div
                                            className={
                                              styles.contentBoxTabListItemFlexTop
                                            }
                                          >
                                            <div
                                              className={
                                                styles.contentBoxTabListItemFlexFront
                                              }
                                            >
                                              <span
                                                className={
                                                  styles.contentBoxTabListItemFlexname
                                                }
                                                style={{
                                                  maxWidth: `${leftWidth -
                                                    200}px`,
                                                }}
                                                title={item?.username}
                                              >
                                                {item?.username}
                                              </span>

                                              {item?.originalStatus ? (
                                                <div
                                                  className={
                                                    styles.contentBoxTabListItemFlexZhuan
                                                  }
                                                >
                                                  转
                                                </div>
                                              ) : null}
                                            </div>
                                            {/* <span
                                        className={
                                          styles.contentBoxTabListItemFlexTime
                                        }
                                      > */}
                                            {/* 这里要写倒计时
                                        /**queryProcessingWorkOrDerListSchedule返回内容
             * workRecordId 工单id
             *  timeStatus 当前时间状态 是否超时  1 - 超出，2 - 剩余
             * scheduleTime  时间
             * timeType  时间类型   1 - 响应  2-解决
             */}
                                            {item?.timeStatus &&
                                              item?.timeType && (
                                                <span
                                                  style={{
                                                    display: 'inline-block',
                                                    width: '38%',
                                                    textAlign: 'right',
                                                    textOverflow: 'ellipsis',
                                                    overflow: 'hidden',
                                                    whiteSpace: 'nowrap',
                                                  }}
                                                >
                                                  {item?.timeType === '1' ? (
                                                    item?.timeStatus === '1' ? (
                                                      <Tooltip
                                                        placement="top"
                                                        title={getIntl().formatMessage(
                                                          {
                                                            id:
                                                              'new.worktable.chatList.time.hover.content.4',
                                                          },
                                                          {
                                                            timeValue:
                                                              item?.scheduleTime,
                                                          },
                                                        )}
                                                      >
                                                        <span
                                                          style={{
                                                            color: '#F22417',
                                                            marginTop: 4,
                                                            display:
                                                              'inline-block',
                                                          }}
                                                        >
                                                          <span
                                                            style={{
                                                              display:
                                                                'inline-block',
                                                              transform:
                                                                'translateY(3px)',
                                                            }}
                                                          >
                                                            {timeOverReplay()}
                                                            &nbsp;
                                                          </span>
                                                          <span>
                                                            {item?.scheduleTime}
                                                          </span>
                                                        </span>
                                                      </Tooltip>
                                                    ) : (
                                                      <Tooltip
                                                        placement="top"
                                                        title={getIntl().formatMessage(
                                                          {
                                                            id:
                                                              'new.worktable.chatList.time.hover.content.3',
                                                          },
                                                          {
                                                            timeValue:
                                                              item?.scheduleTime,
                                                          },
                                                        )}
                                                      >
                                                        <span
                                                          style={{
                                                            color: '#00B900',
                                                            marginTop: 4,
                                                            display:
                                                              'inline-block',
                                                          }}
                                                        >
                                                          <span
                                                            style={{
                                                              display:
                                                                'inline-block',
                                                              transform:
                                                                'translateY(3px)',
                                                            }}
                                                          >
                                                            {timeSurplusReplay()}
                                                            &nbsp;
                                                          </span>
                                                          <span>
                                                            {item?.scheduleTime}
                                                          </span>
                                                        </span>
                                                      </Tooltip>
                                                    )
                                                  ) : item?.timeStatus ===
                                                    '1' ? (
                                                    <Tooltip
                                                      placement="top"
                                                      title={getIntl().formatMessage(
                                                        {
                                                          id:
                                                            'new.worktable.chatList.time.hover.content.2',
                                                        },
                                                        {
                                                          timeValue:
                                                            item?.scheduleTime,
                                                        },
                                                      )}
                                                    >
                                                      <span
                                                        style={{
                                                          color: '#F22417',
                                                          marginTop: 4,
                                                          display:
                                                            'inline-block',
                                                        }}
                                                      >
                                                        <span
                                                          style={{
                                                            display:
                                                              'inline-block',
                                                            transform:
                                                              'translateY(3px)',
                                                          }}
                                                        >
                                                          {timeOverSolve()}
                                                          &nbsp;
                                                        </span>
                                                        <span>
                                                          {item?.scheduleTime}
                                                        </span>
                                                      </span>
                                                    </Tooltip>
                                                  ) : (
                                                    <Tooltip
                                                      placement="top"
                                                      title={getIntl().formatMessage(
                                                        {
                                                          id:
                                                            'new.worktable.chatList.time.hover.content.1',
                                                        },
                                                        {
                                                          timeValue:
                                                            item?.scheduleTime,
                                                        },
                                                      )}
                                                    >
                                                      <span
                                                        style={{
                                                          color: '#00B900',
                                                          marginTop: 4,
                                                          display:
                                                            'inline-block',
                                                        }}
                                                      >
                                                        <span
                                                          style={{
                                                            display:
                                                              'inline-block',
                                                            transform:
                                                              'translateY(3px)',
                                                          }}
                                                        >
                                                          {timeSurplusSolve()}
                                                          &nbsp;
                                                        </span>
                                                        <span>
                                                          {item?.scheduleTime}
                                                        </span>
                                                      </span>
                                                    </Tooltip>
                                                  )}
                                                </span>
                                              )}
                                            <span
                                              style={{
                                                marginLeft: 5,
                                                display: 'inline-block',
                                                textAlign: 'right',
                                                clear: 'both',
                                                float: 'right',
                                                marginTop: 5,
                                              }}
                                              className={styles.listImg}
                                            >
                                              {(() => {
                                                switch (item?.channelId) {
                                                  case '1':
                                                    return (
                                                      <img src={EmailIcon} />
                                                    );
                                                  case '3':
                                                    return (
                                                      <img src={FacebookIcon} />
                                                    );
                                                  case '4':
                                                    return (
                                                      <img src={WhatsAppIcon} />
                                                    );
                                                  case '5':
                                                    return (
                                                      <img src={TwitterIcon} />
                                                    );
                                                  case '6':
                                                    return (
                                                      <img src={LineIcon} />
                                                    );
                                                  case '7':
                                                    return (
                                                      <img src={PhoneIcon} />
                                                    );
                                                  case '8':
                                                    return (
                                                      <img src={ChatIcon} />
                                                    );
                                                  case '9':
                                                    return (
                                                      <img
                                                        src={
                                                          AppChatOutlinedIcon
                                                        }
                                                      />
                                                    );
                                                  case '10':
                                                    return (
                                                      <img
                                                        src={
                                                          WebVideoOutlinedIcon
                                                        }
                                                      />
                                                    );
                                                  case '11':
                                                    return (
                                                      <img
                                                        src={
                                                          AppVideoOutlinedIcon
                                                        }
                                                      />
                                                    );
                                                  case '12':
                                                    return (
                                                      <img
                                                        src={AwsChannelIcon}
                                                      />
                                                    );
                                                  case '13':
                                                    return (
                                                      <img
                                                        src={NewInstagramIcon}
                                                      />
                                                    );
                                                  case '14':
                                                    return (
                                                      <img src={NewLineIcon} />
                                                    );
                                                  case '15':
                                                    return (
                                                      <img src={NewWeComIcon} />
                                                    );
                                                  case '16':
                                                    return (
                                                      <img
                                                        src={
                                                          NewWechatOfficialAccountIcon
                                                        }
                                                      />
                                                    );
                                                  case '17':
                                                    return (
                                                      <img
                                                        src={
                                                          NewWebOnlineVoiceIcon
                                                        }
                                                      />
                                                    );
                                                  case '18':
                                                    return (
                                                      <img
                                                        src={
                                                          NewAppOnlineVoiceIcon
                                                        }
                                                      />
                                                    );

                                                  case '19':
                                                    return (
                                                      <img
                                                        src={NewTwitterIcon}
                                                      />
                                                    );
                                                  case '20':
                                                    return (
                                                      <img
                                                        src={NewTelegramIcon}
                                                      />
                                                    );
                                                  case '21':
                                                    return (
                                                      <img
                                                        src={
                                                          NewWeChatMiniProgramIcon
                                                        }
                                                      />
                                                    );
                                                  case '22':
                                                    return (
                                                      <img
                                                        src={NewShopifyIcon}
                                                      />
                                                    );
                                                  case '23':
                                                    return (
                                                      <img
                                                        src={NewGooglePlayIcon}
                                                      />
                                                    );
                                                  case '24':
                                                    return (
                                                      <img src={TikTokIcon} />
                                                    );
                                                  case '25':
                                                    return (
                                                      <img src={DiscordIcon} />
                                                    );
                                                }
                                              })()}
                                            </span>

                                            {/* </span> */}
                                          </div>
                                          <div
                                            className={
                                              styles.contentBoxTabListItemFlexBottom
                                            }
                                          >
                                            <span
                                              style={{
                                                width: '70%',
                                                textOverflow: 'ellipsis',
                                                display: 'inline-block',
                                                overflow: 'hidden',
                                                whiteSpace: 'nowrap',
                                                verticalAlign: 'top',
                                              }}
                                            >
                                              {/* 回复内容类型 1、文本 2、图片 3、视频 4、附件 5、音频 */}
                                              {(() => {
                                                switch (
                                                  item?.messageList[
                                                    item?.messageList?.length -
                                                      1
                                                  ]?.contentType
                                                ) {
                                                  case 1:
                                                  case 'text':
                                                  case 'text/pain':
                                                  case 'markdown':
                                                    let content =
                                                      item?.messageList[
                                                        item?.messageList
                                                          ?.length - 1
                                                      ].content;
                                                    let replyType =
                                                      item?.messageList[
                                                        item?.messageList
                                                          ?.length - 1
                                                      ].type;
                                                    let dataStatus =
                                                      item?.messageList[
                                                        item?.messageList
                                                          ?.length - 1
                                                      ].dataStatus;

                                                    return dataStatus ===
                                                      '999' ? (
                                                      getIntl().formatMessage({
                                                        id:
                                                          'workerOffers.withdraw.message.tips',
                                                        defaultMessage:
                                                          '您已经撤回一条消息',
                                                      })
                                                    ) : isValidJson(
                                                        content,
                                                        item,
                                                      ) ? (
                                                      replyType === 'aiAgent' ||
                                                      replyType === 7 ? (
                                                        <div>
                                                          [
                                                          <FormattedMessage id="work.order.detail.customer.entry.intention.reason.1" />
                                                          ]
                                                        </div>
                                                      ) : replyType ===
                                                          'intelligentFormFilling' ||
                                                        replyType === 9 ? (
                                                        <div>
                                                          [
                                                          <FormattedMessage id="work.order.detail.ai.intelligent.form.filling" />
                                                          ]
                                                        </div>
                                                      ) : (
                                                        <div>
                                                          [
                                                          <FormattedMessage id="work.order.detail.ai.intelligence.summary" />
                                                          ]
                                                        </div>
                                                      )
                                                    ) : content?.includes(
                                                        '![](',
                                                      ) ? (
                                                      `[${getIntl().formatMessage(
                                                        {
                                                          id:
                                                            'new.worktable.email.editor.image',
                                                          defaultValue: '图片',
                                                        },
                                                      )}]${
                                                        content.split('\n\n')[1]
                                                      }`
                                                    ) : item?.messageList[
                                                        item?.messageList
                                                          ?.length - 1
                                                      ]?.replyType == 2 ? (
                                                      maskSensitiveInfo(content)
                                                    ) : (
                                                      content
                                                    );
                                                  case 'textimage':
                                                  case 'image':
                                                  case 2:
                                                    return (
                                                      <div>
                                                        [
                                                        <FormattedMessage
                                                          id="knowledge.QA.label.2"
                                                          defaultMessage="图片"
                                                        />
                                                        ]
                                                      </div>
                                                    );
                                                  case 'video':
                                                  case 'music':
                                                  case 3:
                                                  case 5:
                                                    return (
                                                      <div>
                                                        [
                                                        <FormattedMessage
                                                          id="knowledge.QA.label.3"
                                                          defaultMessage="视频"
                                                        />
                                                        ]
                                                      </div>
                                                    );
                                                  case 'link':
                                                  case 4:
                                                    return (
                                                      '[' +
                                                      item?.messageList[
                                                        item?.messageList
                                                          ?.length - 1
                                                      ].fileName +
                                                      ']'
                                                    );
                                                  case 'evaluation':
                                                    return (
                                                      <div>
                                                        [
                                                        <FormattedMessage
                                                          id="work.order.detail.customer.evaluation"
                                                          defaultMessage="用户评价"
                                                        />
                                                        ]
                                                      </div>
                                                    );
                                                  default:
                                                    return '';
                                                }
                                              })()}
                                            </span>
                                            <span
                                              style={{
                                                color: '#999',
                                                // color: 'rgb(153, 153, 153)',
                                                display: 'inline-block',
                                                width: '30%',
                                                verticalAlign: 'top',
                                                textAlign: 'right',
                                                textOverflow: 'ellipsis',
                                                overflow: 'hidden',
                                                whiteSpace: 'nowrap',
                                              }}
                                            >
                                              {item?.messageList &&
                                                item?.messageList.length > 0 &&
                                                displayDateTime(
                                                  item?.messageList[
                                                    item?.messageList?.length -
                                                      1
                                                  ]?.time,
                                                )}
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                          </InfiniteScroll>
                        </Checkbox.Group>
                      ) : // 未分配渲染
                      +checkValue === 2 &&
                        handledPendingList &&
                        handledPendingList.length > 0 ? (
                        <Checkbox.Group
                          style={{ width: '100%' }}
                          value={checkedPendingList}
                          onChange={onChangeGroupPending}
                        >
                          <InfiniteScroll
                            height={'calc(100vh - 220px)'}
                            dataLength={handledPendingList?.length}
                            style={{
                              width: '100%',
                            }}
                            next={() => fetchMoreDataPending()}
                            hasMore={hasMore}
                            scrollableTarget={'contentBoxSon'}
                            loader={
                              <p
                                style={{
                                  textAlign: 'center',
                                  color: '#3463FC',
                                  fontSize: 12,
                                }}
                              >
                                <b>
                                  <FormattedMessage
                                    id="definition.synonyms.upload.tips"
                                    defaultMessage="上滑加载中..."
                                  />
                                </b>
                              </p>
                            }
                            // 加载结束提示信息
                            endMessage={
                              <p
                                style={{
                                  textAlign: 'center',
                                  color: '#999',
                                  fontSize: 12,
                                  marginBottom: '30px',
                                }}
                              >
                                <b>
                                  <FormattedMessage
                                    id="definition.synonyms.upload.nodata.tips"
                                    defaultMessage="没有更多了~"
                                  />
                                </b>
                              </p>
                            }
                          >
                            {handledPendingList &&
                              handledPendingList.length > 0 &&
                              handledPendingList?.map(item => {
                                return (
                                  <div className={styles.contentBoxTabList}>
                                    {/* </Checkbox> */}
                                    {/* <Checkbox
                                    // checked={selectedTicketIds.includes(item.ticketId)}
                                    value={item.ticketId}
                                    style={{ marginRight: 5 }}
                                    // onChange={handleCheckboxChange}
                                  ></Checkbox> */}
                                    <div
                                      className={styles.contentBoxTabListItem}
                                      style={{
                                        borderRadius:
                                          item?.selected === true ? 4 : '',
                                        background:
                                          item?.selected === true
                                            ? 'linear-gradient(180deg, #C2DFFC 0%, #C7D0FB 100%)'
                                            : '',
                                      }}
                                    >
                                      <Checkbox
                                        checked={selectedTicketIds.includes(
                                          item.ticketId,
                                        )}
                                        value={item.ticketId}
                                        style={{
                                          marginRight: 5,
                                          display: 'flex',
                                          alignItems: 'center',
                                          width: '100%',
                                          marginLeft: 5,
                                        }}
                                        onChange={e => handleCheckboxChange(e)}
                                      >
                                        <div
                                          onClick={() =>
                                            handleCurrentSessionPending(item)
                                          }
                                          style={{
                                            width: '100%',
                                            float: 'left',
                                          }}
                                        >
                                          <div
                                            className={
                                              styles.contentBoxTabListItemImg
                                            }
                                          >
                                            {/* <img src={Avatar} /> */}
                                            <div
                                              className={styles.headAva}
                                              style={{
                                                backgroundColor: item.customerAvatorColor
                                                  ? item.customerAvatorColor
                                                  : '#3463FC',
                                              }}
                                            >
                                              <span>
                                                {item?.username?.substr(0, 1)}
                                              </span>
                                            </div>
                                            {item?.unReadCount ? (
                                              <span
                                                className={styles.noReadNum}
                                              >
                                                {item?.unReadCount}
                                              </span>
                                            ) : null}
                                          </div>
                                          <div
                                            className={
                                              styles.contentBoxTabListItemFlex
                                            }
                                          >
                                            <div
                                              className={
                                                styles.contentBoxTabListItemFlexTop
                                              }
                                            >
                                              <div
                                                className={
                                                  styles.contentBoxTabListItemFlexFront
                                                }
                                              >
                                                <span
                                                  className={
                                                    styles.contentBoxTabListItemFlexname
                                                  }
                                                  style={{
                                                    maxWidth: `calc(${leftWidth}px - 250%)`,
                                                  }}
                                                  title={item.username}
                                                >
                                                  {item.username}
                                                </span>

                                                {item?.originalStatus ? (
                                                  <div
                                                    className={
                                                      styles.contentBoxTabListItemFlexZhuan
                                                    }
                                                  >
                                                    转
                                                  </div>
                                                ) : null}
                                              </div>
                                              <span
                                                className={
                                                  styles.contentBoxTabListItemFlexTime
                                                }
                                              >
                                                <span
                                                  style={{
                                                    marginLeft: 5,
                                                    float: 'right',
                                                  }}
                                                  className={styles.listImg}
                                                >
                                                  {(() => {
                                                    switch (item?.channelId) {
                                                      case '1':
                                                        return (
                                                          <img
                                                            src={EmailIcon}
                                                          />
                                                        );
                                                      case '3':
                                                        return (
                                                          <img
                                                            src={FacebookIcon}
                                                          />
                                                        );
                                                      case '4':
                                                        return (
                                                          <img
                                                            src={WhatsAppIcon}
                                                          />
                                                        );
                                                      case '5':
                                                        return (
                                                          <img
                                                            src={TwitterIcon}
                                                          />
                                                        );
                                                      case '6':
                                                        return (
                                                          <img src={LineIcon} />
                                                        );
                                                      case '7':
                                                        return (
                                                          <img
                                                            src={PhoneIcon}
                                                          />
                                                        );
                                                      case '8':
                                                        return (
                                                          <img src={ChatIcon} />
                                                        );
                                                      case '9':
                                                        return (
                                                          <img
                                                            src={
                                                              AppChatOutlinedIcon
                                                            }
                                                          />
                                                        );
                                                      case '10':
                                                        return (
                                                          <img
                                                            src={
                                                              WebVideoOutlinedIcon
                                                            }
                                                          />
                                                        );
                                                      case '11':
                                                        return (
                                                          <img
                                                            src={
                                                              AppVideoOutlinedIcon
                                                            }
                                                          />
                                                        );
                                                      case '12':
                                                        return (
                                                          <img
                                                            src={AwsChannelIcon}
                                                          />
                                                        );
                                                      case '13':
                                                        return (
                                                          <img
                                                            src={
                                                              NewInstagramIcon
                                                            }
                                                          />
                                                        );
                                                      case '14':
                                                        return (
                                                          <img
                                                            src={NewLineIcon}
                                                          />
                                                        );
                                                      case '15':
                                                        return (
                                                          <img
                                                            src={NewWeComIcon}
                                                          />
                                                        );
                                                      case '16':
                                                        return (
                                                          <img
                                                            src={
                                                              NewWechatOfficialAccountIcon
                                                            }
                                                          />
                                                        );
                                                      case '17':
                                                        return (
                                                          <img
                                                            src={
                                                              NewWebOnlineVoiceIcon
                                                            }
                                                          />
                                                        );
                                                      case '18':
                                                        return (
                                                          <img
                                                            src={
                                                              NewAppOnlineVoiceIcon
                                                            }
                                                          />
                                                        );

                                                      case '19':
                                                        return (
                                                          <img
                                                            src={NewTwitterIcon}
                                                          />
                                                        );
                                                      case '20':
                                                        return (
                                                          <img
                                                            src={
                                                              NewTelegramIcon
                                                            }
                                                          />
                                                        );
                                                      case '21':
                                                        return (
                                                          <img
                                                            src={
                                                              NewWeChatMiniProgramIcon
                                                            }
                                                          />
                                                        );
                                                      case '22':
                                                        return (
                                                          <img
                                                            src={NewShopifyIcon}
                                                          />
                                                        );
                                                      case '23':
                                                        return (
                                                          <img
                                                            src={
                                                              NewGooglePlayIcon
                                                            }
                                                          />
                                                        );
                                                    }
                                                  })()}
                                                </span>
                                                <span
                                                  style={{
                                                    color: '#999',
                                                    float: 'right',
                                                  }}
                                                >
                                                  {item?.messageList &&
                                                    item?.messageList.length >
                                                      0 &&
                                                    displayDateTime(
                                                      item?.messageList[
                                                        item?.messageList
                                                          ?.length - 1
                                                      ]?.time,
                                                    )}
                                                </span>
                                              </span>
                                            </div>
                                            <div
                                              className={
                                                styles.contentBoxTabListItemFlexBottom
                                              }
                                            >
                                              {/* 回复内容类型 1、文本 2、图片 3、视频 4、附件 5、音频 */}
                                              {(() => {
                                                if (
                                                  item?.messageList &&
                                                  item?.messageList.length > 0
                                                ) {
                                                  switch (
                                                    item?.messageList[
                                                      item?.messageList
                                                        ?.length - 1
                                                    ].contentType
                                                  ) {
                                                    case 1:
                                                    case 'text':
                                                    case 'text/pain':
                                                    case 'markdown':
                                                      let content =
                                                        item?.messageList[
                                                          item?.messageList
                                                            ?.length - 1
                                                        ].content;
                                                      let replyType =
                                                        item?.messageList[
                                                          item?.messageList
                                                            ?.length - 1
                                                        ].type;
                                                      let dataStatus =
                                                        item?.messageList[
                                                          item?.messageList
                                                            ?.length - 1
                                                        ].dataStatus;

                                                      return dataStatus ===
                                                        '999' ? (
                                                        getIntl().formatMessage(
                                                          {
                                                            id:
                                                              'workerOffers.withdraw.message.tips',
                                                            defaultMessage:
                                                              '您已经撤回一条消息',
                                                          },
                                                        )
                                                      ) : isValidJson(
                                                          content,
                                                          item,
                                                        ) ? (
                                                        replyType ===
                                                          'aiAgent' ||
                                                        replyType === 7 ? (
                                                          <div>
                                                            [
                                                            <FormattedMessage id="work.order.detail.customer.entry.intention.reason.1" />
                                                            ]
                                                          </div>
                                                        ) : (
                                                          <div>
                                                            [
                                                            <FormattedMessage id="work.order.detail.ai.intelligence.summary" />
                                                            ]
                                                          </div>
                                                        )
                                                      ) : content?.includes(
                                                          '![](',
                                                        ) ? (
                                                        `[${getIntl().formatMessage(
                                                          {
                                                            id:
                                                              'new.worktable.email.editor.image',
                                                            defaultValue:
                                                              '图片',
                                                          },
                                                        )}]${
                                                          content.split(
                                                            '\n\n',
                                                          )[1]
                                                        }`
                                                      ) : (
                                                        content
                                                      );
                                                    case 'textimage':
                                                    case 'image':
                                                    case 2:
                                                      return (
                                                        <div>
                                                          [
                                                          <FormattedMessage
                                                            id="knowledge.QA.label.2"
                                                            defaultMessage="图片"
                                                          />
                                                          ]
                                                        </div>
                                                      );
                                                    case 'video':
                                                    case 'music':
                                                    case 3:
                                                    case 5:
                                                      return (
                                                        <div>
                                                          [
                                                          <FormattedMessage
                                                            id="knowledge.QA.label.3"
                                                            defaultMessage="视频"
                                                          />
                                                          ]
                                                        </div>
                                                      );
                                                    case 'link':
                                                    case 4:
                                                      return (
                                                        '[' +
                                                        item?.messageList[
                                                          item?.messageList
                                                            ?.length - 1
                                                        ].fileName +
                                                        ']'
                                                      );
                                                    default:
                                                      return '';
                                                  }
                                                } else {
                                                  return '';
                                                }
                                              })()}
                                            </div>
                                          </div>
                                        </div>
                                      </Checkbox>
                                    </div>
                                  </div>
                                );
                              })}
                          </InfiniteScroll>
                        </Checkbox.Group>
                      ) : //机器人
                      +checkValue === 3 &&
                        robotListParent &&
                        robotListParent.length > 0 ? (
                        <InfiniteScroll
                          height={'calc(100vh - 220px)'}
                          dataLength={robotListParent?.length}
                          style={{
                            width: '100%',
                          }}
                          next={() => fetchMoreDataRobot()}
                          hasMore={hasMore}
                          scrollableTarget={'contentBoxSon'}
                          loader={
                            <p
                              style={{
                                textAlign: 'center',
                                color: '#3463FC',
                                fontSize: 12,
                              }}
                            >
                              <b>
                                <FormattedMessage
                                  id="definition.synonyms.upload.tips"
                                  defaultMessage="上滑加载中..."
                                />
                              </b>
                            </p>
                          }
                          // 加载结束提示信息
                          endMessage={
                            <p
                              style={{
                                textAlign: 'center',
                                color: '#999',
                                fontSize: 12,
                              }}
                            >
                              <b>
                                <FormattedMessage
                                  id="definition.synonyms.upload.nodata.tips"
                                  defaultMessage="没有更多了~"
                                />
                              </b>
                            </p>
                          }
                        >
                          {robotListParent &&
                            robotListParent.length > 0 &&
                            robotListParent?.map(item => {
                              return (
                                <div className={styles.contentBoxTabList}>
                                  {/* <Checkbox
                                    value={item.ticketId}
                                    style={{ marginRight: 5,display:'flex',alignItems:'center' }}
                                  > */}
                                  <div
                                    className={styles.contentBoxTabListItem}
                                    style={{
                                      borderRadius:
                                        item?.selected === true ? 4 : '',
                                      background:
                                        item?.selected === true
                                          ? 'linear-gradient(180deg, #C2DFFC 0%, #C7D0FB 100%)'
                                          : '',
                                    }}
                                    onClick={() =>
                                      handleCurrentSessionRobot(item)
                                    }
                                  >
                                    <div
                                      className={
                                        styles.contentBoxTabListItemImg
                                      }
                                    >
                                      {/* <img src={Avatar} /> */}
                                      <div
                                        className={styles.headAva}
                                        style={{
                                          backgroundColor: item.customerAvatorColor
                                            ? item.customerAvatorColor
                                            : '#3463FC',
                                        }}
                                      >
                                        <span>
                                          {item?.username?.substr(0, 1)}
                                        </span>
                                      </div>
                                      {item?.unReadCount ? (
                                        <span className={styles.noReadNum}>
                                          {item?.unReadCount}
                                        </span>
                                      ) : null}
                                    </div>
                                    <div
                                      className={
                                        styles.contentBoxTabListItemFlex
                                      }
                                    >
                                      <div
                                        className={
                                          styles.contentBoxTabListItemFlexTop
                                        }
                                      >
                                        <div
                                          className={
                                            styles.contentBoxTabListItemFlexFront
                                          }
                                        >
                                          <span
                                            className={
                                              styles.contentBoxTabListItemFlexname
                                            }
                                            style={{
                                              maxWidth: `${leftWidth - 200}px`,
                                            }}
                                            title={item.username}
                                          >
                                            {item.username}
                                          </span>

                                          {item?.originalStatus ? (
                                            <div
                                              className={
                                                styles.contentBoxTabListItemFlexZhuan
                                              }
                                            >
                                              转
                                            </div>
                                          ) : null}
                                        </div>
                                        <span
                                          className={
                                            styles.contentBoxTabListItemFlexTime
                                          }
                                        >
                                          <span
                                            style={{
                                              marginLeft: 5,
                                              float: 'right',
                                            }}
                                            className={styles.listImg}
                                          >
                                            {(() => {
                                              switch (item?.channelId) {
                                                case '1':
                                                  return (
                                                    <img src={EmailIcon} />
                                                  );
                                                case '3':
                                                  return (
                                                    <img src={FacebookIcon} />
                                                  );
                                                case '4':
                                                  return (
                                                    <img src={WhatsAppIcon} />
                                                  );
                                                case '5':
                                                  return (
                                                    <img src={TwitterIcon} />
                                                  );
                                                case '6':
                                                  return <img src={LineIcon} />;
                                                case '7':
                                                  return (
                                                    <img src={PhoneIcon} />
                                                  );
                                                case '8':
                                                  return <img src={ChatIcon} />;
                                                case '9':
                                                  return (
                                                    <img
                                                      src={AppChatOutlinedIcon}
                                                    />
                                                  );
                                                case '10':
                                                  return (
                                                    <img
                                                      src={WebVideoOutlinedIcon}
                                                    />
                                                  );
                                                case '11':
                                                  return (
                                                    <img
                                                      src={AppVideoOutlinedIcon}
                                                    />
                                                  );
                                                case '12':
                                                  return (
                                                    <img src={AwsChannelIcon} />
                                                  );
                                                case '13':
                                                  return (
                                                    <img
                                                      src={NewInstagramIcon}
                                                    />
                                                  );
                                                case '14':
                                                  return (
                                                    <img src={NewLineIcon} />
                                                  );
                                                case '15':
                                                  return (
                                                    <img src={NewWeComIcon} />
                                                  );
                                                case '16':
                                                  return (
                                                    <img
                                                      src={
                                                        NewWechatOfficialAccountIcon
                                                      }
                                                    />
                                                  );
                                                case '17':
                                                  return (
                                                    <img
                                                      src={
                                                        NewWebOnlineVoiceIcon
                                                      }
                                                    />
                                                  );
                                                case '18':
                                                  return (
                                                    <img
                                                      src={
                                                        NewAppOnlineVoiceIcon
                                                      }
                                                    />
                                                  );

                                                case '19':
                                                  return (
                                                    <img src={NewTwitterIcon} />
                                                  );
                                                case '20':
                                                  return (
                                                    <img
                                                      src={NewTelegramIcon}
                                                    />
                                                  );
                                                case '21':
                                                  return (
                                                    <img
                                                      src={
                                                        NewWeChatMiniProgramIcon
                                                      }
                                                    />
                                                  );
                                                case '22':
                                                  return (
                                                    <img src={NewShopifyIcon} />
                                                  );
                                                case '23':
                                                  return (
                                                    <img
                                                      src={NewGooglePlayIcon}
                                                    />
                                                  );
                                              }
                                            })()}
                                          </span>
                                          <span
                                            style={{
                                              color: '#999',
                                              float: 'right',
                                            }}
                                          >
                                            {item?.messageList &&
                                              item?.messageList.length > 0 &&
                                              displayDateTime(
                                                item?.messageList[
                                                  item?.messageList?.length - 1
                                                ]?.time,
                                              )}
                                          </span>
                                        </span>
                                      </div>
                                      <div
                                        className={
                                          styles.contentBoxTabListItemFlexBottom
                                        }
                                      >
                                        {/* 回复内容类型 1、文本 2、图片 3、视频 4、附件 5、音频 */}
                                        {(() => {
                                          if (
                                            item?.messageList &&
                                            item?.messageList.length > 0
                                          ) {
                                            switch (
                                              item?.messageList[
                                                item?.messageList?.length - 1
                                              ]?.contentType
                                            ) {
                                              case 1:
                                              case 'text':
                                              case 'text/pain':
                                              case 'markdown':
                                                let content =
                                                  item?.messageList[
                                                    item?.messageList?.length -
                                                      1
                                                  ].content;
                                                let replyType =
                                                  item?.messageList[
                                                    item?.messageList?.length -
                                                      1
                                                  ].type;
                                                let dataStatus =
                                                  item?.messageList[
                                                    item?.messageList?.length -
                                                      1
                                                  ].dataStatus;

                                                return dataStatus === '999' ? (
                                                  getIntl().formatMessage({
                                                    id:
                                                      'workerOffers.withdraw.message.tips',
                                                    defaultMessage:
                                                      '您已经撤回一条消息',
                                                  })
                                                ) : isValidJson(
                                                    content,
                                                    item,
                                                  ) ? (
                                                  replyType === 'aiAgent' ||
                                                  replyType === 7 ? (
                                                    <div>
                                                      [
                                                      <FormattedMessage id="work.order.detail.customer.entry.intention.reason.1" />
                                                      ]
                                                    </div>
                                                  ) : (
                                                    <div>
                                                      [
                                                      <FormattedMessage id="work.order.detail.ai.intelligence.summary" />
                                                      ]
                                                    </div>
                                                  )
                                                ) : content?.includes(
                                                    '![](',
                                                  ) ? (
                                                  `[${getIntl().formatMessage({
                                                    id:
                                                      'new.worktable.email.editor.image',
                                                    defaultValue: '图片',
                                                  })}]${
                                                    content.split('\n\n')[1]
                                                  }`
                                                ) : (
                                                  content
                                                );
                                              case 'textimage':
                                              case 'image':
                                              case 2:
                                                return (
                                                  <div>
                                                    [
                                                    <FormattedMessage
                                                      id="knowledge.QA.label.2"
                                                      defaultMessage="图片"
                                                    />
                                                    ]
                                                  </div>
                                                );
                                              case 'video':
                                              case 'music':
                                              case 3:
                                              case 5:
                                                return (
                                                  <div>
                                                    [
                                                    <FormattedMessage
                                                      id="knowledge.QA.label.3"
                                                      defaultMessage="视频"
                                                    />
                                                    ]
                                                  </div>
                                                );
                                              case 'link':
                                              case 4:
                                                return (
                                                  '[' +
                                                  item?.messageList[
                                                    item?.messageList?.length -
                                                      1
                                                  ].fileName +
                                                  ']'
                                                );
                                              default:
                                                return '';
                                            }
                                          } else {
                                            return '';
                                          }
                                        })()}
                                      </div>
                                    </div>
                                  </div>
                                  {/* </Checkbox> */}
                                </div>
                              );
                            })}
                        </InfiniteScroll>
                      ) : (
                        // <Empty style={{ marginTop: '50%' }} />
                        <div className={styles.noDataImg}>
                          <img src={NoDataImg} />
                          <p>
                            <FormattedMessage
                              id="work.order.reply.no.data"
                              defaultMessage="暂无数据"
                            />
                          </p>
                        </div>
                      )}
                    </div>
                    {/* 领取工单按钮 */}
                    {handledPendingList && handledPendingList.length > 0 ? (
                      ticketType === 2 && +checkValue === 2 ? (
                        <div
                          className={styles.collectTicket}
                          onClick={handleCollectTicket}
                        >
                          <div className={styles.ticketBox}>
                            {Ticket()}
                            <span>
                              <FormattedMessage
                                id="work.order.collect.ticket"
                                defaultMessage="领取工单"
                              />
                            </span>
                          </div>
                        </div>
                      ) : ticketType === 3 && +checkValue === 2 ? (
                        <HOCAuth authKey={'allocation_ticket'}>
                          {authAccess => (
                            <div
                              className={`${
                                styles.collectTicket
                              } ${authAccess && 'disabled'}`}
                              onClick={() => allocateTicket()}
                            >
                              <div className={styles.ticketBox}>
                                {Fenpei()}
                                <span>
                                  <FormattedMessage
                                    id="work.order.allocation.ticket"
                                    defaultMessage="分配工单"
                                  />
                                </span>
                              </div>
                            </div>
                          )}
                        </HOCAuth>
                      ) : (
                        ''
                      )
                    ) : (
                      ''
                    )}
                  </div>
                ),
              },
              // *****************************************************邮件*****************************************************
              user.agentAccessChannel?.search('2') != -1 && {
                label: (
                  <span
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      fontSize: +workTableTabValue === 3 ? 14 : 12,
                    }}
                  >
                    {workTableTabValue === 3 ? EmailSvg() : EmailNormalSvg()}
                    <FormattedMessage id="marketing.channel.type.email"></FormattedMessage>
                    ({emailTicketNum})
                  </span>
                ),
                key: 3,
                children: (
                  <div className={styles.contentBoxTab}>
                    <div className={styles.contentBoxTabTop}>
                      {/*<Radio.Group*/}
                      {/*  block*/}
                      {/*  options={options}*/}
                      {/*  defaultValue="1"*/}
                      {/*  optionType="button"*/}
                      {/*  buttonStyle="solid"*/}
                      {/*/>*/}
                      <div className={styles.formDiv}>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: '100%',
                          }}
                        >
                          <Input
                            value={search ? search : null}
                            placeholder={getIntl().formatMessage({
                              id: 'document.knowledge.base.input.tips',
                            })}
                            onChange={e => searchChange(e)}
                            onPressEnter={() => searchPressEnter()}
                            prefix={<Search />}
                          />
                          <div className={styles.featuresDiv}>
                            <Dropdown
                              menu={{
                                items: dropdownItems,
                                onClick: onDropdown,
                                defaultSelectedKeys: [1],
                              }}
                            >
                              <Button
                                style={{
                                  backgroundColor: isHovered ? '#3463fc' : '', // 动态背景颜色
                                  marginLeft: '4px',
                                }}
                                onMouseEnter={() => setIsHovered(true)}
                                onMouseLeave={() => setIsHovered(false)}
                                icon={isHovered ? SortSvgChecked() : SortSvg()}
                              ></Button>
                            </Dropdown>

                            {/* {ChooseOrderIcon()} */}
                          </div>
                          {formHide ? (
                            <Button
                              onClick={() => showForm()}
                              type="text"
                              style={{
                                backgroundColor: isHoveredDown ? '#fff' : '', // 动态背景颜色
                                marginLeft: '4px',
                              }}
                              onMouseEnter={() => setIsHoveredDown(true)}
                              onMouseLeave={() => setIsHoveredDown(false)}
                              icon={
                                isHoveredDown ? DownIconChecked() : DownIcon()
                              }
                            ></Button>
                          ) : (
                            <Button
                              onClick={() => showForm()}
                              type="text"
                              style={{
                                backgroundColor: isHoveredUp ? '#fff' : '', // 动态背景颜色
                                marginLeft: '4px',
                              }}
                              onMouseEnter={() => setIsHoveredUp(true)}
                              onMouseLeave={() => setIsHoveredUp(false)}
                              icon={isHoveredUp ? UpIconChecked() : UpIcon()}
                            ></Button>
                          )}
                        </div>

                        {formHide ? null : (
                          <div>
                            {/* <Select
                            placeholder={getIntl().formatMessage({
                              id: 'new.worktable.chatList.hide.label',
                              defaultValue: '请选择客户标签',
                            })}
                            mode="multiple"
                            allowClear
                            showArrow
                            showSearch
                            // filterOption={(inputValue, option) =>
                            //   option.name
                            //     .toLowerCase()
                            //     .indexOf(inputValue.toLowerCase()) >= 0
                            // }
                            // fieldNames={{
                            //   label: 'name',
                            //   value: 'channelId',
                            //   key: 'channelId',
                            // }}
                            // options={channelOptions}
                            onChange={value => handleAddSelectChange(value)}
                          /> */}
                            <Select
                              value={
                                workRecordTypeCode ? workRecordTypeCode : null
                              }
                              showSearch
                              placeholder={getIntl().formatMessage({
                                id:
                                  'create.work.order.work.order.type.required',
                                defaultValue: '请选择工单类型',
                              })}
                              allowClear
                              fieldNames={{
                                label: 'workRecordTypeName',
                                value: 'workRecordTypeId',
                                key: 'workRecordTypeId',
                              }}
                              filterOption={(inputValue, option) =>
                                option.workRecordTypeName
                                  .toLowerCase()
                                  .indexOf(inputValue.toLowerCase()) >= 0
                              }
                              options={workRecordTypeList}
                              onChange={value =>
                                handleChangeSelectWorker(value)
                              }
                            />
                            <RangePicker
                              value={rangeValue}
                              showTime={{ format: 'HH:mm:ss' }}
                              format="YYYY-MM-DD HH:mm:ss"
                              onChange={(value, dateString) => {
                                onRangePickerChange(value, dateString);
                              }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                    <div
                      className={styles.contentBoxTabBottom}
                      id={'contentBoxSon'}
                    >
                      {emailTicketList && emailTicketList.length > 0 ? (
                        <InfiniteScroll
                          height={'calc(100vh - 220px)'}
                          dataLength={emailTicketList?.length}
                          style={{
                            width: '100%',
                          }}
                          next={() => fetchMoreData1()}
                          hasMore={hasMore}
                          scrollableTarget={'contentBoxSon'}
                          loader={
                            <p
                              style={{
                                textAlign: 'center',
                                color: '#3463FC',
                                fontSize: 12,
                              }}
                            >
                              <b>
                                <FormattedMessage
                                  id="definition.synonyms.upload.tips"
                                  defaultMessage="上滑加载中..."
                                />
                              </b>
                            </p>
                          }
                          // 加载结束提示信息
                          endMessage={
                            <p
                              style={{
                                textAlign: 'center',
                                color: '#999',
                                fontSize: 12,
                              }}
                            >
                              <b>
                                <FormattedMessage
                                  id="definition.synonyms.upload.nodata.tips"
                                  defaultMessage="没有更多了~"
                                />
                              </b>
                            </p>
                          }
                        >
                          <Checkbox.Group
                            style={{ width: '100%' }}
                            // value={checkedList}
                            // onChange={onChangeGroup}
                          >
                            {emailTicketList &&
                              emailTicketList.length > 0 &&
                              emailTicketList?.map(item => {
                                return (
                                  <div className={styles.contentBoxTabList}>
                                    <div
                                      className={styles.contentBoxTabListItem}
                                      style={{
                                        borderRadius:
                                          item.workRecordId ===
                                          emailSelectTicketData.workRecordId
                                            ? 4
                                            : '',
                                        background:
                                          item.workRecordId ===
                                          emailSelectTicketData.workRecordId
                                            ? 'linear-gradient(180deg, #C2DFFC 0%, #C7D0FB 100%)'
                                            : '',
                                      }}
                                      onClick={() =>
                                        handleCurrentSessionEmail(item)
                                      }
                                    >
                                      {/* <Checkbox
                                    style={{ marginRight: 5 }}
                                  ></Checkbox> */}
                                      <div
                                        className={
                                          styles.contentBoxTabListItemImg
                                        }
                                      >
                                        {/* < img src={Avatar} /> */}
                                        <div
                                          className={styles.headAva}
                                          style={{ backgroundColor: '#76E0C1' }}
                                        >
                                          {item.customerName
                                            ? item?.customerName?.substr(0, 1)
                                            : item?.customerTelephone?.substr(
                                                0,
                                                1,
                                              )}
                                          {/*<span>*/}
                                          {/*  */}
                                          {/*</span>*/}
                                        </div>

                                        {item?.unreadCount === -1 ? (
                                          <span
                                            className={styles.noReadNum}
                                          ></span>
                                        ) : item?.unreadCount ? (
                                          <span className={styles.noReadNum}>
                                            {item?.unreadCount}
                                          </span>
                                        ) : null}
                                      </div>
                                      <div
                                        className={
                                          styles.contentBoxTabListItemFlex
                                        }
                                      >
                                        <div
                                          className={
                                            styles.contentBoxTabListItemFlexTop
                                          }
                                        >
                                          <div
                                            className={
                                              styles.contentBoxTabListItemFlexFront
                                            }
                                          >
                                            <span
                                              className={
                                                styles.contentBoxTabListItemFlexname
                                              }
                                              style={{
                                                maxWidth: `calc(${leftWidth}px - 250%)`,
                                                minWidth: '75%',
                                              }}
                                              title={
                                                item.customerName
                                                  ? item.customerName
                                                  : item.customerTelephone
                                              }
                                            >
                                              {item.customerName
                                                ? item.customerName
                                                : item.customerTelephone}
                                            </span>

                                            {item.oldWorkRecordCode ? (
                                              <div
                                                className={
                                                  styles.contentBoxTabListItemFlexZhuan
                                                }
                                              >
                                                转
                                              </div>
                                            ) : null}
                                          </div>
                                          <span
                                            className={
                                              styles.contentBoxTabListItemFlexTime
                                            }
                                          >
                                            <span
                                              style={{
                                                marginLeft: 5,
                                                float: 'right',
                                              }}
                                              className={styles.listImg}
                                            >
                                              <img src={EmailIcon} />
                                            </span>
                                            <span
                                              style={{
                                                color: '#999',
                                                float: 'right',
                                              }}
                                            >
                                              {displayDateTime(
                                                item.ticketContentIndex[0]
                                                  ?.reply_time,
                                              )}
                                            </span>
                                          </span>
                                        </div>
                                        <div
                                          className={
                                            styles.contentBoxTabListItemFlexBottom
                                          }
                                          title={
                                            item.ticketContentIndex[0]
                                              ?.email_subject
                                          }
                                        >
                                          {
                                            item.ticketContentIndex[0]
                                              ?.email_subject
                                          }
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                          </Checkbox.Group>
                        </InfiniteScroll>
                      ) : (
                        // <Empty style={{ marginTop: '50%' }} />
                        <div className={styles.noDataImg}>
                          <img src={NoDataImg} />
                          <p>
                            <FormattedMessage
                              id="work.order.reply.no.data"
                              defaultMessage="暂无数据"
                            />
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ),
              },
              // *****************************************************设置*****************************************************
              {
                label: (
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    {+workTableTabValue === 4 ? SettingSvg() : UnSettingSvg()}
                    {
                      <span
                        style={{
                          color: +workTableTabValue === 4 ? '#3463FC' : '#333',
                          fontWeight: +workTableTabValue === 4 ? 700 : 400,
                          fontSize: +workTableTabValue === 4 ? '14px' : '12px',
                        }}
                      >
                        <FormattedMessage
                          id="connect.beginner.guide.title.2"
                          defaultMessage="设置"
                        />
                      </span>
                    }
                  </span>
                ),
                key: 4,
                children: <SettingComponents />,
              },
            ]}
          />
        </Spin>
        {/*分配工单*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'work.order.allocation.ticket',
            defaultValue: '分配工单',
          })}
          className="WorkOrderUpgrade3"
          footer={null}
          mask={false}
          open={isAllocateModal}
          closable={false}
          // onCancel={() => setAllocateModal(false)}
        >
          <Spin spinning={allocateLoading}>
            <Form
              name="basic"
              autoComplete="off"
              labelAlign="right"
              ref={formWorkOrderUpgradeRef}
              onFinish={onFinishWorkOrderUpgrade}
              initialValues={{
                agentId: [],
                teamId: [],
                ruleType: 0,
              }}
            >
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'work.order.detail.allocation.rules',
                      defaultValue: '分配规则：',
                    })}
                    name="ruleType"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <Radio.Group
                      onChange={onChangeRuleType}
                      // value={ruleTypeValue}
                    >
                      <Radio value={0}>
                        <FormattedMessage
                          id="ticket.assign.specific.team"
                          defaultMessage="分配给特定团队"
                        />
                      </Radio>
                      <Radio value={1}>
                        <FormattedMessage
                          id="ticket.assign.specific.agent"
                          defaultMessage="分配给特定座席"
                        />
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
              {ruleTypeValue && ruleTypeValue === 1 ? (
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'work.order.detail.replacing.seats.allocate',
                        defaultValue: '分配客服：',
                      })}
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                      name="agentId"
                    >
                      <Select
                        showSearch
                        filterOption={(inputValue, option) =>
                          option.userName
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                        // fieldNames={{
                        //   label: 'userName',
                        //   value: 'userId',
                        //   key: 'userId',
                        // }}
                        // options={seatsUserList}
                        placeholder={getIntl().formatMessage({
                          id: 'ai.agent.nodes.ToolToAgent.select.agent.p',
                          defaultMessage: '请选择座席',
                        })}
                      >
                        {seatsUserList?.map(item => {
                          let disableStatus;

                          if (item.status) {
                            disableStatus = false;
                          } else {
                            disableStatus = true;
                          }
                          // if (disableStatus) {
                          //   return (
                          //     <Option
                          //       disabled={disableStatus}
                          //       value={item.userId}
                          //       key={item.userId}
                          //     >
                          //       <i className="disableLineCircle"></i>
                          //       {item.userName}
                          //     </Option>
                          //   );
                          // } else {
                          //   return (
                          //     <Option
                          //       disabled={disableStatus}
                          //       value={item.userId}
                          //       key={item.userId}
                          //     >
                          //       <i
                          //         className={
                          //           item.status
                          //             ? 'onLineCircle'
                          //             : 'offLineCircle'
                          //         }
                          //       ></i>
                          //       {item.userName}
                          //     </Option>
                          //   );
                          // }
                          return (
                            <Option
                              disabled={disableStatus}
                              value={item.userId}
                              key={item.userId}
                              className={styles.seatOptionClass}
                            >
                              {/* <i
                                  className={
                                    item.status
                                      ? 'onLineCircle'
                                      : 'offLineCircle'
                                  }
                                ></i> */}
                              {item.userName}
                              <span
                                style={{
                                  float: 'right',
                                  color: disableStatus ? '#999' : '#00B900',
                                }}
                              >
                                {item.statusName}
                              </span>
                            </Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              ) : (
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'work.order.detail.replacing.team.allocate',
                        defaultValue: '分配团队：',
                      })}
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                      name="teamId"
                    >
                      <Select
                        showSearch
                        filterOption={(inputValue, option) =>
                          option.userName
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                        placeholder={getIntl().formatMessage({
                          id: 'ai.agent.nodes.ToolToAgent.select.team.p',
                          defaultMessage: '请选择团队',
                        })}
                        // fieldNames={{
                        //   label: 'deptName',
                        //   value: 'deptId',
                        //   key: 'deptId',
                        // }}
                        // options={callDeptList}
                      >
                        {callDeptList?.map(item => {
                          let onlineUserCount = item.onlineUserCount;
                          return (
                            <Option
                              disabled={onlineUserCount > 0 ? false : true}
                              value={item.deptId}
                              key={item.deptId}
                            >
                              {item.deptName}
                              {getIntl().formatMessage(
                                {
                                  id: 'work.order.detail.transfer.ticket.dept',
                                },
                                { onlineUserCount: onlineUserCount },
                              )}
                            </Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'work.order.detail.reason',
                      defaultValue: '原因：',
                    })}
                    name="operationLogReason"
                    rules={[
                      {
                        required: true,
                        message: getIntl().formatMessage({
                          id: 'work.order.detail.reason.placeholder',
                          defaultValue: '请输入原因',
                        }),
                      },
                      {
                        max: 2000,
                        message: (
                          <FormattedMessage
                            id="customerInformation.add.basicInformation.maxlength3"
                            defaultValue="长度不能超过2000个字符"
                          />
                        ),
                      },
                    ]}
                  >
                    <TextArea
                      style={{ width: '100%', whiteSpace: 'pre-wrap' }}
                      autoSize={{
                        minRows: 3,
                        maxRows: 3,
                      }}
                      // showCount
                      maxLength={2000}
                      name={'operationLogReason'}
                      placeholder={getIntl().formatMessage({
                        id: 'work.order.detail.reason.placeholder',
                        defaultValue: '请输入原因',
                      })}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24} style={{ textAlign: 'center' }}>
                <Col span={24}>
                  <Form.Item>
                    <Button onClick={() => setAllocateModal(false)}>
                      <FormattedMessage
                        id="work.order.management.btn.cancel"
                        defaultMessage="取消"
                      />
                    </Button>
                    <Button
                      type="primary"
                      htmlType={'submit'}
                      loading={loadingModal}
                    >
                      <FormattedMessage
                        id="work.order.management.btn.sure"
                        defaultMessage="确定"
                      />
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Spin>
        </Modal>
        {/*批量回复*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'comment.feedback.batch.reply.modal',
            defaultValue: '批量回复',
          })}
          className="WorkOrderUpgrade4"
          footer={null}
          mask={false}
          open={isBathReplyModal}
          closable={false}
          // onCancel={() => setAllocateModal(false)}
        >
          <Row gutter={24} style={{ textAlign: 'center' }}>
            <Col span={24}>
              <div className={styles.chatInputContainer}>
                <textarea
                  className={styles.chatTextarea}
                  placeholder={getIntl().formatMessage({
                    id: 'comment.feedback.replay.content.placeholder',
                    defaultMessage: '请输入您的内容',
                  })}
                  value={inputMessage || ''}
                  onChange={e => setInputMessage(e.target.value)}
                  onKeyPress={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />
                <div className={styles.chatToolbox}>
                  <span
                    className={styles.emojiIcon}
                    onClick={() => setShowEmoji(!showEmoji)}
                    ref={emojiTriggerRef}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 16 16"
                      fill="none"
                    >
                      <path
                        d="M13.6377 11.8124H13.6387V11.8066L13.6377 11.8124Z"
                        fill="#999999"
                      />
                      <path
                        d="M12.614 3.38636C10.0701 0.842038 5.93063 0.842038 3.38676 3.38636C0.842435 5.93022 0.842435 10.0697 3.38676 12.6136C5.93063 15.1579 10.0702 15.1579 12.614 12.6136C15.1583 10.0697 15.1583 5.93022 12.614 3.38636ZM11.9518 11.9519C9.773 14.1307 6.2273 14.1302 4.04873 11.9519C1.86967 9.77261 1.86967 6.22759 4.04873 4.04833C6.2273 1.86974 9.77302 1.86926 11.9518 4.04833C14.1306 6.2269 14.1306 9.77307 11.9518 11.9519Z"
                        fill="#999999"
                      />
                      <path
                        d="M5.96407 7.31284C6.35131 7.31284 6.66648 6.9977 6.66648 6.61023C6.66648 6.22229 6.35131 5.90668 5.96407 5.90668C5.5759 5.90668 5.26074 6.22229 5.26074 6.61023C5.26074 6.9977 5.5759 7.31284 5.96407 7.31284Z"
                        fill="#999999"
                      />
                      <path
                        d="M10.011 7.31398C10.3987 7.31398 10.7143 6.99815 10.7143 6.61067C10.7143 6.22274 10.3987 5.90759 10.011 5.90759C9.62372 5.90759 9.30859 6.22274 9.30859 6.61067C9.30859 6.99815 9.62372 7.31398 10.011 7.31398Z"
                        fill="#999999"
                      />
                      <path
                        d="M10.0349 9.67278L9.99661 9.68525C9.36676 9.97038 8.69116 10.1155 7.99017 10.1155C7.26606 10.1155 6.58121 9.96898 5.95415 9.68111L5.91625 9.66952C5.89268 9.66306 5.8608 9.65383 5.82105 9.65383C5.59047 9.65383 5.40332 9.84098 5.40332 10.0706C5.40332 10.1797 5.45461 10.2892 5.53918 10.3715L5.5341 10.3969L5.64084 10.4449C6.38945 10.7837 7.20367 10.962 7.99524 10.962C8.80162 10.962 9.59181 10.7883 10.3543 10.4408C10.4883 10.3673 10.5719 10.2264 10.5719 10.0734C10.5719 9.80403 10.3284 9.58266 10.0349 9.67278Z"
                        fill="#999999"
                      />
                    </svg>
                  </span>
                </div>
                {showEmoji && (
                  <div
                    className={styles.emojiPickerContainer}
                    ref={emojiPickerRef}
                  >
                    <Picker
                      data={data}
                      onEmojiSelect={emoji => {
                        const textArea = document.querySelector(
                          `.${styles.chatTextarea}`,
                        );
                        if (textArea) {
                          const start = textArea.selectionStart;
                          const end = textArea.selectionEnd;
                          const newValue =
                            inputMessage.substring(0, start) +
                            emoji.native +
                            inputMessage.substring(end);
                          setInputMessage(newValue);

                          // 更新光标位置
                          setTimeout(() => {
                            textArea.focus();
                            textArea.setSelectionRange(
                              start + emoji.native.length,
                              start + emoji.native.length,
                            );
                          }, 0);
                        }
                      }}
                      emojiButtonSize={24}
                      theme={'light'}
                      navPosition={'none'}
                      skinTonePosition={'none'}
                      searchPosition={'none'}
                      previewPosition={'none'}
                      emojiButtonRadius={'4px'}
                      emojiSize={'16'}
                      maxFrequentRows={0}
                      perLine={10}
                    />
                  </div>
                )}
              </div>
            </Col>
          </Row>
          <Row gutter={24} style={{ textAlign: 'center' }}>
            <Col span={24}>
              <Form.Item>
                <Button
                  onClick={() => {
                    setInputMessage('');
                    setIsBathReplyModal(false);
                  }}
                >
                  <FormattedMessage
                    id="work.order.management.btn.cancel"
                    defaultMessage="取消"
                  />
                </Button>
                <Button
                  type="primary"
                  loading={loadingModal}
                  onClick={() => handleSendMessage()}
                >
                  <FormattedMessage
                    id="work.order.management.btn.replay"
                    defaultMessage="回复"
                  />
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Modal>
        <div
          className={`${styles.resizer} ${styles.right}`}
          onMouseDown={e => {
            e.preventDefault();
            document.addEventListener('mousemove', handleLeftResize);
            document.addEventListener(
              'mouseup',
              () => {
                document.removeEventListener('mousemove', handleLeftResize);
              },
              { once: true },
            );
          }}
        />
      </div>
    );
  },
);

export default ChatList;
