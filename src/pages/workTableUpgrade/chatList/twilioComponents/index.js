import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from 'react';
import useMediaRecorder from '@/hooks/useMediaRecorder';
import { notifyMe } from '@/utils/utils';
import styles from './index.less';

import moment from 'moment';
import {
  Input,
  Button,
  Tooltip,
  notification,
  Select,
  Spin,
  Space,
  Drawer,
  Alert,
  Modal,
  Switch,
} from 'antd';
import * as Flags from 'react-flags-select';
import 'amazon-connect-streams';
import 'amazon-connect-chatjs';
import {
  CallIcon,
  CallTimeIcon,
  CallTimeSmallIcon,
  CallTimeSmallBaiIcon,
  CallEndTimeIcon,
  CallCloseIcon,
  CallConnectFastIcon,
  CallSliIcon,
  CallSlinoIcon,
  CallKeyBoardIcon,
  CallKeepIcon,
  CallPlayIcon,
  DrawerCloseIcon,
  InputCloseIcon,
  QuickConnectBtn,
  MultipleEndBtn,
} from '../icon.js';
import agent from '@/assets/agent.svg';
import { Device } from '@twilio/voice-sdk';
import ccpErrorImg from '@/assets/ccpError.png';
import { CloseSquareFilled, ExclamationCircleFilled } from '@ant-design/icons';
import {
  parsePhoneNumber,
  isValidNumber,
  parsePhoneNumberFromString,
} from 'libphonenumber-js';
import {
  useDispatch,
  useSelector,
  getIntl,
  history,
  FormattedMessage,
} from 'umi';
const { confirm } = Modal;

const TwilioComponents = forwardRef(({ getPhoneCurrentInfo }, ref) => {
  const dispatch = useDispatch();

  const {
    user,
    websocketStatus,
    agentStatus,
    workTableSettingTranslationPhoneSpeakers,
  } = useSelector(({ layouts, worktable }) => ({
    user: layouts.user,
    websocketStatus: layouts.websocketStatus,
    agentStatus: layouts.agentStatus,
    workTableSettingTranslationPhoneSpeakers:
      worktable.workTableSettingTranslationPhoneSpeakers,
  }));

  const [token, setToken] = useState('');
  const [audioInfoCurrent, setAudioInfoCurrent] = useState(null);
  /**
   * 通话列表
   * connectId  实例Id 唯一
   * ticketId  工单id 唯一
   * keyNumberArea 区号
   * keyNumber  电话号码
   * timeKeeping 通话时间
   * role  身份
   * callingStatus 通话中的状态
   * connectionId 每建立一个联系人，产生的对应id
   *每次连接，不管是多人还是单人，connectId，ticketId，agent都是唯一的
   connectionId不是唯一的，每一个connectionId代表两个端点间的对话状态（保持联系，静音之类的）
   */
  let [callList, setCallList] = useState({
    connectId: '',
    ticketId: '',
    connectionId: '',
    agentId: '', //坐席id
    customerLanguage: '', //客户语言偏好
    robotWorkRecordId: '', //机器人工单id
    agentWorkRecordId: '', //人工工单id
    enterACW: '', //是否进入acw，当终端用户挂断时，前端可以直接进入acw
    systemPhone: '', //呼入呼出时，使用的系统电话
    incomingOutgoing: '', //呼出呼入
    misscallSecondsEnter: '', //未接通时长MissCall事件
    userList: [
      {
        callSid: '',
        accountSid: '',
        keyNumberArea: '',
        keyNumber: '',
        timeKeeping: 0,
        role: '',
        callingStatus: [], //结合CallingStatus状态
        status: '', //结合CallingShow状态
        name: '',
      },
    ],
  });
  let [keyboardValue, setKeyboardValue] = useState('');
  let [keyNumberArea, setKeyNumberArea] = useState(''); //区号
  let [keyNumber, setKeyNumber] = useState(''); //电话号码
  //通话状态下是否保持中或者静音
  //keep 保持
  //sil 静音
  //connect 快速连接
  let [callingStatus, setCallingStatus] = useState([]);
  let [timeKeeping, setTimeKeeping] = useState(0); ///通话时间
  //多种状态控制界面各种展示及逻辑
  // initial  初始状态
  //callACW   挂断状态
  //callOut   座席呼出状态
  //callIn   客户呼入状态
  //calling   通话中
  //multiple 多方通话
  //videoCallin 视频来电
  //videoCalling 视频通话中
  //videoACW   video挂断状态
  let [callingShow, setCallingShow] = useState('');
  let [drawerOpen, setDrawerOpen] = useState(false);
  let [connectList, setConnectList] = useState([]); //实例下拉
  let [selectedConnect, setSelectedConnect] = useState(''); //实例下拉
  let [loginUrl, setLoginUrl] = useState(''); //sso登录url
  let [ccpLoading, setCcpLoading] = useState(false);
  let [quickConnectLoading, setQuickConnectLoading] = useState(false);
  let [connectUserList, setConnectUserList] = useState([]); //快速连接用户列表
  let [filteredData, setFilteredData] = useState([]); //模糊搜索后的快速连接列表的用户
  let [regionTimezones, setRegionTimezones] = useState([]); //国家前缀
  let [videoRTCCapabilities, setVideoRTCCapabilities] = useState(false); //是否web聊天语音
  let [videoAttributes, setVideoAttributes] = useState(null); //web聊天语音暴露的对象
  let [ccpError, setCcpError] = useState(false); //电话ccp是否产生错误
  let [currentQCUser, setCurrentQCUser] = useState(null); //快速连接列表中选中的用户
  let [chatPhone, setChatPhone] = useState(''); //从聊天渠道转过来的电话号码
  let [chatPhoneType, setChatPhoneType] = useState(false); //从聊天渠道转过来的电话号码
  let [alertShow, setAlertShow] = useState(false); //报错公告
  let [alertText, setAlertText] = useState(''); //报错信息
  let [ticketInfo, setTicketInfo] = useState(null); //工单信息 传递父组件
  let [allowReload, setAllowReload] = useState(false); //工单信息 传递父组件
  let [callLogsExist, setCallLogsExist] = useState(false); //刷新前是否在通话中
  let [queues, setQueues] = useState(null); //坐席队列信息
  let [keepInterval, setKeepInterval] = useState(0); //全局的计数器，用于addPhoneContactDetail计算保持通话时间，单位秒
  let [missCallTimer, setMissCallTimer] = useState(null); //未接通计时器ID
  const downKeyAudio = new Audio(
    `https://${process.env.DOMAIN_NAME_OVER}/static-icon/prompt_sound/ui-sound-1.wav`,
  );
  //实例化twilio
  const [device, setDevice] = useState(null);
  let [callObj, setCallObj] = useState({}); //呼出电话对象
  let [controls, setControls] = useState({}); //录音控制

  /*****************************************************************录音hooks使用 ************************************************************/
  let [stereoRecording, setStereoRecording] = useState(true); //是否启用双声道录音
  // 录音片段处理回调函数
  const handleRecordingChunk = async (blob, audioInfo, isEnd) => {
    console.log('收到录音片段:', blob, audioInfo, '是否结束:', isEnd);
    // 显示录音信息
    if (audioInfo) {
      console.log(
        `录音信息 - 大小: ${blob.size}字节, 时长: ${audioInfo.duration?.toFixed(
          2,
        )}秒, 声道: ${audioInfo.numberOfChannels}, 采样率: ${
          audioInfo.sampleRate
        }Hz`,
      );

      if (audioInfo.isStereo) {
        console.log(
          `双声道录音 - 左声道: ${audioInfo.leftChannel}, 右声道: ${audioInfo.rightChannel}`,
        );
      }

      if (audioInfo.nativeMode) {
        console.log('使用原生双声道录音模式');
      }
    }
    // 立即上传这个录音片段
    await uploadRecordingChunk(blob, audioInfo, isEnd);
  };

  const {
    startRecording,
    startStereoRecording,
    stopRecording,
    convertToWav,
  } = useMediaRecorder(handleRecordingChunk);
  /*****************************************************************录音hooks使用 ************************************************************/

  /**
   * 监听通话过程中按下键盘事件
   */
  useEffect(() => {
    if (!Device.isSupported) {
      setCcpError(true);
      setAlertShow(true);
      setAlertText(
        getIntl().formatMessage({
          id: 'im.twilio.components.device.not.supported',
        }),
      );
    }
    return () => {
      // 清理事件监听器
      window.removeEventListener('keydown', handleKeyDown);
      // 清理未接通计时器
      if (missCallTimer) {
        clearTimeout(missCallTimer);
      }
    };
  }, [missCallTimer]);
  /**********************************************************************处理实例id的value是JSON字符串问题**********************************************************************/
  useEffect(() => {
    //获取国旗列表
    systemRegionTimezones();
    //查询线路
    queryCurrentAgentLinkList();
  }, []);
  /**********************************************************************初始化**********************************************************************/
  useEffect(() => {
    // 添加错误处理逻辑，处理fetch调用失败的情况
    const fetchToken = async () => {
      try {
        console.log('twilio===fetchToken=======', selectedConnect);
        queryGenerateAgentToken();
      } catch (error) {
        console.error('获取 Twilio token 失败:', error);
        setCallingShow('initial');
      }
    };
    if (selectedConnect) {
      fetchToken();
    }
  }, [selectedConnect]);
  useEffect(() => {
    console.log('twilio===device=======', device);
    if (token && device) {
      getAcwStateTwilio();
    }
  }, [device]);
  useEffect(() => {
    console.log('twilio===callObj=======1', callObj, callObj?.status);
  }, [callObj]);
  useEffect(() => {
    console.log(
      'twilio===workTableSettingTranslationPhoneSpeakers=======1',
      workTableSettingTranslationPhoneSpeakers,
      device,
    );
    if (device) {
      device.audio.availableOutputDevices.forEach(function(_, id) {
        if (id === workTableSettingTranslationPhoneSpeakers) {
          console.log(
            'twilio===device.audio.availableOutputDevices=======yes1',
          );
          device.audio.speakerDevices.set(id);
          device.audio.ringtoneDevices.set(id);
        }
      });
    }
  }, [device, workTableSettingTranslationPhoneSpeakers]);
  //查询线路
  const queryCurrentAgentLinkList = () => {
    setCcpLoading(true);
    dispatch({
      type: 'worktable/queryCurrentAgentLinkList',
      callback: response => {
        setCcpLoading(false);
        if (response.code == 200) {
          let data = response?.data;
          let connectListTemp = [];
          data.forEach(item => {
            connectListTemp.push({
              label: item.alias,
              value: item.connectLineId,
            });
          });
          setConnectList(connectListTemp);
          setSelectedConnect({
            connectId: response?.data[0]?.connectLineId,
            connectAlias: response?.data[0]?.alias,
          });
          sessionStorage.setItem(
            'twilioConnectAlias',
            response?.data[0]?.alias,
          );
        } else {
          notification.error({
            message: response.message,
          });
        }
      },
    });
  };
  //初始化
  const intitializeDevice = () => {
    console.log('twilio===device=======', device);
    console.log('twilio===device.state=======', device?.state);
    //初始化面板
    if (
      callingShow !== 'callACW' &&
      callingShow !== 'videoACW' &&
      device.state
    ) {
      setCallingShow('initial');
    }

    //监听所有device事件
    addDeviceListeners(device);

    // 注册device，才能接收来电
    device.register();
  };
  /*
  ===========================================================与twilio非相关的，界面要使用的工具函数===========================================
  **/
  /*******************************更新通话列表数据*************************************/
  const handleCallList = (key, value) => {
    if (!key) {
      return;
    }
    setCallList(preState => {
      const callListCopy = { ...preState }; // 创建callList的副本
      if (key === 'userList') {
        const foundIndex = callListCopy.userList?.findIndex(
          item => item?.callSid === value[0],
        ); // 根据id查找对应的对象
        if (foundIndex != -1) {
          value[1]?.forEach(([key, value, flag]) => {
            //单独处理计时器，需要前值+1
            if (key === 'timeKeeping') {
              callListCopy.userList[foundIndex][key] =
                value === 0 ? 0 : callListCopy.userList[foundIndex][key] + 1;
            } else if (key === 'callingStatus') {
              //单独处理媒体状态，需要可以存储多个值
              let newStatusArray = [
                ...new Set(callListCopy.userList[foundIndex][key]),
              ];
              if (flag) {
                newStatusArray?.push(value);
              } else {
                let i = newStatusArray?.findIndex(item => value === item);
                if (i != -1) {
                  newStatusArray?.splice(i, 1);
                }
              }
              callListCopy.userList[foundIndex][key] = newStatusArray;
            } else {
              callListCopy.userList[foundIndex][key] = value; // 更新对应的 属性;
            }
          });
        }
      } else {
        callListCopy[key] = value;
      }
      return callListCopy;
    }); // 更新状态
  };
  /***************************** 更新通话中的状态*************************************/
  const handleCallStatus = (flag, value) => {
    setCallingStatus(preState => {
      const callStatusCopy = [...preState];
      if (flag) {
        callStatusCopy.push(value);
      } else {
        let i = callStatusCopy.findIndex(item => value === item);
        if (i != -1) {
          callStatusCopy.splice(i, 1);
        }
      }
      return callStatusCopy;
    });
  };
  /***************************** 按下键盘********************************************/
  const passKey = (index, value) => {
    console.log('twilio===passKey=======', index, value, callObj);
    //拨号功能
    if (index === 0) {
      playAudio();
      let key = callList.userList[index].keyNumber + value;
      handleCallList('userList', ['', [['keyNumber', key]]]);
    }
    //数字键盘，通话中按键事件
    if (index === 1) {
      playAudio();
      setKeyboardValue(pre => {
        return pre + value;
      });
      callObj.sendDigits(value, {
        success: function() {
          console.log('触发按键成功====', value);
        },
        failure: function(err) {
          console.log('触发按键失败====', value);
        },
      });
    }
  };
  const handleKeyDown = (event, conn) => {
    const validKeys = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '*',
      '#',
    ];

    if (validKeys.includes(event.key) && conn) {
      conn.sendDigits(event.key, {
        success: function() {
          console.log('触发按键成功====', event.key);
        },
        failure: function(err) {
          console.log('触发按键失败====', event.key);
        },
      });
    }
  };
  /***************************** 关闭报错公告*************************************/
  const onCloseAlert = () => {
    setAlertShow(false);
  };
  /***************************** 播放来电铃声*************************************/
  const playAudio = () => {
    // 重置音频
    downKeyAudio.currentTime = 0;
    downKeyAudio.play();
  };
  /***************************** 修改实例*************************************/
  const onChangeConncet = (value, item) => {
    handleCallList('connectId', value);
    console.log('twilio===onChangeConncet=======', value, item);
    setSelectedConnect({
      connectId: value,
      connectAlias: item?.label,
    });
    sessionStorage.setItem('twilioConnectAlias', item?.label);

    // 暂存到redux中
    dispatch({
      type: 'layouts/updateConnect',
      payload: item,
    });
    // 存到Redis中
    dispatch({
      type: 'layouts/saveSelectedConnect',
      payload: item,
    });
  };
  /***************************** 把电话号码绑定到最新的userList下*************************************/
  const handleKeyNumber = value => {
    handleCallList('userList', ['', [['keyNumber', value?.replace(/_/g, '')]]]);
  };
  /**
   * 回车事件
   */
  const onPressEnter = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      outBoundCall(
        callList.userList[callList.userList.length - 1].keyNumberArea +
          callList.userList[callList.userList.length - 1].keyNumber,
      );
    }
  };
  /*
  ===========================================================与twilio非相关的，界面要使用的工具函数（结束）===========================================
  **/
  /**********************************************************************监听所有device事件******************************************************************/
  const addDeviceListeners = useCallback(device => {
    //配置输入输出音频
    console.log(
      'twilio===device.audio.speakerDevices.get()=======',
      device.audio.speakerDevices.get(),
      device.audio.ringtoneDevices.get(),
      device.audio.availableOutputDevices,
      device.audio.availableInputDevices,
      localStorage.getItem('speaker_select_value'),
      localStorage.getItem('micro_phones_select_value'),
      device.audio.isOutputSelectionSupported,
    );
    setTimeout(() => {
      device.audio.availableOutputDevices.forEach(function(_, id) {
        if (id === localStorage.getItem('speaker_select_value')) {
          console.log(
            'twilio===device.audio.availableOutputDevices=======yes1',
          );
          device.audio.speakerDevices.set(id);
          device.audio.ringtoneDevices.set(id);
        }
      });
    }, 1000);

    device.on('incoming', call => handleIncoming(call));
    device.on('destroyed', val => {
      console.log('twilio===destroyed=======', device, val);
    });
    device.on('error', (twilioError, call) => {
      console.log('twilio===error======= ', twilioError, call);
    });
    device.on('registered', val => {
      console.log('twilio===registered======= ', val);
      console.log('twilio===device.edge=======', device.edge);
      console.log('twilio===device.home=======', device.home);
    });
    device.on('registering', val => {
      console.log('twilio===registering======= ', val);
    });
    device.on('tokenWillExpire', () => {
      console.log('twilio===tokenWillExpire======= ');
    });
    device.on('unregistered', val => {
      console.log('twilio===unregistered======= ', val);
    });
    device.on('tokenWillExpire', () => {
      queryGenerateAgentToken();
    });
  }, []);
  //获取token
  const queryGenerateAgentToken = () => {
    dispatch({
      type: 'worktable/queryGenerateAgentToken',
      callback: response => {
        if (response.code == 200) {
          setToken(response.data.token);
          let deviceNew = new Device(response.data.token, {
            logLevel: 1,
            codecPreferences: ['opus', 'pcmu'], // 编码器优先级，opus质量更好
          });
          setDevice(deviceNew);
          console.log(
            'twilio===selectedConnect=======',
            selectedConnect,
            selectedConnect.connectId,
          );
          //绑定到connectId
          handleCallList('connectId', selectedConnect.connectId);
        } else {
          notification.error({
            message: response.message,
          });
          setCcpError(true);
          setAlertShow(true);
          setAlertText(response.message);
        }
      },
    });
  };
  //获取国旗列表
  const systemRegionTimezones = () => {
    dispatch({
      type: 'worktable/systemRegionTimezones',
      // type: 'worktable/queryCompanyTelPrefix',
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          const countries = {
            AF: 'Afghanistan',
            AL: 'Albania',
            DZ: 'Algeria',
            AS: 'American Samoa',
            AD: 'Andorra',
            AO: 'Angola',
            AI: 'Anguilla',
            AG: 'Antigua and Barbuda',
            AR: 'Argentina',
            AM: 'Armenia',
            AW: 'Aruba',
            AU: 'Australia',
            AT: 'Austria',
            AZ: 'Azerbaijan',
            BS: 'Bahamas',
            BH: 'Bahrain',
            BD: 'Bangladesh',
            BB: 'Barbados',
            BY: 'Belarus',
            BE: 'Belgium',
            BZ: 'Belize',
            BJ: 'Benin',
            BM: 'Bermuda',
            BT: 'Bhutan',
            BO: 'Bolivia, Plurinational State of',
            BA: 'Bosnia and Herzegovina',
            BW: 'Botswana',
            BR: 'Brazil',
            IO: 'British Indian Ocean Territory',
            BG: 'Bulgaria',
            BF: 'Burkina Faso',
            BI: 'Burundi',
            KH: 'Cambodia',
            CM: 'Cameroon',
            CA: 'Canada',
            CV: 'Cape Verde',
            KY: 'Cayman Islands',
            CF: 'Central African Republic',
            TD: 'Chad',
            CL: 'Chile',
            CN: 'China',
            CO: 'Colombia',
            KM: 'Comoros',
            CG: 'Congo',
            CD: 'Democratic Republic of the Congo',
            CK: 'Cook Islands',
            CR: 'Costa Rica',
            CI: "Côte d'Ivoire",
            HR: 'Croatia',
            CU: 'Cuba',
            CW: 'Curaçao',
            CY: 'Cyprus',
            CZ: 'Czech Republic',
            DK: 'Denmark',
            DJ: 'Djibouti',
            DM: 'Dominica',
            DO: 'Dominican Republic',
            EC: 'Ecuador',
            EG: 'Egypt',
            SV: 'El Salvador',
            GQ: 'Equatorial Guinea',
            ER: 'Eritrea',
            EE: 'Estonia',
            ET: 'Ethiopia',
            FK: 'Falkland Islands (Malvinas)',
            FO: 'Faroe Islands',
            FJ: 'Fiji',
            FI: 'Finland',
            FR: 'France',
            PF: 'French Polynesia',
            GA: 'Gabon',
            GM: 'Gambia',
            GE: 'Georgia',
            DE: 'Germany',
            GH: 'Ghana',
            GI: 'Gibraltar',
            GR: 'Greece',
            GL: 'Greenland',
            GD: 'Grenada',
            GU: 'Guam',
            GT: 'Guatemala',
            GG: 'Guernsey',
            GN: 'Guinea',
            GW: 'Guinea-Bissau',
            HT: 'Haiti',
            HN: 'Honduras',
            HK: 'Hong Kong',
            HU: 'Hungary',
            IS: 'Iceland',
            IN: 'India',
            ID: 'Indonesia',
            IR: 'Iran, Islamic Republic of',
            IQ: 'Iraq',
            IE: 'Ireland',
            IM: 'Isle of Man',
            IL: 'Israel',
            IT: 'Italy',
            JM: 'Jamaica',
            JP: 'Japan',
            JE: 'Jersey',
            JO: 'Jordan',
            KZ: 'Kazakhstan',
            KE: 'Kenya',
            KI: 'Kiribati',
            KP: 'North Korea',
            KR: 'South Korea',
            KW: 'Kuwait',
            KG: 'Kyrgyzstan',
            LA: "Lao People's Democratic Republic",
            LV: 'Latvia',
            LB: 'Lebanon',
            LS: 'Lesotho',
            LR: 'Liberia',
            LY: 'Libya',
            LI: 'Liechtenstein',
            LT: 'Lithuania',
            LU: 'Luxembourg',
            MO: 'Macao',
            MK: 'Republic of Macedonia',
            MG: 'Madagascar',
            MW: 'Malawi',
            MY: 'Malaysia',
            MV: 'Maldives',
            ML: 'Mali',
            MT: 'Malta',
            MH: 'Marshall Islands',
            MQ: 'Martinique',
            MR: 'Mauritania',
            MU: 'Mauritius',
            MX: 'Mexico',
            FM: 'Micronesia, Federated States of',
            MD: 'Republic of Moldova',
            MC: 'Monaco',
            MN: 'Mongolia',
            ME: 'Montenegro',
            MS: 'Montserrat',
            MA: 'Morocco',
            MZ: 'Mozambique',
            MM: 'Myanmar',
            NA: 'Namibia',
            NR: 'Nauru',
            NP: 'Nepal',
            NL: 'Netherlands',
            NZ: 'New Zealand',
            NI: 'Nicaragua',
            NE: 'Niger',
            NG: 'Nigeria',
            NU: 'Niue',
            NF: 'Norfolk Island',
            MP: 'Northern Mariana Islands',
            NO: 'Norway',
            OM: 'Oman',
            PK: 'Pakistan',
            PW: 'Palau',
            PS: 'Palestinian Territory',
            PA: 'Panama',
            PG: 'Papua New Guinea',
            PY: 'Paraguay',
            PE: 'Peru',
            PH: 'Philippines',
            PN: 'Pitcairn',
            PL: 'Poland',
            PT: 'Portugal',
            PR: 'Puerto Rico',
            QA: 'Qatar',
            RO: 'Romania',
            RU: 'Russia',
            RW: 'Rwanda',
            KN: 'Saint Kitts and Nevis',
            LC: 'Saint Lucia',
            WS: 'Samoa',
            SM: 'San Marino',
            ST: 'Sao Tome and Principe',
            SA: 'Saudi Arabia',
            SN: 'Senegal',
            RS: 'Serbia',
            SC: 'Seychelles',
            SL: 'Sierra Leone',
            SG: 'Singapore',
            SX: 'Sint Maarten',
            SK: 'Slovakia',
            SI: 'Slovenia',
            SB: 'Solomon Islands',
            SO: 'Somalia',
            ZA: 'South Africa',
            SS: 'South Sudan',
            ES: 'Spain',
            LK: 'Sri Lanka',
            SD: 'Sudan',
            SR: 'Suriname',
            SZ: 'Swaziland',
            SE: 'Sweden',
            CH: 'Switzerland',
            SY: 'Syria',
            TW: 'Taiwan',
            TJ: 'Tajikistan',
            TZ: 'Tanzania',
            TH: 'Thailand',
            TG: 'Togo',
            TK: 'Tokelau',
            TO: 'Tonga',
            TT: 'Trinidad and Tobago',
            TN: 'Tunisia',
            TR: 'Turkey',
            TM: 'Turkmenistan',
            TC: 'Turks and Caicos Islands',
            TV: 'Tuvalu',
            UG: 'Uganda',
            UA: 'Ukraine',
            AE: 'United Arab Emirates',
            GB: 'United Kingdom',
            // US: 'United States',
            US: 'America/Canada',
            UY: 'Uruguay',
            UZ: 'Uzbekistan',
            VU: 'Vanuatu',
            VE: 'Venezuela, Bolivarian Republic of',
            VN: 'Viet Nam',
            VI: 'Virgin Islands',
            YE: 'Yemen',
            ZM: 'Zambia',
            ZW: 'Zimbabwe',
          };
          // 将键的第二个字母转换为小写
          const updatedCountries = Object.fromEntries(
            Object.entries(countries)
              .map(([key, value]) => [
                key[0] + key[1].toLowerCase() + key.slice(2), // 小写第二个字母
                value,
              ])
              .map(([key, value]) => [value, key]), // 反转键值
          );
          console.log(updatedCountries, '国旗列表');
          let regionList = data?.map(item => {
            return {
              svgIcon: updatedCountries[item.countryEn],
              ...item,
            };
          });
          console.log(regionList, callList, '整合后的国旗select');
          setRegionTimezones(regionList);

          setCallList(preState => {
            const tempCallList = { ...preState }; // 创建callList的副本
            tempCallList.userList[0].keyNumberArea =
              regionList?.[0]?.telephonePrefix;
            return tempCallList;
          });
        }
      },
    });
  };

  /**********************************************************************监听呼出状态**********************************************************************/
  // 启动通话录制（使用原生双声道录音）
  const startCallRecording = useCallback(
    async (localStream, remoteStream) => {
      try {
        if (stereoRecording) {
          try {
            const controls = await startStereoRecording(
              localStream,
              remoteStream,
            );

            console.log(
              '===call===原生双声道通话录制启动成功, 控制接口:',
              controls,
            );
            if (controls) {
              setControls(controls);
            }
          } catch (error) {
            console.error('===call===原生双声道录音设置失败:', error);
            // 降级到单声道
            try {
              await startRecording();
            } catch (fallbackError) {
              console.error('===call===单声道录音也失败:', fallbackError);
            }
          }
        } else {
          // 单声道模式：只录制麦克风
          await startRecording();
          console.log('===call===单声道通话录制启动成功');
        }
      } catch (error) {
        console.error('===call===启动通话录制失败:', error);
      }
    },
    [startRecording, startStereoRecording, stereoRecording],
  );
  const callOutAccept = async call => {
    console.log(
      'twilio===callOutAccept=======',
      call,
      call.status(),
      call.getLocalStream(),
      call.getRemoteStream(),
    );

    if (call.status() === 'open') {
      //创建工单，拿到电话信息
      if (call._direction === 'OUTGOING') {
        handleCallList('userList', [
          '',
          [['callSid', call.parameters?.CallSid]],
        ]);
        handleCallList('userList', [
          call.parameters?.CallSid,
          [['status', 'calling']],
        ]);
        getCallInfo({
          callSid: call.parameters?.CallSid,
          callType: 'OUTBOUND',
          eventCode: 'outbound_call_answering',
          customerContactInfo:
            callList.userList[callList.userList.length - 1]?.keyNumberArea +
            callList.userList[callList.userList.length - 1]?.keyNumber,
        });
      } else {
        handleCallList('userList', [
          call.parameters?.Params?.split('webhookCallSid=')?.[1],
          [['status', 'calling']],
        ]);
        //坐席接听分配
        acceptByAgentClient({
          callSid: call.parameters?.Params?.split('webhookCallSid=')?.[1],
          agentId: user.userId,
        });
        //记录坐席事件
        addPhoneContactDetail({
          companyId: user.companyId, //公司ID
          agentAccountId: user.userId, //坐席ID
          agentAccount: user.userName, //坐席name
          agentGroupId: user.deptId, //座席组ID
          agentGroup: '', //座席组name
          connectAlias: sessionStorage.getItem('twilioConnectAlias'), //实例别名（联络线路）
          incomingOutgoing: 'INBOUND', //呼入/呼出
          eventSystemTimestamp: '', //事件发生的系统时间
          contactId: call.parameters?.Params?.split('webhookCallSid=')?.[1], //联络ID,可能为空
          twilioAccountId: '', //twilio 账号ID，可能为空
          customerPhone:
            callList.userList[callList.userList.length - 1]?.keyNumberArea +
            callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
          systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
          eventName: 'answer', //事件名
          eventCode: 'answer', //事件code
          workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
          workOrderNumber: '', //工单编号，可能为空
          createTime: '', //记录的创建时间，不是事件发生时间
        });
      }
      //开始录制，使用原生双声道录音
      setTimeout(() => {
        startCallRecording(call.getLocalStream(), call.getRemoteStream());
      }, 500);
      //进入通话
      setCallingShow('calling');
    }
  };
  const callOutDisconnect = call => {
    if (call?.status() === 'closed') {
      console.log('twilio===callOutDisconnect=======', call, selectedConnect);
      // 停止录音 - 现在录音已经实时上传，这里只需要停止录音即可
      stopRecording()
        .then(result => {
          console.log('twilio===stopRecording=======录音已停止', result);

          // 由于录音已经实时上传，这里不再需要额外处理
          // 可以发送一个结束标识给后端，表示录音完成
          if (result && result.blob) {
            console.log('录音总时长:', result.audioInfo?.duration, '秒');
            console.log('录音总大小:', result.blob.size, 'bytes');
            // 注意：最后的录音片段已经通过 onChunkReady 回调自动上传了
          }
        })
        .catch(error => {
          console.error('停止录音失败:', error);
        });
      //记录挂断事件
      addPhoneContactDetail({
        companyId: user.companyId, //公司ID
        agentAccountId: user.userId, //坐席ID
        agentAccount: user.userName, //坐席name
        agentGroupId: user.deptId, //座席组ID
        agentGroup: '', //座席组name
        connectAlias: sessionStorage.getItem('twilioConnectAlias'), //实例别名（联络线路）
        incomingOutgoing: sessionStorage.getItem('incomingOutgoing'), //呼入/呼出
        eventSystemTimestamp: '', //事件发生的系统时间
        contactId: sessionStorage.getItem('twilioCallSid'), //联络ID,可能为空
        twilioAccountId: '', //twilio 账号ID，可能为空
        customerPhone:
          callList.userList[callList.userList.length - 1]?.keyNumberArea +
          callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
        systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
        eventName:
          sessionStorage.getItem('disconnectDir') === 'agent'
            ? 'hang_up'
            : 'customer_hang_up', //事件名
        eventCode:
          sessionStorage.getItem('disconnectDir') === 'agent'
            ? 'hang_up'
            : 'customer_hang_up', //事件code
        workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
        workOrderNumber: '', //工单编号，可能为空
        createTime: '', //记录的创建时间，不是事件发生时间
      });
      //修改通话状态
      if (sessionStorage.getItem('enterACW') === 'yes') {
        setCallingShow('callACW');

        handleCallList('userList', [
          sessionStorage.getItem('twilioCallSid'),
          [['status', 'callACW']],
        ]);
        //记录进入acw事件
        addPhoneContactDetail({
          companyId: user.companyId, //公司ID
          agentAccountId: user.userId, //坐席ID
          agentAccount: user.userName, //坐席name
          agentGroupId: user.deptId, //座席组ID
          agentGroup: '', //座席组name
          connectAlias: sessionStorage.getItem('twilioConnectAlias'), //实例别名（联络线路）
          incomingOutgoing: sessionStorage.getItem('incomingOutgoing'), //呼入/呼出
          eventSystemTimestamp: '', //事件发生的系统时间
          contactId: sessionStorage.getItem('twilioCallSid'), //联络ID,可能为空
          twilioAccountId: '', //twilio 账号ID，可能为空
          customerPhone:
            callList.userList[callList.userList.length - 1]?.keyNumberArea +
            callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
          systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
          customerPhone:
            sessionStorage.getItem('AfterKeyNumberArea') +
            sessionStorage.getItem('AfterPhoneNumber'), //客户电话
          eventName: 'enter_acw', //事件名
          eventCode: 'enter_acw', //事件code
          workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
          workOrderNumber: '', //工单编号，可能为空
          createTime: '', //记录的创建时间，不是事件发生时间
        });
      } else {
        setCallingShow('initial');
      }
      //重置一下chat呼叫过来的状态
      setChatPhone('');
      setChatPhoneType(false);
    }
    console.log('twilio===callOutDisconnect=======', call);
  };
  const callOutCancel = () => {
    console.log('twilio===callOutCancel=======');
    // 清除未接通计时器
    if (missCallTimer) {
      clearTimeout(missCallTimer);
      setMissCallTimer(null);
    }
  };
  const callOutReject = () => {
    console.log(
      'twilio===callOutReject=======',
      sessionStorage.getItem('missCall'),
      sessionStorage.getItem('missCall') !== 'true',
    );

    // 清除未接通计时器
    if (missCallTimer) {
      clearTimeout(missCallTimer);
      setMissCallTimer(null);
    }
    if (sessionStorage.getItem('missCall') !== 'true') {
      addPhoneContactDetail({
        companyId: user.companyId, //公司ID
        agentAccountId: user.userId, //坐席ID
        agentAccount: user.userName, //坐席name
        agentGroupId: user.deptId, //座席组ID
        agentGroup: '', //座席组name
        connectAlias: sessionStorage.getItem('twilioConnectAlias'), //实例别名（联络线路）
        incomingOutgoing: sessionStorage.getItem('incomingOutgoing'), //呼入/呼出
        eventSystemTimestamp: '', //事件发生的系统时间
        contactId: sessionStorage.getItem('twilioCallSid'), //联络ID,可能为空
        twilioAccountId: '', //twilio 账号ID，可能为空
        customerPhone:
          callList.userList[callList.userList.length - 1]?.keyNumberArea +
          callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
        systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
        eventName: 'refuse', //事件名
        eventCode: 'refuse', //事件code
        workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
        workOrderNumber: '', //工单编号，可能为空
        createTime: '', //记录的创建时间，不是事件发生时间
      });
    } else {
      addPhoneContactDetail({
        companyId: user.companyId, //公司ID
        agentAccountId: user.userId, //坐席ID
        agentAccount: user.userName, //坐席name
        agentGroupId: user.deptId, //座席组ID
        agentGroup: '', //座席组name
        connectAlias: sessionStorage.getItem('twilioConnectAlias'), //实例别名（联络线路）
        incomingOutgoing: sessionStorage.getItem('incomingOutgoing'), //呼入/呼出
        eventSystemTimestamp: '', //事件发生的系统时间
        contactId: sessionStorage.getItem('twilioCallSid'), //联络ID,可能为空
        twilioAccountId: '', //twilio 账号ID，可能为空
        customerPhone:
          callList.userList[callList.userList.length - 1]?.keyNumberArea +
          callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
        systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
        eventName: 'miss_call', //事件名
        eventCode: 'miss_call', //事件code
        workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
        workOrderNumber: '', //工单编号，可能为空
        createTime: '', //记录的创建时间，不是事件发生时间
      });
    }
    setCallingShow('initial');
  };
  const callOutError = error => {
    console.log('twilio===callOutError=======', error);
  };
  const callGetMutedStatus = (isMuted, call) => {
    setTimeout(() => {
      console.log(
        'twilio===callMuted=======',
        isMuted,
        call,
        sessionStorage.getItem('twilioSil'),
      );
      if (isMuted && sessionStorage.getItem('twilioSil') === 'true') {
        handleCallStatus(true, 'sil');
        handleCallList('userList', [
          call.callSid,
          [['callingStatus', 'sil', true]],
        ]);
      } else {
        handleCallStatus(false, 'sil');
        handleCallList('userList', [
          call.callSid,
          [['callingStatus', 'sil', false]],
        ]);
      }
    }, 0);
  };
  /**********************************************************************处理来电**********************************************************************/
  const handleIncoming = async connection => {
    console.log('twilio===incoming=======', connection, selectedConnect);
    //监听呼入状态
    connection.on('accept', call => callOutAccept(call));
    connection.on('disconnect', call => callOutDisconnect(call));
    connection.on('cancel', () => callOutCancel());
    connection.on('reject', () => callOutReject());
    connection.on('error', callOutError);
    connection.on('ringing', hasEarlyMedia => {
      console.log('twilio===ringing=======', hasEarlyMedia, call.status());
    });
    connection.on('mute', (isMuted, call) => callGetMutedStatus(isMuted, call));
    connection.on('reconnected', () => {
      console.log('twilio===reconnected=======');
    });
    connection.on('warning', function(warningName, warningData) {
      console.log('twilio===warning=======', warningName, warningData);
    });
    connection.on('warning-cleared', function(warningName) {
      console.log('twilio===warning-cleared=======', warningName);
    });

    //呼入记录呼入状态，呼叫方电话号码
    const phoneNumber = parsePhoneNumber(connection?.parameters?.From); // 输入号码
    const customerPhone =
      '+' + String(phoneNumber.countryCallingCode) + phoneNumber.nationalNumber;

    //创建工单，拿到电话信息
    await getCallInfo({
      callSid: connection.parameters?.Params?.split('webhookCallSid=')?.[1],
      callType: 'INBOUND',
      eventCode: 'ringing_the_bell',
      customerContactInfo: customerPhone,
      call: connection,
    });

    //本地状态变更
    setCallingShow('callIn');
    handleCallList('connectionId', connection.outboundConnectionId);
    handleCallList('userList', [
      '',
      [
        [
          'callSid',
          connection.parameters?.Params?.split('webhookCallSid=')?.[1],
        ],
      ],
    ]);
    handleCallList('userList', [
      '',
      [['accountSid', connection.parameters?.AccountSid]],
    ]);

    handleCallList('userList', [
      connection.parameters?.Params?.split('webhookCallSid=')?.[1],
      [
        ['keyNumber', phoneNumber.nationalNumber],
        ['keyNumberArea', '+' + String(phoneNumber.countryCallingCode)],
        ['status', 'callIn'],
      ],
    ]);
    sessionStorage.setItem('AfterPhoneNumber', phoneNumber.nationalNumber);
    sessionStorage.setItem(
      'AfterKeyNumberArea',
      '+' + String(phoneNumber.countryCallingCode),
    );
    setCallObj(connection);

    //监听按键事件
    window.addEventListener('keydown', e => handleKeyDown(e, connection));
  };
  /*******************************************************************触发missCall事件**********************************************************************/
  const missCallEnter = connection => {
    console.log(
      sessionStorage.getItem('misscallSecondsEnter'),
      'twilio===missCallEnter=======',
      connection,
      missCallTimer,
      callList,
      selectedConnect,
    );
    if (sessionStorage.getItem('misscallSecondsEnter') > 0) {
      // 清除之前的计时器（如果存在）
      if (missCallTimer) {
        clearTimeout(missCallTimer);
      }

      // 设置新的计时器并存储ID
      const timerId = setTimeout(() => {
        console.log(
          '触发missCall自动拒绝，未接通时长:',
          sessionStorage.getItem('misscallSecondsEnter'),
        );
        if (connection) {
          sessionStorage.setItem('missCall', 'true');
          connection.reject();
        }
        setMissCallTimer(null); // 清除计时器ID
      }, Number(sessionStorage.getItem('misscallSecondsEnter')) * 1000); // 转换为毫秒

      setMissCallTimer(timerId);
    }
  };
  /*******************************************************************接听来电**********************************************************************/
  const receiveCall = async () => {
    console.log(
      'twilio===receiveCall=======',
      callObj,
      selectedConnect,
      callList,
    );

    // 清除未接通计时器
    if (missCallTimer) {
      clearTimeout(missCallTimer);
      setMissCallTimer(null);
      console.log('已清除missCall计时器，用户主动接听电话');
    }

    try {
      await callObj.accept();
      // console.log(
      //   'twilio===receiveCall=======2',
      //   callObj,
      //   callObj._status,
      //   callObj?.status(),
      // );
      // setCallingShow('calling');
      // handleCallList('userList', [
      //   callObj.parameters?.CallSid,
      //   [['status', 'calling']],
      // ]);
    } catch {
      setAlertShow(true);
      setAlertText(
        getIntl().formatMessage({
          id: 'im.chat.translation.retry.re',
        }),
      );
    }
  };
  /*******************************************************************拒绝来电**********************************************************************/
  const rejectCall = async () => {
    console.log('twilio===rejectCall=======', callObj);
    try {
      await callObj.reject();
    } catch {
      console.log('twilio===rejectCall===error');
      setAlertShow(true);
      setAlertText(
        getIntl().formatMessage({
          id: 'im.chat.translation.retry.re',
        }),
      );
    }
  };
  //格式化电话号码
  const formatKeyNumber = keyNumber => {
    // 找到所有数字并拼接成格式
    // const formatted = keyNumber
    //   ?.replace(/[^\d*]/g, '')
    //   ?.replace(/(\d{3})(\d{4})(\d+)/, '$1 $2 $3');
    let formattedNumBer = keyNumber
      ? keyNumber
      : sessionStorage.getItem('AfterPhoneNumber');
    const formatted =
      formattedNumBer?.[0] +
      '******' +
      formattedNumBer?.[formattedNumBer.length - 1];
    return formatted;
  };
  //格式化时间
  const formatTime = secs => {
    const minutes = String(Math.floor(secs / 60)).padStart(2, '0');
    const seconds = String(secs % 60).padStart(2, '0');
    return `${minutes}:${seconds}`;
  };
  //处理直接复制带有区号的号码到输入框并拨打的情况
  const handleCountryCode = value => {
    let arrayList = value?.split('+');
    let phoneNumber = value;
    if (arrayList?.length > 0) {
      phoneNumber = '+' + arrayList[arrayList.length - 1];
    }
    try {
      console.log('处理后的电话号码', callList, phoneNumber, arrayList);
      const phoneNumberNew = parsePhoneNumber(phoneNumber); // 输入号码
      handleCallList('userList', [
        callList.userList[callList.userList.length - 1].callSid,
        [
          ['keyNumber', phoneNumberNew.nationalNumber],
          ['keyNumberArea', '+' + String(phoneNumberNew.countryCallingCode)],
        ],
      ]);
    } catch (error) {
      notification.error({
        message: error,
      });
    }
    return phoneNumber;
  };
  /**********************************************************************呼出**********************************************************************/
  const outBoundCall = async value => {
    if (value) {
      let number = handleCountryCode(value);
      const params = {
        To: number,
        ConnectId: selectedConnect.connectId,
      };
      const call = await device.connect({ params });
      console.log(
        'twilio===call=======',
        device.audio.ringtoneDevices.get(),
        device.audio.speakerDevices.get(),
        call.status(),
        call,
        call.outboundConnectionId,
        call.parameters?.CallSid,
      );
      if (call) {
        setCallObj(call);
        if (['ringing', 'connecting'].includes(call.status())) {
          //进入呼出状态
          setCallingShow('callOut');
          handleCallList('connectionId', call.outboundConnectionId);
          handleCallList('userList', ['', [['status', 'callOut']]]);

          //记录外呼事件
          // addPhoneContactDetail({
          //   companyId: user.companyId, //公司ID
          //   agentAccountId: user.userId, //坐席ID
          //   agentAccount: user.userName, //坐席name
          //   agentGroupId: user.deptId, //座席组ID
          //   agentGroup: '', //座席组name
          //   connectAlias: selectedConnect.connectAlias, //实例别名（联络线路）
          //   incomingOutgoing: 'OUTBOUND', //呼入/呼出
          //   eventSystemTimestamp: '', //事件发生的系统时间
          //   contactId: call.parameters?.CallSid, //联络ID,可能为空
          //   twilioAccountId: '', //twilio 账号ID，可能为空
          //   customerPhone:
          //     callList.userList[callList.userList.length - 1]?.keyNumberArea +
          //     callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
          //   systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
          //   eventName: 'telephone_outbound', //事件名
          //   eventCode: 'telephone_outbound', //事件code
          //   workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
          //   workOrderNumber: '', //工单编号，可能为空
          //   createTime: '', //记录的创建时间，不是事件发生时间
          // });
        }
        //监听呼出状态
        call.on('accept', call => callOutAccept(call));
        call.on('disconnect', call => callOutDisconnect(call));
        call.on('cancel', callOutCancel);
        call.on('error', callOutError);
        call.on('ringing', hasEarlyMedia => {
          console.log('twilio===ringing=======', hasEarlyMedia, call.status());
        });
        call.on('mute', (isMuted, call) => callGetMutedStatus(isMuted, call));
        call.on('reconnected', () => {
          console.log('twilio===reconnected=======');
        });
        call.on('warning', function(warningName, warningData) {
          console.log('twilio===warning=======', warningName, warningData);
        });
        call.on('warning-cleared', function(warningName) {
          console.log('twilio===warning-cleared=======', warningName);
        });
        //监听按键事件
        window.addEventListener('keydown', e => handleKeyDown(e, call));
      }
    }
  };
  /**********************************************************************结束呼叫**********************************************************************/
  //结束呼叫
  const destroyCall = () => {
    if (callObj) {
      //标注哪方挂断，true表示坐席，false表示客户
      sessionStorage.setItem('disconnectDir', 'agent');
      dispatch({
        type: 'worktable/hangupFromAgent',
        payload: {
          callSid: callList.userList[callList.userList.length - 1]?.callSid,
        },
        callback: response => {
          if (response) {
            let { code, data, msg } = response;
            if (code === 200) {
            } else {
              notification.error({
                message: msg,
              });
            }
          }
        },
      });
      // callObj.disconnect();
      // console.log('twilio===destroyCall=======', callObj, callObj.status());
      // if (callObj.status() === 'closed') {
      //   setCallingShow('callACW');
      //   //重置一下chat呼叫过来的状态
      //   setChatPhone('');
      //   setChatPhoneType(false);
      // }
    }
  };

  /**********************************************************************通话进入acw状态，关闭acw状态需要清空通话列**********************************************************************/
  const closeACW = async () => {
    setCallingShow('initial');
    //记录关闭acw事件
    addPhoneContactDetail({
      companyId: user.companyId, //公司ID
      agentAccountId: user.userId, //坐席ID
      agentAccount: user.userName, //坐席name
      agentGroupId: user.deptId, //座席组ID
      agentGroup: '', //座席组name
      connectAlias: selectedConnect.connectAlias, //实例别名（联络线路）
      incomingOutgoing: callList.incomingOutgoing, //呼入/呼出
      eventSystemTimestamp: '', //事件发生的系统时间
      contactId: callList.userList[callList.userList.length - 1]?.callSid, //联络ID,可能为空
      twilioAccountId: '', //twilio 账号ID，可能为空
      customerPhone:
        callList.userList[callList.userList.length - 1]?.keyNumberArea +
        callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
      systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
      eventName: 'finish_acw', //事件名
      eventCode: 'finish_acw', //事件code
      workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
      workOrderNumber: '', //工单编号，可能为空
      createTime: '', //记录的创建时间，不是事件发生时间
    });
  };
  //静音
  const onSilenceCall = value => {
    callObj.mute(true);
    sessionStorage.setItem('twilioSil', 'true');
    addPhoneContactDetail({
      companyId: user.companyId, //公司ID
      agentAccountId: user.userId, //坐席ID
      agentAccount: user.userName, //坐席name
      agentGroupId: user.deptId, //座席组ID
      agentGroup: '', //座席组name
      connectAlias: selectedConnect.connectAlias, //实例别名（联络线路）
      incomingOutgoing: callList.incomingOutgoing, //呼入/呼出
      eventSystemTimestamp: '', //事件发生的系统时间
      contactId: callList.userList[callList.userList.length - 1]?.callSid, //联络ID,可能为空
      twilioAccountId: '', //twilio 账号ID，可能为空
      customerPhone:
        callList.userList[callList.userList.length - 1]?.keyNumberArea +
        callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
      systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
      eventName: 'silent', //事件名
      eventCode: 'silent', //事件code
      workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
      workOrderNumber: '', //工单编号，可能为空
      createTime: '', //记录的创建时间，不是事件发生时间
    });
    // if (value !== 'multiple') {
    // }
  };
  //解除静音
  const onNOSilenceCall = value => {
    callObj.mute(false);
    sessionStorage.setItem('twilioSil', 'false');
    addPhoneContactDetail({
      companyId: user.companyId, //公司ID
      agentAccountId: user.userId, //坐席ID
      agentAccount: user.userName, //坐席name
      agentGroupId: user.deptId, //座席组ID
      agentGroup: '', //座席组name
      connectAlias: selectedConnect.connectAlias, //实例别名（联络线路）
      incomingOutgoing: callList.incomingOutgoing, //呼入/呼出
      eventSystemTimestamp: '', //事件发生的系统时间
      contactId: callList.userList[callList.userList.length - 1]?.callSid, //联络ID,可能为空
      twilioAccountId: '', //twilio 账号ID，可能为空
      customerPhone:
        callList.userList[callList.userList.length - 1]?.keyNumberArea +
        callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
      systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
      eventName: 'unmute', //事件名
      eventCode: 'unmute', //事件code
      workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
      workOrderNumber: '', //工单编号，可能为空
      createTime: '', //记录的创建时间，不是事件发生时间
    });
  };
  //保持通话功能
  const onKeepCall = (value, controls) => {
    handleCallStatus(true, 'keep');
    if (value !== 'multiple') {
      //会话中联系人列表
      handleCallList('userList', [
        callList.userList[callList.userList.length - 1]?.callSid,
        [['callingStatus', 'keep', true]],
      ]);
      // 记录开始保持时间
      setKeepInterval(Math.floor(Date.now() / 1000));
    } else {
    }
    if (device && callObj && controls) {
      console.log(
        device,
        controls,
        callObj,
        'twilio===触发onKeepCall==============',
      );
      // 静音自己的麦克风，对方听不到你的声音
      callObj.mute(true);
      // 静音录音中的左右声道
      controls.setLeftVolume(0.0);
      controls.setRightVolume(0.0);
      //音频流静音
      const remoteStream = callObj.getRemoteStream();
      if (remoteStream) {
        try {
          // 获取远程音频轨道并静音
          const audioTracks = remoteStream.getAudioTracks();
          console.log('twilio===audioTracks=======', audioTracks);
          audioTracks.forEach(track => {
            track.enabled = false;
          });
          addPhoneContactDetail({
            companyId: user.companyId, //公司ID
            agentAccountId: user.userId, //坐席ID
            agentAccount: user.userName, //坐席name
            agentGroupId: user.deptId, //座席组ID
            agentGroup: '', //座席组name
            connectAlias: selectedConnect.connectAlias, //实例别名（联络线路）
            incomingOutgoing: callList.incomingOutgoing, //呼入/呼出
            eventSystemTimestamp: '', //事件发生的系统时间
            contactId: callList.userList[callList.userList.length - 1]?.callSid, //联络ID,可能为空
            twilioAccountId: '', //twilio 账号ID，可能为空
            customerPhone:
              callList.userList[callList.userList.length - 1]?.keyNumberArea +
              callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
            systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
            eventName: 'on_the_phone', //事件名
            eventCode: 'on_the_phone', //事件code
            workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
            workOrderNumber: '', //工单编号，可能为空
            createTime: '', //记录的创建时间，不是事件发生时间
          });
        } catch (error) {
          console.error('静音远程音频失败:', error);
        }
      }
    }
  };
  //恢复通话功能
  const onNOKeepCall = (value, controls) => {
    handleCallStatus(false, 'keep');
    if (value !== 'multiple') {
      //会话中联系人列表
      handleCallList('userList', [
        callList.userList[callList.userList.length - 1]?.callSid,
        [['callingStatus', 'keep', false]],
      ]);
    } else {
    }
    if (device && callObj && controls) {
      console.log(
        device,
        controls,
        callObj,
        'twilio===触发onNOKeepCall==============',
      );

      // 恢复录音音量
      controls.setLeftVolume(1.0);
      controls.setRightVolume(1.0);

      // 恢复自己的麦克风
      callObj.mute(false);
      // 恢复远程音频流
      const remoteStream = callObj.getRemoteStream();
      if (remoteStream) {
        try {
          const audioTracks = remoteStream.getAudioTracks();
          console.log('twilio===恢复远程音频=======', audioTracks);
          audioTracks.forEach(track => {
            track.enabled = true;
          });
          addPhoneContactDetail({
            companyId: user.companyId, //公司ID
            agentAccountId: user.userId, //坐席ID
            agentAccount: user.userName, //坐席name
            agentGroupId: user.deptId, //座席组ID
            agentGroup: '', //座席组name
            connectAlias: selectedConnect.connectAlias, //实例别名（联络线路）
            incomingOutgoing: callList.incomingOutgoing, //呼入/呼出
            eventSystemTimestamp: '', //事件发生的系统时间
            contactId: callList.userList[callList.userList.length - 1]?.callSid, //联络ID,可能为空
            twilioAccountId: '', //twilio 账号ID，可能为空
            customerPhone:
              callList.userList[callList.userList.length - 1]?.keyNumberArea +
              callList.userList[callList.userList.length - 1]?.keyNumber, //客户电话
            systemPhone: sessionStorage.getItem('systemPhone'), //系统电话
            eventName: 'resume_and_maintain_the_call', //事件名
            eventCode: 'resume_and_maintain_the_call', //事件code
            workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,可能为空
            workOrderNumber: '', //工单编号，可能为空
            createTime: '', //记录的创建时间，不是事件发生时间
          });
        } catch (error) {
          console.error('恢复远程音频失败:', error);
        }
      }

      if (value !== 'multiple') {
        // 计算保持时长并调用接口
        const endTime = Math.floor(Date.now() / 1000);
        const duration = endTime - keepInterval;
      }
    }
  };
  /**********************************************************************下面是一些计时器，和状态监控**********************************************************************/
  //计时器逻辑
  useEffect(() => {
    let interval = null;
    // 呼出呼入界面计时器
    if (['callOut', 'callIn'].includes(callingShow)) {
      interval = setInterval(() => {
        handleCallList('userList', [
          callList.userList[callList.userList.length - 1]?.callSid,
          [['timeKeeping', '']],
        ]);
      }, 1000);
    } else if (['calling', 'multiple', 'callACW'].includes(callingShow)) {
      interval = setInterval(() => {
        handleCallList('userList', [
          callList.userList[callList.userList.length - 1]?.callSid,
          [['timeKeeping', '']],
        ]);
      }, 1000);
    } else if (
      ![
        'callOut',
        'callIn',
        'calling',
        'videoCalling',
        'videoCallin',
        'callACW',
        'videoACW',
      ].includes(callingShow)
    ) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [callingShow, callList.userList[callList.userList.length - 1]?.callSid]);
  //监控状态
  useEffect(() => {
    //acw状态重新计时
    if (callingShow === 'videoACW' || callingShow === 'callACW') {
      handleCallList('userList', [
        callList.userList[callList.userList.length - 1]?.callSid,
        [['timeKeeping', 0]],
      ]);
      setTicketInfo({
        ...ticketInfo,
        phoneAcwStatus: false,
      });
      getPhoneCurrentInfo({
        ...ticketInfo,
        phoneAcwStatus: false,
      });
      setCallingStatus([]);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
    //回到初始页面需要清空一些数据
    if (callingShow === 'initial') {
      // setTimeKeeping(0);
      setCallList({
        connectId: selectedConnect.connectId,
        ticketId: '',
        connectionId: '',
        agentId: '', //坐席id
        customerLanguage: '', //客户语言偏好
        robotWorkRecordId: '', //机器人工单id
        agentWorkRecordId: '', //人工工单id
        enterACW: '', //是否进入acw，当终端用户挂断时，前端可以直接进入acw
        systemPhone: '', //呼入呼出时，使用的系统电话
        incomingOutgoing: '', //呼出呼入
        userList: [
          {
            callSid: '',
            accountSid: '',
            keyNumberArea: regionTimezones?.[0]?.telephonePrefix,
            keyNumber: '',
            timeKeeping: 0,
            role: '',
            callingStatus: [],
            status: '',
            name: '',
          },
        ],
      });
      sessionStorage.setItem('autoCreateWorkID', '');
      sessionStorage.setItem('enterACW', '');
      sessionStorage.setItem('agentId', '');
      sessionStorage.setItem('customerLanguage', '');
      sessionStorage.setItem('robotWorkRecordId', '');
      sessionStorage.setItem('systemPhone', '');
      sessionStorage.setItem('misscallSecondsEnter', '');
      sessionStorage.setItem('incomingOutgoing', '');
      sessionStorage.setItem('missCall', '');
      sessionStorage.setItem('twilioCallSid', '');
      sessionStorage.setItem('twilioSil', '');

      setTicketInfo(null);
      getPhoneCurrentInfo({});
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
    if (JSON.parse(localStorage.getItem('callLog')) && callLogsExist) {
      return;
    }
    //接通电话需要清空计时器
    if (
      (callingShow === 'calling' || callingShow === 'videoCalling') &&
      !callingStatus.includes('connect')
    ) {
      //会话中联系人列表
      handleCallList('userList', [
        callList.userList[callList.userList.length - 1]?.callSid,
        [['timeKeeping', 0]],
      ]);
      setCallingStatus([]);
    }
  }, [callingShow]);

  /*
  ===========================================================音频处理相关函数===========================================
  **/
  /*********************************************************************上传单个录音片段到服务器 **************************************************************/
  /**
   * 上传单个录音片段到服务器（每分钟调用一次）
   * @param {Blob} blob 音频 Blob 片段
   * @param {Object} audioInfo 音频信息
   */
  const uploadRecordingChunk = async (blob, audioInfo, isEnd) => {
    try {
      console.log('===chunk===开始上传录音片段...', blob, audioInfo);

      console.log(`===chunk===原始音频信息:`, {
        size: blob.size,
        type: blob.type,
        sampleRate: audioInfo?.sampleRate,
        channels: audioInfo?.numberOfChannels,
        bitDepth: audioInfo?.bitDepth,
        duration: audioInfo?.duration,
        isStereo: audioInfo?.isStereo || false,
        leftChannel: audioInfo?.leftChannel,
        rightChannel: audioInfo?.rightChannel,
      });

      // 将blob转换为WAV格式
      const wavBlob = convertToWav(audioInfo);

      if (!wavBlob) {
        console.error('===chunk===WAV转换失败，使用原始blob');
        // 如果转换失败，使用原始blob
        var finalBlob = blob;
        var fileName = `audio_chunk_${Date.now()}.webm`;
        var fileType = 'webm';
      } else {
        console.log(`===chunk===WAV转换成功:`, {
          originalSize: blob.size,
          wavSize: wavBlob.size,
          originalType: blob.type,
          wavType: wavBlob.type,
          compressionRatio:
            (((blob.size - wavBlob.size) / blob.size) * 100).toFixed(1) + '%',
        });
        var finalBlob = wavBlob;
        var fileName = `audio_chunk_${Date.now()}.wav`;
        var fileType = 'wav';
      }

      // 创建FormData并添加音频数据
      const formData = new FormData();
      // 使用转换后的WAV blob或原始blob
      formData.append('audioData', finalBlob, fileName);
      formData.append('sampleRate', audioInfo?.sampleRate || 8000);
      formData.append('channels', audioInfo?.numberOfChannels || 1);
      formData.append(
        'bitDepth',
        fileType === 'wav' ? 16 : audioInfo?.bitDepth || 16,
      );
      formData.append('fileType', fileType); // 添加文件类型标识
      formData.append('agentId', user.userId);
      formData.append(
        'contactId',
        callList.userList[callList.userList.length - 1]?.callSid,
      );
      formData.append('ticketLanguage', callList.customerLanguage);
      formData.append(
        'workRecordId',
        sessionStorage.getItem('autoCreateWorkID'),
      );
      formData.append('end', isEnd);

      // const chunkData = {
      //   audioBuffer: Array.from(chunks), //录音的buffer流 - 转换为普通数组
      //   sampleRate: audioInfo?.sampleRate, //采样率
      //   channels: audioInfo?.numberOfChannels, //声道数 (channels，单声道为 1，立体声为 2)
      //   bitDepth: audioInfo?.bitDepth, //位深度 (bitDepth，通常为 16 位)
      //   agentId: user.userId, //坐席id
      //   contactId: callList.userList[callList.userList.length - 1]?.callSid, //callSid
      //   ticketLanguage: 'zh-CN', //转录语言
      //   workRecordId: sessionStorage.getItem('autoCreateWorkID'), //工单id
      //   end: isEnd, //是否最后一次（针对当前片段而言）
      // };

      await dispatch({
        type: 'worktable/twilioChunkUpload',
        payload: formData,
        callback: response => {
          if (response) {
            let { code, data, msg } = response;
            if (code === 200) {
              console.log(`===chunk===record上传分片 成功`, response);
            } else {
              console.log(`===chunk===record上传分片 失败`, response);
            }
          }
        },
      });

      console.log('===chunk===录音片段上传完成');
    } catch (error) {
      console.error('===chunk===上传录音片段失败:', error);
    }
  };

  /*
  ===========================================================音频处理相关工具函数（结束）===========================================
  **/
  /*********************************************************************调用记录通话中指标的接口 **************************************************************/
  const addPhoneContactDetail = async payload => {
    console.log(payload, callList, '触发addPhoneContactDetail==============');
    if (!payload.workOrderId) {
      payload.workOrderId = callList.ticketId;
    }
    payload.haveACWFlag = sessionStorage.getItem('enterACW') === 'yes';
    dispatch({
      type: 'worktable/saveEventDetailsTwilio',
      payload: payload,
      callback: response => {
        if (response.code == 200) {
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  const acceptByAgentClient = async payload => {
    dispatch({
      type: 'worktable/acceptByAgentClient',
      payload: payload,
      callback: response => {
        if (response.code == 200) {
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /*********************************************************************获取acw状态 **************************************************************/
  const getAcwStateTwilio = () => {
    let payload = {
      companyId: user.companyId,
      agentAccountId: user.userId,
    };
    dispatch({
      type: 'worktable/getAcwStateTwilio',
      payload: payload,
      callback: response => {
        if (response.code == 200) {
          intitializeDevice();
          if (response.data.acwState) {
            setCallingShow('callACW');
            const phoneNumber = parsePhoneNumber(response.data.customerPhone); // 输入号码
            handleCallList('incomingOutgoing', response.data.incomingOutgoing);
            handleCallList('agentWorkRecordId', response.data.workOrderId);
            handleCallList('systemPhone', response.data.systemPhone);
            handleCallList('userList', [
              '',
              [['callSid', response.data.contactId]],
            ]);
            handleCallList('userList', [
              response.data.contactId,
              [
                ['keyNumber', phoneNumber.nationalNumber],
                ['keyNumberArea', '+' + String(phoneNumber.countryCallingCode)],
                ['status', 'callACW'],
              ],
            ]);
            sessionStorage.setItem(
              'autoCreateWorkID',
              response.data.workOrderId,
            );
            sessionStorage.setItem('systemPhone', response.data.systemPhone);
            sessionStorage.setItem('twilioCallSid', response.data.contactId);
            sessionStorage.setItem(
              'incomingOutgoing',
              response.data.incomingOutgoing,
            );
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /*********************************************************************查询工单详情 **************************************************************/
  const queryWorkOrderDetail = payload => {
    dispatch({
      type: 'workOrderCenter/queryWorkOrderDetail',
      payload: payload.workOrderId,
      callback: response => {
        if (response.code == 200) {
          setTicketInfo({
            ...response.data,
            phoneNumber: response.data.customerPhone,
            voiceName: '',
            phoneAcwStatus: true,
            connectType: 'Twilio',
            workRecordId: response.data.workRecordId,
            contactId: payload.contactId,
          });
          getPhoneCurrentInfo({
            ...response.data,
            phoneNumber: response.data.customerPhone,
            voiceName: '',
            phoneAcwStatus: true,
            connectType: 'Twilio',
            workRecordId: response.data.workRecordId,
            contactId: payload.contactId,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /*********************************************************************查询电话信息 **************************************************************/
  const getCallInfo = async payload => {
    dispatch({
      type: 'worktable/getCallInfo',
      payload: {
        callSid: payload.callSid,
      },
      callback: response => {
        if (response.code == 200) {
          handleCallList('agentId', user.userId);
          handleCallList('customerLanguage', response.data.customerLanguage);
          handleCallList('robotWorkRecordId', response.data.robotWorkRecordId);
          handleCallList('agentWorkRecordId', response.data.agentWorkRecordId);
          handleCallList(
            'misscallSecondsEnter',
            response.data.misscallSecondsEnter,
          );
          handleCallList('enterACW', response.data.enterACW);
          handleCallList('systemPhone', response.data.systemPhone);
          handleCallList('incomingOutgoing', payload.callType);

          sessionStorage.setItem(
            'autoCreateWorkID',
            response.data.agentWorkRecordId,
          );
          sessionStorage.setItem('twilioCallSid', response.data.callSid);
          sessionStorage.setItem('enterACW', response.data.enterACW);
          sessionStorage.setItem('agentId', user.userId);
          sessionStorage.setItem(
            'customerLanguage',
            response.data.customerLanguage,
          );
          sessionStorage.setItem(
            'robotWorkRecordId',
            response.data.robotWorkRecordId,
          );
          sessionStorage.setItem('systemPhone', response.data.systemPhone);
          sessionStorage.setItem(
            'misscallSecondsEnter',
            response.data.misscallSecondsEnter,
          );
          sessionStorage.setItem('incomingOutgoing', payload.callType);
          if (
            payload.callType === 'INBOUND' &&
            response.data.misscallSecondsEnter > 0
          ) {
            //下面记录missCall事件，可能会触发拒绝接听
            missCallEnter(payload.call);
          }
          //记录坐席事件
          addPhoneContactDetail({
            companyId: user.companyId, //公司ID
            agentAccountId: user.userId, //坐席ID
            agentAccount: user.userName, //坐席name
            agentGroupId: user.deptId, //座席组ID
            agentGroup: '', //座席组name
            connectAlias: sessionStorage.getItem('twilioConnectAlias'), //实例别名（联络线路）
            incomingOutgoing: payload.callType, //呼入/呼出
            eventSystemTimestamp: '', //事件发生的系统时间
            contactId: response.data.callSid, //联络ID,可能为空
            twilioAccountId: '', //twilio 账号ID，可能为空
            customerPhone: payload.customerContactInfo, //客户电话
            systemPhone: response.data.systemPhone, //系统电话
            eventName: payload.eventCode, //事件名
            eventCode: payload.eventCode, //事件code
            workOrderId: response.data.agentWorkRecordId, //工单id,可能为空
            workOrderNumber: '', //工单编号，可能为空
            createTime: '', //记录的创建时间，不是事件发生时间
          });
          setTimeout(() => {
            //查询工单详情
            queryWorkOrderDetail({
              workOrderId: response.data.agentWorkRecordId,
              contactId: response.data.callSid,
            });
          }, 10000);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  return ccpError ? (
    <div className={styles.contentBoxPhoneError}>
      <Alert
        description={alertText}
        type="error"
        closable
        onClose={() => onCloseAlert()}
        style={{
          display: alertShow ? '' : 'none',
          position: 'absolute',
          zIndex: 100,
          width: '80%',
          left: '10%',
          top: '5%',
          borderRadius: 4,
          border: '1px solid #F22417',
          background:
            'linear-gradient(0deg, rgba(242, 36, 23, 0.10) 0%, rgba(242, 36, 23, 0.10) 100%), rgba(255, 255, 255, 0.80)',
          boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.15)',
          backdropFilter: 'blur(2px)',
        }}
      />
      <div
        style={{
          margin: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: 10,
        }}
      >
        <img src={ccpErrorImg} style={{ width: '100%', height: 'auto' }}></img>
        <p style={{ color: '#999', textAlign: 'center', fontSize: 12 }}>
          <FormattedMessage
            id="new.worktable.phone.refresh"
            values={{
              refresh: (
                <span
                  style={{ color: '#3463FC', cursor: 'pointer' }}
                  onClick={() => {
                    location.reload();
                  }}
                >
                  <FormattedMessage id="new.worktable.phone.refresh.Retry" />
                </span>
              ),
            }}
          />
        </p>
      </div>
    </div>
  ) : (
    <>
      <div
        style={{
          background: ['initial', 'callOut', 'callIn', 'videoCallin'].includes(
            callingShow,
          )
            ? 'rgba(255, 255, 255, 0.6)'
            : ['callACW', 'videoACW'].includes(callingShow)
            ? 'linear-gradient(180deg, rgba(255, 255, 255, 0.10) 0%, rgba(33, 38, 43, 0.10) 100%), #FFF'
            : ['calling', 'videoCalling'].includes(callingShow) &&
              !callingStatus.includes('sil') &&
              !callingStatus.includes('keep')
            ? 'linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.00) 5%), linear-gradient(180deg, rgba(235, 247, 228, 0.10) 0%, rgba(19, 200, 37, 0.10) 100%)'
            : ['calling', 'videoCalling'].includes(callingShow) &&
              (callingStatus.includes('sil') || callingStatus.includes('keep'))
            ? 'linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.00) 5%), linear-gradient(180deg, rgba(250, 233, 225, 0.10) 0%, rgba(255, 211, 1, 0.10) 100%)'
            : callingShow === 'multiple'
            ? 'linear-gradient(180deg, rgba(255, 255, 255, 0.10) 0%, rgba(52, 99, 252, 0.10) 74.5%), #FFF'
            : '#fff',
        }}
        className={styles.contentBoxPhone}
      >
        {/**********************************************************************错误提示**********************************************************************/}
        <Alert
          description={alertText}
          type="error"
          closable
          onClose={() => onCloseAlert()}
          style={{
            display: alertShow ? '' : 'none',
            position: 'absolute',
            zIndex: 100,
            width: '80%',
            left: '10%',
            top: '5%',
            borderRadius: 4,
            border: '1px solid #F22417',
            background:
              'linear-gradient(0deg, rgba(242, 36, 23, 0.10) 0%, rgba(242, 36, 23, 0.10) 100%), rgba(255, 255, 255, 0.80)',
            boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.15)',
            backdropFilter: 'blur(2px)',
          }}
        />
        <Spin spinning={ccpLoading}>
          {/*********************************************************************** 初始状态 ***********************************************************************/}
          <div
            className={styles.contentBoxInitial}
            style={{ display: callingShow === 'initial' ? '' : 'none' }}
          >
            <div className={styles.contentBoxPhoneTitle}>
              <FormattedMessage
                id="new.worktable.phone.title"
                defaultMessage="数字键盘"
              />
            </div>
            <div className={styles.contentBoxPhoneConnect}>
              <p>
                <FormattedMessage
                  id="new.worktable.phone.connect"
                  defaultMessage="选择 Connect 实例"
                />
              </p>
              <Select
                placeholder={getIntl().formatMessage({
                  id: 'new.worktable.phone.connect.p',
                })}
                options={connectList}
                showSearch
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                onChange={(value, item) => onChangeConncet(value, item)}
                value={callList.connectId}
              />
            </div>
            <div className={styles.contentBoxPhoneNumber}>
              <Space direction="vertical" id={'spaceBox'}>
                <p>
                  <FormattedMessage
                    id="new.worktable.phone.number"
                    defaultMessage="请输入手机号码"
                  />
                </p>
                <Space.Compact>
                  <Select
                    value={callList?.userList[0]?.keyNumberArea}
                    getPopupContainer={() =>
                      document.getElementById('spaceBox')
                    }
                    showSearch
                    onChange={e =>
                      handleCallList('userList', ['', [['keyNumberArea', e]]])
                    }
                  >
                    {regionTimezones?.map(item => {
                      const Icon = Flags[item.svgIcon];
                      return (
                        <Select.Option
                          value={item.telephonePrefix}
                          className={styles.regionSelect}
                        >
                          <Space>
                            <span>{Icon ? <Icon /> : null}</span>
                            <span
                              style={{
                                marginRight: Icon ? 20 : 40,
                                fontSize: 12,
                              }}
                            >
                              {item.telephonePrefix}
                            </span>
                            {item.countryEn}
                          </Space>
                        </Select.Option>
                      );
                    })}
                  </Select>
                  <div
                    style={{
                      border: '0.1px #999 solid',
                      height: 16,
                      opacity: '0.4',
                      background: '#999',
                      marginTop: 12,
                    }}
                  ></div>
                  <Input
                    value={callList?.userList[0]?.keyNumber}
                    onChange={e => {
                      playAudio();
                      handleKeyNumber(e.target.value);
                    }}
                    onPressEnter={e => onPressEnter(e)}
                    placeholder={getIntl().formatMessage({
                      id: 'new.worktable.phone.number',
                    })}
                  />
                  <span
                    style={{
                      position: 'absolute',
                      right: 12,
                      top: 40,
                      cursor: 'pointer',
                      zIndex: 999,
                    }}
                    onClick={() =>
                      handleCallList('userList', ['', [['keyNumber', '']]])
                    }
                  >
                    {InputCloseIcon()}
                  </span>
                </Space.Compact>
              </Space>
            </div>
            <div className={styles.contentBoxPhonekeyboard}>
              {['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'].map(
                item => {
                  return (
                    <div
                      className={styles.item}
                      onClick={() => passKey(0, item)}
                    >
                      {item}
                    </div>
                  );
                },
              )}
            </div>
            <div className={styles.contentBoxPhoneButton}>
              <Button
                onClick={() => {
                  outBoundCall(
                    callList.userList[0].keyNumberArea +
                      callList.userList[0].keyNumber,
                  );
                }}
              >
                {CallIcon()}
                <FormattedMessage
                  id="new.worktable.phone.call"
                  defaultMessage="呼叫"
                />
              </Button>
            </div>
          </div>
          {/******************************************************** 通话状态下的样式 videoRTCCapabilities===true代表视频电话*******************************************/}
          <div
            className={styles.contentBoxCall}
            style={{
              display: [
                'callOut',
                'callACW',
                'callIn',
                'calling',
                'videoCalling',
                'videoCallin',
                'videoACW',
              ].includes(callingShow)
                ? ''
                : 'none',
            }}
            id="callBox"
          >
            {/*********************************************************************** 电话 ***********************************************************************/}
            {['videoCallin', 'videoCalling'].includes(callingShow) &&
            videoRTCCapabilities ? (
              <div>
                <img
                  src={agent}
                  style={{
                    width: 100,
                    height: 100,
                    marginTop: 80,
                    marginBottom: 20,
                  }}
                />
                <div
                  style={{ textAlign: 'center', fontSize: 16, fontWeight: 700 }}
                >
                  {callList.userList[0].name}
                </div>
              </div>
            ) : ['videoACW'].includes(callingShow) && videoRTCCapabilities ? (
              <div className={styles.numberCall}>
                {callList.userList[0].name}
              </div>
            ) : (
              <div className={styles.numberCall}>
                {callList.userList[0]?.keyNumberArea ??
                  sessionStorage.getItem('AfterKeyNumberArea')}
                &nbsp;&nbsp;
                {formatKeyNumber(callList.userList[0]?.keyNumber)}
              </div>
            )}
            {/* **********************************************************************连接状态 ***********************************************************************/}
            <div
              className={styles.connectionCall}
              style={{
                marginBottom: ['videoCallin', 'videoCalling'].includes(
                  callingShow,
                )
                  ? 3
                  : '',
              }}
            >
              {callingShow === 'callACW' || callingShow === 'videoACW' ? (
                <span style={{ color: '#666' }}>
                  <FormattedMessage
                    id="new.worktable.phone.callACW"
                    defaultMessage="通话结束后的工作"
                  />
                </span>
              ) : (callingShow === 'calling' ||
                  callingShow === 'videoCalling') &&
                callingStatus.length == 0 ? (
                <span
                  style={{
                    color: callingShow === 'videoCalling' ? '#333' : '#13C825',
                    fontWeight: 700,
                    fontSize: callingShow === 'videoCalling' ? 12 : '',
                  }}
                >
                  <FormattedMessage
                    id="new.worktable.phone.calling"
                    defaultMessage="正在通话"
                  />
                </span>
              ) : (callingShow === 'calling' ||
                  callingShow === 'videoCalling') &&
                callingStatus.includes('keep') ? (
                <span
                  style={{
                    color: '#FFD301',
                    fontWeight: 700,
                    fontSize: callingShow === 'videoCalling' ? 12 : '',
                  }}
                >
                  <FormattedMessage
                    id="new.worktable.phone.callKeep"
                    defaultMessage="通话保持中"
                  />
                </span>
              ) : (callingShow === 'calling' ||
                  callingShow === 'videoCalling') &&
                callingStatus.includes('sil') ? (
                <span
                  style={{
                    color: '#FFD301',
                    fontWeight: 700,
                    fontSize: callingShow === 'videoCalling' ? 12 : '',
                  }}
                >
                  <FormattedMessage
                    id="new.worktable.phone.callSil"
                    defaultMessage="静音中"
                  />
                </span>
              ) : callingShow === 'callIn' ? (
                <FormattedMessage
                  id="new.worktable.phone.callin"
                  defaultMessage="新的来电"
                />
              ) : (
                <span
                  style={{ fontSize: callingShow === 'videoCallin' ? 12 : '' }}
                >
                  <FormattedMessage
                    id="new.worktable.phone.callout"
                    defaultMessage="正在连接"
                  />
                </span>
              )}
            </div>
            {/*********************************************************************** 计时器 ***********************************************************************/}
            <div
              className={styles.timeCall}
              style={{
                fontSize: ['videoCallin', 'videoCalling'].includes(callingShow)
                  ? 12
                  : '',
              }}
            >
              {callingShow !== 'callACW' &&
              callingShow !== 'videoCalling' &&
              callingShow !== 'videoCallin' &&
              callingShow !== 'videoACW'
                ? CallTimeIcon()
                : ''}
              {formatTime(callList?.userList[0]?.timeKeeping)}
            </div>
            {/*********************************************************************** 通话中的功能按钮 ***********************************************************************/}
            {callingShow === 'calling' && !videoRTCCapabilities ? (
              <div className={styles.keyBtnCall}>
                {callingStatus.includes('keep') ? (
                  <Button
                    onClick={() => onNOKeepCall('', controls)}
                    style={{ background: '#AD30E5' }}
                  >
                    {CallPlayIcon(16, 16)}
                    <span style={{ color: '#fff' }}>
                      <FormattedMessage
                        id="new.worktable.phone.callBtn.1.target"
                        defaultMessage="恢复通话"
                      />
                    </span>
                  </Button>
                ) : (
                  <Button onClick={() => onKeepCall('', controls)}>
                    {CallKeepIcon(16, 16)}
                    <FormattedMessage
                      id="new.worktable.phone.callBtn.1"
                      defaultMessage="保持通话"
                    />
                  </Button>
                )}

                <Button
                  onClick={() => {
                    setDrawerOpen(true);
                    // pushUserList('multiple');
                  }}
                >
                  {CallKeyBoardIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.callBtn.2"
                    defaultMessage="数字键盘"
                  />
                </Button>
                {callingStatus.includes('sil') ? (
                  <Button
                    onClick={() => onNOSilenceCall()}
                    style={{ background: '#AD30E5' }}
                  >
                    {CallSlinoIcon(16, 16)}
                    <span style={{ color: '#fff' }}>
                      <FormattedMessage
                        id="new.worktable.phone.callBtn.3"
                        defaultMessage="静音"
                      />
                    </span>
                  </Button>
                ) : (
                  <Button onClick={() => onSilenceCall()}>
                    {CallSliIcon(16, 16)}
                    <FormattedMessage
                      id="new.worktable.phone.callBtn.3"
                      defaultMessage="静音"
                    />
                  </Button>
                )}
                {/* <Button
                  onClick={() => {
                    handleCallStatus(true, 'connect');
                    pushUserList('multiple');
                    listQueueQuickConnects();
                  }}
                >
                  {CallConnectFastIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.callBtn.4"
                    defaultMessage="快速连接"
                  />
                </Button> */}
              </div>
            ) : (
              ''
            )}
            {/*********************************************************************** 通话前的按钮 拒绝 接受 ***********************************************************************/}
            {['callOut', 'calling'].includes(callingShow) ? (
              <div className={styles.endCall}>
                <Button onClick={() => destroyCall()}>
                  {CallEndTimeIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.callEnd"
                    defaultMessage="结束通话"
                  />
                </Button>
              </div>
            ) : ['callIn', 'videoCallin'].includes(callingShow) ? (
              <div className={styles.endCallinOut}>
                <div className={styles.endCallin}>
                  <Button onClick={() => receiveCall()}>
                    {CallIcon()}
                    <FormattedMessage
                      id="new.worktable.phone.callReceive"
                      defaultMessage="接听电话"
                    />
                  </Button>
                </div>
                <div className={styles.endCallout}>
                  <Button onClick={() => rejectCall()}>
                    {CallEndTimeIcon()}
                    <FormattedMessage
                      id="new.worktable.phone.callReject"
                      defaultMessage="拒绝电话"
                    />
                  </Button>
                </div>
              </div>
            ) : ['videoCalling'].includes(callingShow) &&
              videoRTCCapabilities ? (
              <div className={styles.contentBoxPhoneButton}>
                {callingStatus.includes('keep') ? (
                  <div
                    className={styles.contentBoxPhoneButtonItemStop}
                    style={{
                      backgroundColor: '#AD30E5',
                    }}
                    onClick={() => onNOKeepCall('', controls)}
                  >
                    {CallPlayIcon(25, 25)}
                  </div>
                ) : (
                  <div
                    className={styles.contentBoxPhoneButtonItemStop}
                    style={{
                      backgroundColor: '#fff',
                    }}
                    onClick={() => onKeepCall('', controls)}
                  >
                    {CallKeepIcon(25, 25)}
                  </div>
                )}
                {callingStatus.includes('sil') ? (
                  <div
                    className={styles.contentBoxPhoneButtonItemSli}
                    style={{
                      backgroundColor: '#AD30E5',
                    }}
                    onClick={() => onNOSilenceCall()}
                  >
                    {CallSlinoIcon(25, 25)}
                  </div>
                ) : (
                  <div
                    className={styles.contentBoxPhoneButtonItemSli}
                    style={{
                      backgroundColor: '#fff',
                    }}
                    onClick={() => onSilenceCall()}
                  >
                    {CallSliIcon(25, 25)}
                  </div>
                )}
                <div
                  className={styles.contentBoxPhoneButtonItemYellow}
                  onClick={() => {
                    handleCallStatus(true, 'connect');
                    pushUserList('multiple');
                    listQueueQuickConnects();
                  }}
                >
                  {QuickConnectBtn()}
                </div>
                <div
                  className={styles.contentBoxPhoneButtonItemRed}
                  onClick={() => destroyCall()}
                >
                  {MultipleEndBtn(25, 25)}
                </div>
              </div>
            ) : (
              <div className={styles.endCallACW}>
                <div>
                  <FormattedMessage
                    id="im.chat.acw.open"
                    values={{
                      acw: (
                        <Tooltip
                          color="#92a4ec"
                          placement="top"
                          title={getIntl().formatMessage({
                            id: 'im.chat.acw.prompt',
                          })}
                        >
                          <span style={{ color: '#3463FC' }}>ACW</span>
                        </Tooltip>
                      ),
                    }}
                  />
                </div>
                <Button onClick={() => closeACW()}>
                  {CallCloseIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.callClose"
                    defaultMessage="关闭通话"
                  />
                </Button>
              </div>
            )}
            {/*********************************************************************** 数字键盘抽屉 **********************************************************************/}
            <Drawer
              title={getIntl().formatMessage({
                id: 'new.worktable.phone.title',
              })}
              getContainer={() => document.getElementById('callBox')}
              placement={'bottom'}
              width={'100%'}
              height={'50%'}
              open={drawerOpen}
              closeIcon={DrawerCloseIcon()}
              onClose={() => {
                setDrawerOpen(false);
                setKeyboardValue('');
                // deleteUserList();
              }}
            >
              <div className={styles.contentBoxPhoneNumberDrawer}>
                <Space direction="vertical" id={'spaceBox1'}>
                  <Space.Compact>
                    {/* <Select
                      value={callList?.userList[1]?.keyNumberArea}
                      getPopupContainer={() =>
                        document.getElementById('spaceBox1')
                      }
                      showSearch
                      onChange={e =>
                        handleCallList('userList', ['', [['keyNumberArea', e]]])
                      }
                    >
                      {regionTimezones?.map(item => {
                        const Icon = Flags[item.svgIcon];
                        return (
                          <Select.Option
                            key={item.telephonePrefix}
                            value={item.telephonePrefix}
                            className={styles.regionSelect}
                          >
                            <Space>
                              {Icon ? <Icon /> : null}
                              <span style={{ marginRight: 20, fontSize: 12 }}>
                                {item.telephonePrefix}
                              </span>
                              {item.countryEn}
                            </Space>
                          </Select.Option>
                        );
                      })}
                    </Select>
                   <div
                      style={{
                        border: '0.1px #999 solid',
                        height: 16,
                        opacity: '0.4',
                        background: '#999',
                        marginTop: 12,
                      }}
                    ></div> */}
                    <Input
                      value={keyboardValue}
                      onChange={e => {
                        playAudio();
                        setKeyboardValue(e.target.value);
                      }}
                      // onPressEnter={e => onPressEnter(e)}
                      placeholder={getIntl().formatMessage({
                        id: 'channel.please.input',
                      })}
                    />
                    <span
                      style={{
                        position: 'absolute',
                        right: 12,
                        top: 12,
                        cursor: 'pointer',
                        zIndex: 999,
                      }}
                      onClick={() => setKeyboardValue('')}
                    >
                      {InputCloseIcon()}
                    </span>
                  </Space.Compact>
                </Space>
              </div>
              <div className={styles.contentBoxPhonekeyboard}>
                {[
                  '1',
                  '2',
                  '3',
                  '4',
                  '5',
                  '6',
                  '7',
                  '8',
                  '9',
                  '*',
                  '0',
                  '#',
                ].map(item => {
                  return (
                    <div
                      className={styles.item}
                      onClick={() => passKey(1, item)}
                    >
                      {item}
                    </div>
                  );
                })}
              </div>
              {/* <div className={styles.contentBoxPhoneButtonDrawer}>
                <Button onClick={() => multiConnectCall()}>
                  {CallIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.call"
                    defaultMessage="呼叫"
                  />
                </Button>
              </div> */}
            </Drawer>
          </div>
          {/* ***********************************************************************快速连接 ***********************************************************************/}
          <div
            className={styles.contentBoxQuick}
            style={{
              display: callingStatus.includes('connect') ? '' : 'none',
            }}
          >
            <Spin spinning={quickConnectLoading}>
              <div className={styles.contentBoxPhoneTitle}>
                <FormattedMessage
                  id="new.worktable.phone.call.quickConnect"
                  defaultMessage="快速连接"
                />
              </div>

              <div className={styles.contentBoxPhoneNumber}>
                <Space direction="vertical" id={'spaceBox2'}>
                  <p>
                    <FormattedMessage
                      id="new.worktable.phone.number"
                      defaultMessage="请输入手机号码"
                    />
                  </p>
                  <Space.Compact>
                    <Select
                      value={
                        callList?.userList?.[callList?.userList?.length - 1]
                          ?.keyNumberArea
                      }
                      getPopupContainer={() =>
                        document.getElementById('spaceBox2')
                      }
                      showSearch
                      onChange={e =>
                        handleCallList('userList', ['', [['keyNumberArea', e]]])
                      }
                    >
                      {regionTimezones?.map(item => {
                        const Icon = Flags[item.svgIcon];
                        return (
                          <Select.Option
                            value={item.telephonePrefix}
                            className={styles.regionSelect}
                          >
                            <Space>
                              {Icon ? <Icon /> : null}
                              <span style={{ marginRight: 20, fontSize: 12 }}>
                                {item.telephonePrefix}
                              </span>
                              {item.countryEn}
                            </Space>
                          </Select.Option>
                        );
                      })}
                    </Select>
                    <div
                      style={{
                        border: '0.1px #999 solid',
                        height: 16,
                        opacity: '0.4',
                        background: '#999',
                        marginTop: 12,
                      }}
                    ></div>
                    <Input
                      value={
                        callList?.userList?.[callList?.userList?.length - 1]
                          ?.name
                      }
                      onChange={e => handleSearchKeyNumber(e.target.value)}
                      placeholder={getIntl().formatMessage({
                        id: 'new.worktable.phone.number',
                      })}
                    />
                  </Space.Compact>
                </Space>
              </div>
              <div className={styles.contentBoxPhoneConnect}>
                <p>
                  <FormattedMessage
                    id="new.worktable.phone.call.quickConnect.agent.p"
                    defaultMessage="请选择要转接的座席或队列"
                  />
                </p>

                <div className={styles.contentBoxPhoneUserList}>
                  {filteredData?.map(item => {
                    return (
                      <div
                        className={styles.contentBoxPhoneUserListItem}
                        onClick={() => selectQuickConnect(item)}
                        style={{
                          backgroundColor:
                            currentQCUser?.endpointId === item.endpointId
                              ? 'rgba(173, 48, 229, 1)'
                              : '',
                          color:
                            currentQCUser?.endpointId === item.endpointId
                              ? '#fff'
                              : '',
                        }}
                      >
                        <div className={styles.contentBoxPhoneUserListFont}>
                          {item.name}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className={styles.contentBoxPhoneButton}>
                <Button onClick={() => multiConnectCall('qucikConnect')}>
                  {CallIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.call"
                    defaultMessage="呼叫"
                  />
                </Button>
                <Button
                  onClick={() => {
                    handleCallStatus(false, 'connect');
                    deleteUserList();
                    setCurrentQCUser(null);
                  }}
                >
                  {CallCloseIcon()}
                  <FormattedMessage
                    id="AIGC.Drawer.two.form.cancel"
                    defaultMessage="取消"
                  />
                </Button>
              </div>
            </Spin>
          </div>
          {/*********************************************************************** 多方通话 ***********************************************************************/}
          <div
            className={styles.contentBoxMultiple}
            style={{
              display: callingShow === 'multiple' ? '' : 'none',
            }}
          >
            <div className={styles.contentBoxMultipleTop}>
              <div className={styles.contentBoxPhoneTitle}>
                <FormattedMessage
                  id="new.worktable.phone.call.multiple.people"
                  defaultMessage="多方通话"
                />
              </div>
              <div className={styles.contentBoxPhoneList}>
                <Space direction="vertical">
                  {callList.userList?.map((item, index) => {
                    return (
                      <div
                        className={styles.contentBoxPhoneItem}
                        style={{
                          background: item.status?.includes('multiple')
                            ? '#AD30E5'
                            : '',
                        }}
                      >
                        <div className={styles.contentBoxPhoneItemContent}>
                          <span>
                            {item.keyNumber ? (
                              <span
                                style={{
                                  color: item.status?.includes('multiple')
                                    ? '#fff'
                                    : '#333',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                }}
                              >
                                {item.keyNumberArea ??
                                  sessionStorage.getItem('AfterKeyNumberArea')}
                                &nbsp; &nbsp;
                                {formatKeyNumber(item.keyNumber)}
                              </span>
                            ) : (
                              <span
                                style={{
                                  color: item.status?.includes('multiple')
                                    ? '#fff'
                                    : '#333',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                }}
                              >
                                {item.name}
                              </span>
                            )}
                            {item.role}
                          </span>
                          <span
                            style={{
                              color: item.status?.includes('multiple')
                                ? '#fff'
                                : '#666',
                              fontFamily: 'Poppins, sans-serif',
                              fontSize: 12,
                              fontWeight: 400,
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            {item.status?.includes('multiple')
                              ? CallTimeSmallBaiIcon()
                              : CallTimeSmallIcon()}
                            <span style={{ marginLeft: 3 }}>
                              {formatTime(item.timeKeeping)}
                            </span>
                          </span>
                        </div>
                        <div className={styles.contentBoxPhoneItemStatus}>
                          <div
                            style={{
                              display: 'inline-flex',
                              gap: 5,
                              alignItems: 'center',
                              justifyContent: 'flex-end',
                            }}
                          >
                            {item.status?.includes(
                              'multiple',
                            ) ? null : item.callingStatus?.includes('keep') ? (
                              <div
                                className={styles.contentBoxPhoneButtonItemStop}
                                style={{
                                  backgroundColor: '#AD30E5',
                                }}
                                onClick={() => setNoOneKeep(item)}
                              >
                                {CallPlayIcon(8, 8)}
                              </div>
                            ) : (
                              <div style={{ display: 'flex' }}>
                                {item.callingStatus?.includes('sil') ? (
                                  <div
                                    className={
                                      styles.contentBoxPhoneButtonItemSli
                                    }
                                    style={{
                                      backgroundColor: '#AD30E5',
                                    }}
                                    onClick={() => setNoOneSil(item)}
                                  >
                                    {CallSlinoIcon(8, 8)}
                                  </div>
                                ) : (
                                  <div
                                    className={
                                      styles.contentBoxPhoneButtonItemSli
                                    }
                                    style={{
                                      backgroundColor: '#fff',
                                    }}
                                    onClick={() => setOneSil(item)}
                                  >
                                    {CallSliIcon(8, 8)}
                                  </div>
                                )}
                                {!item.callingStatus?.includes('keep') ? (
                                  <div
                                    className={
                                      styles.contentBoxPhoneButtonItemStop
                                    }
                                    style={{
                                      backgroundColor: '#fff',
                                      marginLeft: 5,
                                    }}
                                    onClick={() => setOneKeep(item)}
                                  >
                                    {CallKeepIcon(8, 8)}
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            )}
                            <div
                              className={styles.contentBoxPhoneButtonItemRed}
                              onClick={() => {
                                setOneEnded(item, index);
                              }}
                            >
                              {MultipleEndBtn(8, 8)}
                            </div>
                          </div>
                          <div style={{ marginTop: 3 }}>
                            {item.status === 'multiple' &&
                            item.callingStatus.length <= 0 ? (
                              <span
                                style={{
                                  color: '#fff',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                }}
                              >
                                <FormattedMessage
                                  id="new.worktable.phone.callout"
                                  defaultMessage="正在连接"
                                />
                              </span>
                            ) : item.callingStatus?.includes('keep') ? (
                              <span
                                style={{
                                  color: '#FFD301',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                }}
                              >
                                <FormattedMessage
                                  id="new.worktable.phone.callKeep"
                                  defaultMessage="通话保持中"
                                />
                              </span>
                            ) : item.callingStatus?.includes('sil') ? (
                              <span
                                style={{
                                  color: '#FFD301',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                }}
                              >
                                <FormattedMessage
                                  id="new.worktable.phone.callSil"
                                  defaultMessage="静音中"
                                />
                              </span>
                            ) : (
                              <span
                                style={{
                                  color: '#13C825',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 400,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                }}
                              >
                                <FormattedMessage
                                  id="new.worktable.phone.calling"
                                  defaultMessage="正在通话"
                                />
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </Space>
              </div>
            </div>

            <div className={styles.contentBoxPhoneButton}>
              {callingStatus.includes('keep') ? (
                <div
                  className={styles.contentBoxPhoneButtonItemStop}
                  style={{
                    backgroundColor: '#AD30E5',
                  }}
                  onClick={() => onNOKeepCall('multiple', controls)}
                >
                  {CallPlayIcon(25, 25)}
                </div>
              ) : (
                <div
                  className={styles.contentBoxPhoneButtonItemStop}
                  style={{
                    backgroundColor: '#fff',
                  }}
                  onClick={() => onKeepCall('multiple', controls)}
                >
                  {CallKeepIcon(25, 25)}
                </div>
              )}
              {callingStatus.includes('sil') ? (
                <div
                  className={styles.contentBoxPhoneButtonItemSli}
                  style={{
                    backgroundColor: '#AD30E5',
                  }}
                  onClick={() => onNOSilenceCall('multiple')}
                >
                  {CallSlinoIcon(25, 25)}
                </div>
              ) : (
                <div
                  className={styles.contentBoxPhoneButtonItemSli}
                  style={{
                    backgroundColor: '#fff',
                  }}
                  onClick={() => onSilenceCall('multiple')}
                >
                  {CallSliIcon(25, 25)}
                </div>
              )}
              <div
                className={styles.contentBoxPhoneButtonItemYellow}
                onClick={() => {
                  handleCallStatus(true, 'connect');
                  pushUserList('multiple');
                  listQueueQuickConnects();
                }}
              >
                {QuickConnectBtn()}
              </div>
              <div
                className={styles.contentBoxPhoneButtonItemRed}
                onClick={() => destroyCall('multiple')}
              >
                {MultipleEndBtn(25, 25)}
              </div>
            </div>
          </div>
        </Spin>
      </div>
    </>
  );
});

export default TwilioComponents;
