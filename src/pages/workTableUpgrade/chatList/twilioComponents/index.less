.contentBoxPhone {
  border-radius: 0px 0px 0px 4px;
  box-shadow: -1px 1px 1px 1px rgba(0, 0, 0, 0.05);
  height: 100%;

  .contentBoxInitial {
    padding: 20px 10px;
    display: flex;
    flex-direction: column;
    height: 100%;

    .contentBoxPhoneTitle {
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      margin-bottom: 20px;
    }

    .contentBoxPhoneConnect {
      margin-bottom: 20px;

      :global {
        .ant-select {
          height: 40px;
          width: 100%;
          font-size: 12px;
        }

        .ant-select-selector {
          border-radius: 100px;
          border: 1px solid #e6e6e6;
          background: rgba(255, 255, 255, 0.5);
          box-shadow: none;
          height: 40px;
          width: 100%;
          line-height: 38px;
        }

        .ant-select-selection-item {
          line-height: 38px;
        }

        .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
          border-color: #40a9ff;
          border-right-width: 1px;
          box-shadow: none !important;
        }

        .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
          .ant-select-selector {
          box-shadow: none !important;
        }

        .ant-select-selection-placeholder {
          line-height: 38px !important;
          color: #999;
          font-family: 'Poppins', sans-serif;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }

      p {
        margin-bottom: 10px;
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
    }

    .contentBoxPhoneNumber {
      margin-bottom: 20px;
      position: relative;

      p {
        margin-bottom: 10px;
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }

      :global {
        .ant-select-suffix {
          svg {
            width: 12px !important;
            height: 12px !important;
          }
        }

        .ant-select {
          height: 40px;
          width: 110px;
        }

        .ant-select-dropdown {
          width: 100% !important;
          background: rgba(255, 255, 255, 0.8);
          backdrop-filter: blur(2px);
          border-radius: 4px;
          border: 1px solid rgb(230, 230, 230);
          box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 4px 0px;
          padding: 5px;
          padding-top: 20px;
          top: 48px !important;
          z-index: 99;
          border-top: none;

          .ant-select-item {
            border-bottom: 1px solid #e6e6e6;
          }

          svg {
            width: 25px;
            height: 18px;
          }
        }

        .ant-select-selection-item,
        .ant-select-selection-search {
          display: flex;
          align-items: center;

          svg {
            width: 25px;
            height: 18px;
          }

          .ant-space {
            gap: 16px !important;
            margin-left: 5px;
          }

          .ant-space-item:nth-child(1) {
            margin-top: 8px;
          }
        }

        .ant-space-compact {
          width: 100%;
        }

        .ant-space {
          width: 100%;
          gap: 0 !important;
          position: relative;
        }

        .ant-space-item {
          z-index: 100;
        }

        .ant-input {
          border-radius: 0 100px 100px 0px !important;
          border: 1px solid #e6e6e6;
          background: rgba(255, 255, 255, 0.5);
          box-shadow: none;
          height: 40px !important;
          width: 100%;
          border-left: 0;
          padding-right: 40px;
        }

        .ant-select-selector {
          border-radius: 100px;
          border: 1px solid #e6e6e6;
          background: rgba(255, 255, 255, 0.5);
          box-shadow: none;
          height: 40px;
          width: 100%;
          border-right: 0;
        }

        .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
          border: 1px solid #e6e6e6;
          box-shadow: none !important;
        }

        .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
          .ant-select-selector {
          border: 1px solid #e6e6e6;
          box-shadow: none !important;
        }

        .ant-select-selection-placeholder {
          line-height: 38px !important;
          color: #999;
          font-family: 'Poppins', sans-serif;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
    }
  }

  .contentBoxPhonekeyboard {
    margin-top: 10px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 三列 */
    grid-template-rows: repeat(4, auto);
    /* 四行 */
    gap: 10px;

    /* 行和列之间的间距 */
    .item {
      display: flex;
      height: 40px;
      padding: 4px 12px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      border: 1px solid #ad30e5;
      opacity: 0.9;
      background: rgba(255, 255, 255, 0.5);
      color: #ad30e5;
      text-align: center;
      font-family: 'Poppins', sans-serif;
      font-size: 20px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      cursor: pointer;
      /* 100% */
    }

    .item:hover {
      background-color: #ad30e5;
      color: #fff;
    }
  }

  .contentBoxPhoneButton {
    margin-top: 80px;
    display: flex;
    justify-content: center;

    :global {
      .ant-btn {
        background: #ad30e5;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 36px;
        border-radius: 4px;
        border: 1px solid #ad30e5;
        gap: 8px;
        height: 40px;
      }
    }
  }

  :global {
    .ant-alert-description {
      color: #f22417;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}

.contentBoxPhoneError {
  border-radius: 0px 0px 0px 4px;
  box-shadow: -1px 1px 1px 1px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  align-items: center;

  :global {
    .ant-alert-description {
      color: #f22417;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}

.contentBoxCall {
  padding: 20px 10px;
  display: flex;
  flex-direction: column;
  height: 100%;
  align-items: center;
  position: relative;

  .numberCall {
    margin-top: 60px;
    color: #333;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 30px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 20px;
    overflow-wrap: break-word;
  }

  .connectionCall {
    color: #333;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 20px;
  }

  .timeCall {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    color: #333;
    font-family: 'Poppins', sans-serif;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .keyBtnCall {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10%;

    :global {
      .ant-btn {
        display: inline-flex;
        padding: 6px 36px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        margin-top: 10px;
        border-radius: 4px;
        border: 1px solid #ad30e5;
        background: rgba(255, 255, 255, 0.5);
        color: #ad30e5;
        text-align: justify;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        /* 18px */
      }

      .ant-btn:hover {
        background: #ad30e5;
        color: #fff;

        svg path {
          fill: #fff;
        }
      }
    }
  }

  .endCall {
    display: flex;
    justify-content: center;

    :global {
      .ant-btn {
        display: flex;
        width: 144px;
        padding: 12px 36px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        height: 40px;
        border-radius: 4px;
        border: none;
        background: #f22417;
        color: #fff;
        text-align: justify;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        position: absolute;
        bottom: 5%;
        /* 18px */
      }
    }
  }

  .endCallinOut {
    position: absolute;
    bottom: 60px;
    width: 100%;
    text-align: center;

    .endCallin {
      margin-right: 5%;
      display: inline-block;
      width: 40%;

      :global {
        .ant-btn {
          background: #13c825;
        }
      }
    }

    .endCallout {
      display: inline-block;
      width: 40%;

      :global {
        .ant-btn {
          background: #f22417;
        }
      }
    }

    :global {
      .ant-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        gap: 8px;
        height: 40px;
        border-radius: 4px;
        border: none;
        color: #fff;
        text-align: justify;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
      }
    }
  }

  .endCallACW {
    position: absolute;
    bottom: 0;
    min-height: 131px;
    background: linear-gradient(0deg, #e8e8e8 0%, #e8e8e8 100%),
      rgba(255, 255, 255, 0.5);
    stroke-width: 1px;
    stroke: #e6e6e6;
    backdrop-filter: blur(50px);
    padding: 20px 40px;
    color: #666;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    :global {
      .ant-btn {
        display: inline-flex;
        padding: 4px 16px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        margin-top: 10px;
        border-radius: 4px;
        border: 1px solid #ad30e5;
        background: rgba(255, 255, 255, 0.5);
        color: #ad30e5;
        text-align: justify;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
      }
    }
  }

  :global {
    .ant-drawer {
      display: contents;
      background-color: rgba(255, 255, 255, 0.5);
      /* 透明白色背景 */
      backdrop-filter: blur(2px);
    }

    .ant-drawer-header-title {
      display: inline-table;
    }

    .ant-drawer-bottom > .ant-drawer-content-wrapper {
      box-shadow: 0 -1px 10px 0px rgba(0, 0, 0, 0.05);
    }

    .ant-drawer-content-wrapper {
    }

    .ant-drawer-content {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(2px);
    }

    .ant-drawer-close {
      float: right;
      margin-right: 0;
    }

    .ant-drawer-title {
      float: left;
    }

    .ant-drawer-header {
      padding: 18px 11px;
      border-bottom: none;
    }

    .ant-drawer-body {
      padding: 0 11px 18px;
    }

    .ant-drawer-mask {
      background: none;
    }
  }

  /* 背景模糊效果 */
  .contentBoxPhoneNumberDrawer {
    margin-bottom: 20px;
    position: relative;

    p {
      margin-bottom: 10px;
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }

    :global {
      .ant-select-suffix {
        svg {
          width: 12px !important;
          height: 12px !important;
        }
      }

      .ant-select {
        height: 40px;
        width: 110px;
      }

      .ant-select-dropdown {
        width: 100% !important;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(2px);
        border-radius: 4px;
        border: 1px solid rgb(230, 230, 230);
        box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 4px 0px;
        padding: 5px;
        padding-top: 20px;
        top: 48px !important;
        z-index: 99;
        border-top: none;

        .ant-select-item {
          border-bottom: 1px solid #e6e6e6;
        }

        svg {
          width: 25px;
          height: 18px;
        }
      }

      .ant-select-selection-item,
      .ant-select-selection-search {
        display: flex;
        align-items: center;

        svg {
          width: 25px;
          height: 18px;
        }

        .ant-space {
          gap: 16px !important;
          margin-left: 5px;
        }

        .ant-space-item:nth-child(1) {
          margin-top: 8px;
        }
      }

      .ant-space-compact {
        width: 100%;
      }

      .ant-space {
        width: 100%;
        gap: 0 !important;
        position: relative;
      }

      .ant-space-item {
        z-index: 100;
      }

      .ant-input {
        border-radius: 100px !important;
        border: 1px solid #ad30e5;
        background: rgba(173, 48, 229, 0.05);
        box-shadow: none;
        height: 40px !important;
        width: 100%;
        // border-left: 0;
        color: #999;
        display: inline-block;
        padding-right: 30px;
      }

      .ant-select-selector {
        border-radius: 100px;
        border-radius: 100px;
        border: 1px solid #ad30e5;
        background: rgba(173, 48, 229, 0.05);
        border-right: none;
        height: 40px;
        width: 100%;
      }

      .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
        border: 1px solid #ad30e5;
        box-shadow: none !important;
      }

      .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
        .ant-select-selector {
        border: 1px solid #ad30e5;
        box-shadow: none !important;
      }

      .ant-select-selection-placeholder {
        line-height: 38px !important;
        color: #999;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }

  .contentBoxPhonekeyboard {
    margin-top: 10px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 三列 */
    grid-template-rows: repeat(4, auto);
    /* 四行 */
    gap: 10px;

    /* 行和列之间的间距 */
    .item {
      display: flex;
      height: 40px;
      padding: 4px 12px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      border: 1px solid #ad30e5;
      opacity: 0.9;
      background: rgba(255, 255, 255, 0.5);
      color: #ad30e5;
      text-align: center;
      font-family: 'Poppins', sans-serif;
      font-size: 20px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      cursor: pointer;
      /* 100% */
    }

    .item:hover {
      background-color: #ad30e5;
      color: #fff;
    }
  }

  .contentBoxPhoneButtonDrawer {
    margin-top: 30px;
    display: flex;
    justify-content: center;

    :global {
      .ant-btn {
        background: #ad30e5;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 10%;
        border-radius: 4px;
        border: 1px solid #ad30e5;
        gap: 8px;
        height: 40px;
      }
    }
  }

  .contentBoxPhoneButton {
    display: flex;
    align-items: center;
    justify-content: space-around;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(255, 255, 255, 0.5) 100%
    );
    backdrop-filter: blur(50px);
    width: 100%;
    height: 77px;
    position: absolute;
    bottom: 0;

    .contentBoxPhoneButtonItemYellow {
      background-color: #ffd301;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0px 2.5px 3.333px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .contentBoxPhoneButtonItemRed {
      background-color: #f22417;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0px 2.5px 3.333px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .contentBoxPhoneButtonItemStop {
      border: 1px solid #ad30e5;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0px 2.5px 3.333px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .contentBoxPhoneButtonItemSli {
      border: 1px solid #ad30e5;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0px 2.5px 3.333px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
}

.contentBoxQuick {
  padding: 20px 10px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: absolute;
  z-index: 99;
  width: 100%;
  top: 0;
  background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(52, 99, 252, 0.1) 74.5%
    ),
    #fff;

  .contentBoxPhoneTitle {
    color: #333;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-bottom: 20px;
  }

  .contentBoxPhoneNumber {
    margin-bottom: 20px;
    position: relative;

    p {
      margin-bottom: 10px;
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }

    :global {
      .ant-select-suffix {
        svg {
          width: 12px !important;
          height: 12px !important;
        }
      }

      .ant-select {
        height: 40px;
        width: 110px;
      }

      .ant-select-dropdown {
        width: 100% !important;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(2px);
        border-radius: 4px;
        border: 1px solid rgb(230, 230, 230);
        box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 4px 0px;
        padding: 5px;
        padding-top: 20px;
        top: 48px !important;
        z-index: 99;
        border-top: none;

        .ant-select-item {
          border-bottom: 1px solid #e6e6e6;
        }

        svg {
          width: 25px;
          height: 18px;
        }
      }

      .ant-space-compact {
        width: 100%;
      }

      .ant-space {
        width: 100%;
        gap: 0 !important;
        position: relative;
      }

      .ant-select-selection-item,
      .ant-select-selection-search {
        display: flex;
        align-items: center;

        svg {
          width: 25px;
          height: 18px;
        }

        .ant-space {
          gap: 16px !important;
          margin-left: 5px;
        }

        .ant-space-item:nth-child(1) {
          margin-top: 8px;
        }
      }

      .ant-space-item {
        z-index: 100;
      }

      .ant-input {
        border-radius: 0 100px 100px 0px !important;
        border: 1px solid #e6e6e6;
        background: rgba(255, 255, 255, 0.5);
        box-shadow: none;
        height: 40px !important;
        width: 100%;
        border-left: 0;
      }

      .ant-select-selector {
        border-radius: 100px;
        border: 1px solid #e6e6e6;
        background: rgba(255, 255, 255, 0.5);
        box-shadow: none;
        height: 40px;
        width: 100%;
        border-right: 0;
      }

      .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
        border: 1px solid #e6e6e6;
        box-shadow: none !important;
      }

      .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
        .ant-select-selector {
        border: 1px solid #e6e6e6;
        box-shadow: none !important;
      }

      .ant-select-selection-placeholder {
        line-height: 38px !important;
        color: #999;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }

  .contentBoxPhoneConnect {
    margin-bottom: 20px;
    height: 50%;

    p {
      margin-bottom: 10px;
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }

    .contentBoxPhoneUserList {
      border-radius: 4px;
      border: 1px solid #e6e6e6;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(2px);
      width: 100%;
      height: 100%;
      padding: 0 5px;
      overflow-y: scroll;

      .contentBoxPhoneUserListItem {
        border-bottom: 1px solid #e6e6e6;
        border-radius: 3px;
        color: #333;
      }

      .contentBoxPhoneUserListFont {
        width: 100%;
        margin: 2px 0;
        border-radius: 2px;
        cursor: pointer;
        font-size: 12px;
        padding: 8px 0;
        padding-left: 6px;
      }

      .contentBoxPhoneUserListItem:hover {
        background-color: rgba(173, 48, 229, 0.1);
      }
    }
  }

  .contentBoxPhoneButton {
    margin-top: 80px;
    display: flex;
    justify-content: center;
    gap: 20px;

    :global {
      .ant-btn:nth-child(1) {
        background: #ad30e5;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 10%;
        border-radius: 4px;
        border: 1px solid #ad30e5;
        gap: 8px;
        height: 40px;
      }

      .ant-btn:nth-child(2) {
        background: #fff;
        color: #ad30e5;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 10%;
        border-radius: 4px;
        border: 1px solid #ad30e5;
        gap: 8px;
        height: 40px;
      }
    }
  }
}

.contentBoxMultiple {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 100%;

  .contentBoxMultipleTop {
    padding: 20px 10px;

    :global {
      .ant-space {
        width: 100%;
      }
    }
  }

  .contentBoxPhoneTitle {
    color: #333;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-bottom: 20px;
  }

  .contentBoxPhoneItem {
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    background: rgba(255, 255, 255, 0.5);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .contentBoxPhoneItemContent {
    display: flex;
    flex-direction: column;
  }

  .contentBoxPhoneItemStatus {
    display: flex;
    flex-direction: column;

    .contentBoxPhoneButtonItemStop {
      border: 1px solid #ad30e5;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .contentBoxPhoneButtonItemSli {
      border: 1px solid #ad30e5;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      cursor: pointer;

      display: flex;
      justify-content: center;
      align-items: center;
    }

    .contentBoxPhoneButtonItemRed {
      background-color: #f22417;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }

  .contentBoxPhoneButton {
    display: flex;
    align-items: center;
    justify-content: space-around;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(255, 255, 255, 0.5) 100%
    );
    backdrop-filter: blur(50px);
    width: 100%;
    height: 77px;

    .contentBoxPhoneButtonItemYellow {
      background-color: #ffd301;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0px 2.5px 3.333px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .contentBoxPhoneButtonItemRed {
      background-color: #f22417;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0px 2.5px 3.333px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .contentBoxPhoneButtonItemStop {
      border: 1px solid #ad30e5;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0px 2.5px 3.333px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .contentBoxPhoneButtonItemSli {
      border: 1px solid #ad30e5;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0px 2.5px 3.333px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
}

.regionSelect {
  width: 100%;

  :global {
    .ant-space-item {
      span {
        margin-top: 6px;
      }

      margin-right: 10px;
    }

    .ant-space {
      gap: 16px !important;
      margin-left: 5px;
    }
  }

  svg {
    width: 25px;
    height: 18px;
  }
}
