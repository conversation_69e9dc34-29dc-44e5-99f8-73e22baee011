import React, { useState, useEffect } from 'react';
import styles from './index.less';
import { Spin, Input, Button, Table, notification, Popconfirm, Tabs } from 'antd';
import { useDispatch, getIntl, FormattedMessage, history } from 'umi';
import { Reorder, Add, TableDetailIcon, Edit, Delete, BackIcon } from './icon';
import ChatIcon from '../../assets/chat-icon.jpg';
import AppChatOutlinedIcon from '../../assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '../../assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon from '../../assets/AppVideoOutlined.svg';
import AwsChannelIcon from '../../assets/aws-channel-icon.svg';
import TableEmailIcon from '../../assets/table-email-icon.png';
import AllchannelIcon from '../../assets/allchannel.svg';
import TablePhoneIcon from '../../assets/table-phone-icon.png';
import TableInfoIcon from '../../assets/table-info-icon.png';
import NewWebOnlineVoiceIcon from '../../assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '../../assets/app-online-voice-icon.svg';
import NewFaceBookIcon from '../../assets/new-facebook-icon.svg';
import NewInstagramIcon from '../../assets/ins.svg';
import NewLineIcon from '../../assets/new-line-icon.svg';
import NewTwitterIcon from '../../assets/new-twitter-icon.svg';
import NewTelegramIcon from '../../assets/new-telegram-icon.svg';
import NewWeComIcon from '../../assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '../../assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '../../assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '../../assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '../../assets/google-play-icon.svg';
import WhatsAppIcon from '../../assets/whats-app.svg';
import HOCAuth from '@/components/HOCAuth/index';
import TikTokIcon from '@/assets/new-tiktok-icon.svg';
import DiscordIcon from '@/assets/new-discord-icon.svg';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc';
import { MenuOutlined } from '@ant-design/icons';
import { arrayMoveImmutable } from 'array-move';

const AllocationRules = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [tableDataSource, setTableDataSource] = useState([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [isSort, setIsSort] = useState(false); // true需要拖拽排序
  const [activeTabKey, setActiveTabKey] = useState('1'); // 当前激活的筛选标签
  const [tabCounts, setTabCounts] = useState({ // 各类型数量统计
    phone: 0,
    chat: 0,
    email: 0,
  });
  const [selectItem, setSelectItem] = useState({
    pageNum: pageNum,
    pageSize: pageSize,
    type: '1', // 默认电话类型
  });
  const SortableItem = SortableElement(props => <tr {...props} />);
  const SortableBody = SortableContainer(props => <tbody {...props} />);
  const DragHandle = SortableHandle(() => (
    <MenuOutlined
      style={{
        cursor: 'grab',
        color: '#999',
      }}
    />
  ));

  useEffect(() => {
    const fetchData = async () => {
      await queryAiMatchList();
      await fetchTabCounts();
    };
    fetchData();
  }, [pageNum, pageSize]);

  useEffect(() => {
    const fetchData = async () => {
      await queryAiMatchList();
    };
    fetchData();
  }, [activeTabKey]);

  // 模拟获取数量统计的接口
  const fetchTabCounts = () => {
    return new Promise((resolve) => {
      // 模拟接口请求
      setTimeout(() => {
        setTabCounts({
          phone: Math.floor(Math.random() * 100) + 10, // 模拟电话数量
          chat: Math.floor(Math.random() * 80) + 5,    // 模拟聊天数量
          email: Math.floor(Math.random() * 60) + 8,   // 模拟邮件数量
        });
        resolve();
      }, 500);
    });
  };

  const queryAiMatchList = () => {
    setLoading(true);
    dispatch({
      type: 'workOrderCenter/getAiMatchList',
      payload: selectItem,
      callback: response => {
        setLoading(false);
        if (response.code == 200) {
          setTableDataSource(response.data.records);
          setTotal(+response.data.total);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 处理标签切换
  const handleTabChange = (key) => {
    setActiveTabKey(key);
    setPageNum(1); // 重置页码
    setSelectItem({
      ...selectItem,
      type: key,
      pageNum: 1,
    });
  };

  /**
   * 新建规则
   */
  const handleAddAlloctionRule = () => {
    let editObj = {
      type: 'add',
    };
    localStorage.setItem('editObj', JSON.stringify(editObj));
    history.push({ pathname: '/allocationRuleAdd', state: editObj });
  };
  /**
   *详情
   */
  const handleDetail = data => {
    let editObj = {
      type: 'detail',
      routingId: data.routingId,
      defaultRouting: data.defaultRouting,
    };
    localStorage.setItem('editObj', JSON.stringify(editObj));
    history.push({ pathname: '/allocationRuleDetail', state: editObj });
  };
  /**
   * 修改匹配规则
   */
  const handleEdit = data => {
    let editObj = {
      type: 'edit',
      routingId: data.routingId,
      defaultRouting: data.defaultRouting,
    };
    localStorage.setItem('editObj', JSON.stringify(editObj));
    history.push({ pathname: '/allocationRuleAdd', state: editObj });
  };
  /**
   * 删除匹配规则
   */
  const handleDelete = record => {
    dispatch({
      type: 'allocation/delRuleInfo',
      payload: { routingId: record.routingId },
      callback: data => {
        setPageNum(1);
        notification.success({
          message: data.msg,
        });
        queryAiMatchList();
      },
    });
  };
  const DraggableContainer = props => (
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );
  const DraggableBodyRow = ({ className, style, ...restProps }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    let index = tableDataSource.findIndex(
      x => x.routingId === restProps['data-row-key'],
    );
    let currentItem = tableDataSource.find(
      x => x.routingId === restProps['data-row-key'],
    );
    if (+currentItem?.defaultRouting === 1) {
      return <tr className={className} {...restProps} />;
    }
    return (
      <SortableItem
        index={index}
        {...restProps}
        style={{ border: '1px solid #F9F9F9' }}
      />
    );
  };
  const onSortEnd = ({ oldIndex, newIndex }) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(
        tableDataSource.slice(),
        oldIndex,
        newIndex,
      ).filter(el => !!el);
      setTableDataSource(newData);
    }
  };
  // 拖拽排序
  const handleSort = () => {
    setIsSort(true);
    if (!isSort) {
      setColumns([
        {
          title: <DragHandle />,
          dataIndex: 'sort',
          width: 1,
          className: 'drag-visible',
          render: (text, record) =>
            record.defaultRouting !== 1 && <DragHandle />,
        },
        ...columns,
      ]);
    } else {
      setColumns(columns.slice(1));
    }
  };
  // 取消排序
  const handleCancelSort = () => {
    setIsSort(false);
    setColumns(columns.slice(1));
    queryAiMatchList();
  };
  // 保存排序
  const handleSaveSort = () => {
    const newParams = tableDataSource.map(item => {
      return { routingId: item.routingId, routingSeq: item.routingSeq };
    });
    dispatch({
      type: 'allocation/saveRuleInfoSort',
      payload: newParams,
      callback: res => {
        setIsSort(false);
        setColumns(columns.slice(1));
        if (res.code === 200) {
          queryAiMatchList();
        }
      },
    });
  };

  const [columns, setColumns] = useState([
    {
      title: '',
      dataIndex: 'defaultRouting',
      key: 'defaultRouting',
      ellipsis: true,
      width: 2,
      render: (text, record) => {
        if (+record.defaultRouting !== 0) {
          return (
            <span>
              <FormattedMessage
                id="allocation.applicable.channel.types.for.default"
                defaultMessage="默认"
              />
            </span>
          );
        }
        return null;
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'allocation.execution.order',
        defaultValue: '执行顺序',
      }),
      dataIndex: 'routingSeq',
      key: 'routingSeq',
      ellipsis: true,
      width: 2.5,
    },
    {
      title: getIntl().formatMessage({
        id: 'allocation.applicable.channel.types.for.rules',
        defaultValue: '规则适用渠道类型',
      }),
      dataIndex: 'channelType',
      key: 'channelType',
      ellipsis: true,
      width: 6,
      render: (text, record) => {
        let routingChannelNameList = [];
        record.routingChannel?.forEach(item => {
          routingChannelNameList.push(item.routingChannelName);
        });
        if (record.channelType == '0') {
          return (
            <div>
              <img
                src={AllchannelIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span
                style={{
                  fontSize: '12px',
                  fontWeight: '700',
                  color: '#333',
                }}
              >
                <FormattedMessage
                  id="marketing.channel.type.all"
                  defaultMessage="所有渠道"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <>
                  <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
                  <span
                    style={{
                      marginLeft: '4px',
                      color: '#3463FC',
                      fontSize: '12px',
                      // cursor:'pointer',
                    }}
                    title={routingChannelNameList.join(',')}
                  >
                    {/*{record.routingChannel[0]?.routingChannelName}*/}
                    {routingChannelNameList.join(',')}
                    {/*<FormattedMessage*/}
                    {/*  id="allocation.more.rules.index.and.back"*/}
                    {/*  defaultMessage=""*/}
                    {/*/>*/}
                  </span>
                </>
              ) : (
                ''
              )}
            </div>
          );
        } else if (record.channelType == '1') {
          return (
            <div>
              <img
                src={TableEmailIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.email"
                  defaultMessage="邮件"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {/*{record.routingChannel[0]?.routingChannelName}*/}
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '7') {
          return (
            <div className={styles.channelName}>
              <img
                src={TablePhoneIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.phone"
                  defaultMessage="电话"
                />
              </span>
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '2') {
          return (
            <div className={styles.channelName}>
              <img
                src={TableInfoIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.info"
                  defaultMessage="短信"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}

              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '3') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewFaceBookIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.facebook"
                  defaultMessage="Facebook"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '4') {
          return (
            <div className={styles.channelName}>
              <img
                src={WhatsAppIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.whats.app"
                  defaultMessage="WhatsApp"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '8') {
          return (
            <div className={styles.channelName}>
              <img
                src={ChatIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.chat"
                  defaultMessage="Web在线聊天"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '9') {
          return (
            <div className={styles.channelName}>
              <img
                src={AppChatOutlinedIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.app.chat"
                  defaultMessage="App在线聊天"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/*  */}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '10') {
          return (
            <div className={styles.channelName}>
              <img
                src={WebVideoOutlinedIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.web.video"
                  defaultMessage="Web在线视频"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '11') {
          return (
            <div className={styles.channelName}>
              <img
                src={AppVideoOutlinedIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.app.video"
                  defaultMessage="App在线视频"
                />
              </span>

              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '12') {
          return (
            <div className={styles.channelName}>
              <img
                src={AwsChannelIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.amazon.message"
                  defaultMessage="亚马逊站内信"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '13') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewInstagramIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.instagram"
                  defaultMessage="Instagram"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '14') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewLineIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.line"
                  defaultMessage="Line"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '15') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewWeComIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.weCom"
                  defaultMessage="微信客服"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '16') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewWechatOfficialAccountIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.weChat.official.account"
                  defaultMessage="微信公众号"
                />
              </span>

              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}

              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '17') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewWebOnlineVoiceIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.web.online.video"
                  defaultMessage="WEB在线语音"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}

              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '18') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewAppOnlineVoiceIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.app.online.video"
                  defaultMessage="APP在线语音"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '19') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewTwitterIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.twitter"
                  defaultMessage="Twitter"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '20') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewTelegramIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.telegram"
                  defaultMessage="Telegram"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '21') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewWeChatMiniProgramIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.weChat.mini.program"
                  defaultMessage="微信小程序"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '22') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewShopifyIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.shopify"
                  defaultMessage="Shopify"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '23') {
          return (
            <div className={styles.channelName}>
              <img
                src={NewGooglePlayIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.google.play"
                  defaultMessage="Google Play"
                />
              </span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '24') {
          return (
            <div className={styles.channelName}>
              <img
                src={TikTokIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>TikTok Shop</span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else if (record.channelType == '25') {
          return (
            <div className={styles.channelName}>
              <img
                src={DiscordIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span>Discord</span>
              {record.routingChannel[0]?.routingChannelName ? (
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
              ) : (
                ''
              )}
              {/* <span style={{ color: '#333', marginLeft: '4px' }}>/</span> */}
              <span
                style={{
                  marginLeft: '4px',
                  color: '#3463FC',
                  fontSize: '12px',
                }}
                title={routingChannelNameList.join(',')}
              >
                {routingChannelNameList.join(',')}
              </span>
            </div>
          );
        } else {
          return (
            <div className={styles.channelName}>
              <span>--</span>
            </div>
          );
        }
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'allocation.rule.details',
        defaultValue: '规则详情',
      }),
      dataIndex: 'channelName',
      key: 'channelName',
      ellipsis: true,
      width: 10,
      render: (text, record) => {
        return (
          <>
            {+record.routingRule[0]?.judgmentRuleType === 2 &&
              record?.routingRule[0]?.uniquesList.length > 0 &&
              record?.routingRule[0]?.uniquesList.map((item, index) => {
                return (
                  <div key={index}>
                    {item.conditionType === 'ticket_type' ? (
                      <>
                        <FormattedMessage
                          id="allocation.more.rules.detail.work.order.when"
                          defaultMessage="当"
                        />
                        <span>
                          <FormattedMessage
                            id="allocation.more.rules.detail.work.order"
                            defaultMessage="工单类型"
                          />
                        </span>
                      </>
                    ) : item.conditionType === 'channel_name' ? (
                      <>
                        <FormattedMessage
                          id="allocation.more.rules.detail.work.order.when"
                          defaultMessage="当"
                        />
                        <span>
                          <FormattedMessage
                            id="allocation.more.rules.detail.chnannel.name"
                            defaultMessage="渠道名称"
                          />
                        </span>
                      </>
                    ) : item.conditionType === 'customer_label' ? (
                      <>
                        <FormattedMessage
                          id="allocation.more.rules.detail.work.order.when"
                          defaultMessage="当"
                        />
                        <span>
                          <FormattedMessage
                            id="allocation.more.rules.detail.customer.tag"
                            defaultMessage="客户标签"
                          />
                        </span>
                      </>
                    ) : item.conditionType === 'customer_level' ? (
                      <>
                        <FormattedMessage
                          id="allocation.more.rules.detail.work.order.when"
                          defaultMessage="当"
                        />
                        <span>
                          <FormattedMessage
                            id="allocation.more.rules.detail.customer.level"
                            defaultMessage="客户等级"
                          />
                        </span>
                      </>
                    ) : item.conditionType === 'customer_preferred_language' ? (
                      <>
                        <FormattedMessage
                          id="allocation.more.rules.detail.work.order.when"
                          defaultMessage="当"
                        />
                        <span>
                          <FormattedMessage
                            id="allocation.more.rules.detail.customer.language"
                            defaultMessage="客户语言"
                          />
                        </span>
                      </>
                    ) : item.conditionType === 'customer_email' ? (
                      <>
                        <FormattedMessage
                          id="allocation.more.rules.detail.work.order.when"
                          defaultMessage="当"
                        />
                        <span>
                          <FormattedMessage
                            id="allocation.more.rules.detail.customer.email"
                            defaultMessage="客户邮件"
                          />
                        </span>
                      </>
                    ) : item.conditionType === 'customer_country' ? (
                      <>
                        <FormattedMessage
                          id="allocation.more.rules.detail.work.order.when"
                          defaultMessage="当"
                        />
                        <span>
                          <FormattedMessage
                            id="allocation.more.rules.detail.customer.country"
                            defaultMessage="客户国家"
                          />
                        </span>
                      </>
                    ) : item.conditionType === 'customer_phone' ? (
                      <>
                        <FormattedMessage
                          id="allocation.more.rules.detail.work.order.when"
                          defaultMessage="当"
                        />
                        <span>
                          <FormattedMessage
                            id="allocation.more.rules.detail.customer.phone"
                            defaultMessage="客户手机号"
                          />
                        </span>
                      </>
                    ) : (
                      '---'
                    )}
                    <span>
                      <FormattedMessage
                        id="allocation.personal.include"
                        defaultMessage="包含"
                      />
                    </span>
                    <span>
                      {item.conditionValuesNameList &&
                      item.conditionValuesNameList.length > 0 ? (
                        <span>
                          {item.conditionValuesNameList.join(',')}
                          <FormattedMessage
                            id="allocation.more.rules.index.and.back"
                            defaultMessage=""
                          />
                        </span>
                      ) : (
                        '---'
                      )}
                      {index !==
                      record?.routingRule[0]?.uniquesList.length - 1 ? (
                        <FormattedMessage
                          id="allocation.more.rules.index.and"
                          defaultMessage="且"
                        />
                      ) : (
                        ''
                      )}
                    </span>
                  </div>
                );
              })}
            <span>
              <span>
                {+record.routingRule[0]?.distributionRuleType === 1
                  ? getIntl().formatMessage({
                      id: 'allocation.assign.to.specific.team.maohao.index',
                      defaultValue: '分配给特定团队',
                    })
                  : getIntl().formatMessage({
                      id: 'allocation.assign.to.specific.agent.maohao.index',
                      defaultValue: '分配给特定座席',
                    })}
              </span>
              <span
                style={{ color: '#3463FC', fontSize: '12px', fontWeight: 700 }}
              >
                {record.routingRule[0]?.distributionIdsNameList &&
                record.routingRule[0]?.distributionIdsNameList.length > 0
                  ? // ? record.routingRule[0]?.distributionIdsNameList[0]
                    record.routingRule[0]?.distributionIdsNameList.join(',')
                  : '--'}
              </span>
            </span>
            {+record.routingRule?.length > 1 ? (
              <div onClick={() => handleDetail(record)}>
                <span
                  style={{
                    color: '#3463FC',
                    fontSize: '12px',
                    cursor: 'pointer',
                  }}
                >
                  <FormattedMessage
                    id="allocation.more.rules"
                    defaultMessage="更多规则请「查看详情」"
                  />
                </span>
              </div>
            ) : (
              ''
            )}
          </>
        );
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'work.record.create.time',
        defaultValue: '创建时间',
      }),
      dataIndex: 'createTime',
      key: 'createTime',
      width: 4,
    },
    {
      title: getIntl().formatMessage({
        id: 'work.order.management.table.modify.time',
        defaultValue: '修改时间',
      }),
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 4,
    },

    {
      title: getIntl().formatMessage({
        id: 'alloctionRule.operation.operation',
        defaultValue: '操作',
      }),
      dataIndex: 'operation',
      key: 'operation',
      ellipsis: true,
      fixed: 'right',
      width: 7,
      render: (text, record) => {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <p
              onClick={() => handleEdit(record)}
              style={{
                marginRight: '8px',
                display: 'flex',
                alignItems: 'center',
                color: '#3463FC',
                marginBottom: 0,
                cursor: 'pointer',
              }}
            >
              <span style={{ marginRight: '8px', marginTop: '4px' }}>
                {Edit()}
              </span>
              <FormattedMessage
                style={{ marginLeft: '4px' }}
                id="alloctionRule.table.editor"
                defaultMessage="详情"
              />
            </p>
            <p
              onClick={() => handleDetail(record)}
              style={{
                marginRight: '8px',
                display: 'flex',
                alignItems: 'center',
                color: '#3463FC',
                marginBottom: 0,
                cursor: 'pointer',
              }}
            >
              <span style={{ marginRight: '8px', marginTop: '4px' }}>
                {TableDetailIcon()}
              </span>
              <FormattedMessage
                style={{ marginLeft: '4px' }}
                id="alloctionRule.table.detail"
                defaultMessage="修改"
              />
            </p>
            {+record.defaultRouting !== 1 ? (
              <>
                <Popconfirm
                  // title="返回将清空表单，确定返回么？"
                  title={getIntl().formatMessage({
                    id: 'delete.channel.confirm.rule',
                    defaultValue: '确定删除么？',
                  })}
                  onConfirm={() => handleDelete(record)}
                  // onOpenChange={() => console.log('open change')}
                  disabled={record.enableStatus === 0}
                  style={{
                    marginRight: '20px',
                  }}
                >
                  <p
                    // onClick={() => handleDelete(record)}
                    style={{
                      marginRight: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      color: '#F22417',
                      marginBottom: 0,
                      cursor: 'pointer',
                    }}
                  >
                    <span style={{ marginRight: '8px', marginTop: '4px' }}>
                      {Delete()}
                    </span>
                    <FormattedMessage
                      style={{ marginLeft: '4px' }}
                      id="alloctionRule.table.delete"
                      defaultMessage="删除"
                    />
                  </p>
                </Popconfirm>
              </>
            ) : (
              ''
            )}
          </div>
        );
      },
    },
  ]);

  return (
    <div className={styles.wrapperContent}>
      <Spin spinning={loading}>
        <div className={styles.header}>
          <div style={{ display: 'flex' }}>
            <span
              style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
              onClick={() => history.push('/settingTicket')}
            >
              {BackIcon()}
            </span>
            <FormattedMessage
              id="automation.config.smart.routing"
              defaultMessage="智能分配规则"
            />
          </div>
          <div className={styles.btnBox}>
            {/* 拖拽后取消保存 */}
            {isSort ? (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Button
                  onClick={handleCancelSort}
                  style={{
                    borderRadius: '4px',
                    display: 'flex',
                    alignContent: 'center',
                    alignItems: 'center',
                    marginRight: '20px',
                  }}
                  className={styles.allocationRuleStyle}
                >
                  <FormattedMessage id="channel.cancel" defaultMessage="取消" />
                </Button>
                <Button
                  onClick={handleSaveSort}
                  type="primary"
                  style={{
                    borderRadius: '4px',
                    display: 'flex',
                    alignContent: 'center',
                    alignItems: 'center',
                    marginRight: '20px',
                  }}
                  className={styles.allocationRuleAdd}
                >
                  <FormattedMessage id="channel.save" defaultMessage="保存" />
                </Button>
              </div>
            ) : (
              <Button
                onClick={handleSort}
                icon={
                  <Reorder style={{ fontSize: '16px', marginRight: '2px' }} />
                }
                style={{
                  borderRadius: '4px',
                  display: 'flex',
                  alignContent: 'center',
                  alignItems: 'center',
                  marginRight: '20px',
                }}
                className={styles.allocationRuleStyle}
              >
                <span style={{ marginLeft: '2px' }}>
                  <FormattedMessage
                    id="allocation.adjust.order"
                    defaultMessage="调整顺序"
                  />
                </span>
              </Button>
            )}

            {!isSort ? (
              <HOCAuth authKey={'save_rule_info'}>
                {authAccess => (
                  <Button
                    onClick={handleAddAlloctionRule}
                    type="primary"
                    icon={<Add style={{ fontSize: '16px' }} />}
                    style={{
                      borderRadius: '4px',
                      display: 'flex',
                      alignContent: 'center',
                      alignItems: 'center',
                    }}
                    className={styles.allocationRuleAdd}
                    disabled={authAccess}
                    title={
                      authAccess
                        ? getIntl().formatMessage({
                            id: 'auth.access.no.1',
                            defaultValue: '您的当前版本不支持此功能',
                          })
                        : ''
                    }
                  >
                    <FormattedMessage
                      id="allocation.create.a.new.matching.rule"
                      defaultMessage="新建匹配规则"
                    ></FormattedMessage>
                  </Button>
                )}
              </HOCAuth>
            ) : (
              ''
            )}
          </div>
        </div>

        {/* 筛选标签页 */}
        <div className={styles.filterTabsContainer}>
          <Tabs
            activeKey={activeTabKey}
            onChange={handleTabChange}
            className={styles.filterTabs}
            items={[
              {
                key: '1',
                label: (
                  <div className={styles.tabLabel}>
                    <span className={styles.tabCount}>{tabCounts.phone}</span>
                    <span>电话</span>
                  </div>
                ),
              },
              {
                key: '2',
                label: (
                  <div className={styles.tabLabel}>
                    <span className={styles.tabCount}>{tabCounts.chat}</span>
                    <span>聊天</span>
                  </div>
                ),
              },
              {
                key: '3',
                label: (
                  <div className={styles.tabLabel}>
                    <span className={styles.tabCount}>{tabCounts.email}</span>
                    <span>邮件</span>
                  </div>
                ),
              },
            ]}
          />
        </div>

        <div>
          <Table
            dataSource={tableDataSource}
            columns={columns}
            scroll={{ x: 1350 }}
            className="ruleStyle"
            rowKey="routingId"
            components={{
              body: {
                wrapper: DraggableContainer,
                row: DraggableBodyRow,
              },
            }}
            pagination={{
              total: total,
              pageSize: pageSize,
              current: pageNum,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showTotal: total => (
                <FormattedMessage
                  id="page.total.num"
                  defaultMessage={`共 ${total} 条`}
                  values={{ total }}
                />
              ),
              onChange: (pageNum, pageSize) => {
                let params = {
                  pageNum: pageNum,
                  pageSize: pageSize,
                };
                setSelectItem(params);
                setPageNum(pageNum);
                setPageSize(pageSize);
              },
            }}
          />
        </div>
      </Spin>
    </div>
  );
};

export default AllocationRules;
