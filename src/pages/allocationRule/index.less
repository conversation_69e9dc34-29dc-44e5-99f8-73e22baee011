.wrapperContent {
  //width: 100%;
  // max-height: 100vh;
  border-radius: 4px;
  // background: #fff;
  // box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  margin: 20px;
  // padding: 20px;
  overflow: hidden;
  overflow-y: scroll;

  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    font-size: 18px;

    .btnBox {
      display: flex;
      justify-self: center;
      align-items: center;

      .allocationRuleStyle:hover {
        background-color: #f5f5f5;
        // color: red !important;
      }

      .allocationRuleAdd:hover {
        background-color: #4873fc;
      }
    }
  }

  :global {
    .ant-table-thead>tr>th {
      // background-color: #f3f7fe !important;
      background-color: #ffffff !important;
      font-weight: 700;
    }

    .ant-table-tbody {
      background-color: #fcfbff;
    }
  }

  .operationContent {
    display: flex;
    align-items: center;

    // .details {
    //   margin-right: 8px;
    //   display: flex;
    //   align-items: center;

    //   svg {
    //     margin-right: 4px;
    //   }
    // }

    // .details {
    //   float: left;
    //   color: #3463fc;
    //   font-size: 12px;
    //   margin-bottom: 0px;
    //   cursor: pointer;
    //   margin-left: 10px;

    //   img {
    //     width: 12px;
    //     float: left;
    //     margin-right: 4px;
    //     margin-top: 3px;
    //   }
    // }
  }

  // .channelName {
  //   img {
  //     width: 12px;
  //     height: 12px;
  //     float: left;
  //     margin-top: 3px;
  //     margin-right: 5px;
  //   }
  // }
  // .sortItem {
  //   .operationContent {
  //     display: flex;
  //     align-items: center;

  //     .details {
  //       margin-right: 8px;
  //       display: flex;
  //       align-items: center;

  //       svg {
  //         margin-right: 4px;
  //       }
  //     }

  //     .details {
  //       float: left;
  //       color: #3463fc;
  //       font-size: 12px;
  //       margin-bottom: 0px;
  //       cursor: pointer;
  //       margin-left: 10px;

  //       img {
  //         width: 12px;
  //         float: left;
  //         margin-right: 4px;
  //         margin-top: 3px;
  //       }
  //     }
  //   }

  // .channelName {
  //   img {
  //     width: 12px;
  //     height: 12px;
  //     float: left;
  //     margin-top: 3px;
  //     margin-right: 5px;
  //   }
  // }
  // }

  // 筛选标签页样式
  .filterTabsContainer {
    margin-bottom: 20px;
    border-bottom: 1px solid #e8e8e8;
    .filterTabs {
      :global {
        .ant-tabs-nav {
          margin-bottom: 0;
        }

        .ant-tabs-tab {
          margin-right: 32px;
          position: relative;
          padding-bottom:10px !important;

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: #1890ff;
              font-weight: 500;
            }

            // 自定义蓝色下划线
            &::after {
              content: '';
              position: absolute;
              bottom: -1px;
              left: 0;
              right: 0;
              height: 2px;
              background-color: #1890ff;
              border-radius: 1px;
            }
          }
        }

        .ant-tabs-ink-bar {
          display: none; // 隐藏默认的下划线，使用自定义的
        }

        .ant-tabs-content-holder {
          display: none; // 隐藏内容区域，只显示标签
        }
      }
    }

    .tabLabel {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      font-weight: 500;
      color: #999;
      .tabCount {
        padding: 4px;
        text-align: center;
        font-family: "Microsoft YaHei";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        border-radius: 4px;

        // 电话类型样式
        &.phoneCount {
          color: #13C825;
          background: rgba(19, 200, 37, 0.20);
        }

        // 聊天类型样式
        &.chatCount {
          color: #3463FC;
          background: rgba(52, 99, 252, 0.20);
        }

        // 邮件类型样式
        &.emailCount {
          color: #8500BB;
          background: rgba(133, 0, 187, 0.20);
        }
      }
    }

    
  }
}
