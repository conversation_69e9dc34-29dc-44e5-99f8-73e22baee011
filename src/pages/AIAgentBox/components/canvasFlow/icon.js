export const BackIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M1.8525 9.32188L8.95375 2.54375C9.01313 2.48707 9.08309 2.44264 9.15964 2.413C9.2362 2.38336 9.31783 2.36909 9.3999 2.371C9.48197 2.37292 9.56285 2.39098 9.63794 2.42416C9.71302 2.45734 9.78084 2.50499 9.8375 2.56438L10.2688 3.01625C10.374 3.12631 10.4354 3.27098 10.4415 3.42312C10.4476 3.57525 10.3979 3.72438 10.3019 3.8425L10.2481 3.9L4.83938 9.0625H17.8125C17.9783 9.0625 18.1372 9.12835 18.2544 9.24556C18.3717 9.36277 18.4375 9.52174 18.4375 9.6875V10.3125C18.4375 10.4783 18.3717 10.6372 18.2544 10.7544C18.1372 10.8717 17.9783 10.9375 17.8125 10.9375H4.83938L10.2481 16.1C10.3583 16.2051 10.4263 16.3467 10.4394 16.4984C10.4526 16.6501 10.4099 16.8013 10.3194 16.9238L10.2694 16.9838L9.8375 17.4356C9.78084 17.495 9.71302 17.5427 9.63794 17.5758C9.56285 17.609 9.48197 17.6271 9.3999 17.629C9.31783 17.6309 9.2362 17.6166 9.15964 17.587C9.08309 17.5574 9.01313 17.5129 8.95375 17.4563L1.8525 10.6781C1.7608 10.5906 1.68781 10.4854 1.63793 10.3688C1.58806 10.2522 1.56235 10.1268 1.56235 10C1.56235 9.87323 1.58806 9.74776 1.63793 9.63121C1.68781 9.51465 1.7608 9.40942 1.8525 9.32188Z"
      fill="#3463FC"
    />
  </svg>
);
export const EditGrayIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M8.92948 2.14156C9.02425 2.05126 9.15064 2.00174 9.28152 2.00361C9.4124 2.00548 9.53733 2.05859 9.62948 2.15156L12.3545 4.90006C12.4026 4.94853 12.4403 5.00634 12.4652 5.06994C12.4901 5.13354 12.5017 5.20156 12.4993 5.26982C12.497 5.33808 12.4806 5.40513 12.4513 5.46683C12.422 5.52853 12.3804 5.58357 12.329 5.62856L5.76298 11.3796C5.66911 11.4619 5.54782 11.5061 5.42298 11.5036L2.98898 11.4516C2.8582 11.4488 2.73371 11.3949 2.6422 11.3014C2.55069 11.208 2.49945 11.0824 2.49948 10.9516V8.47956C2.49947 8.41187 2.5132 8.34488 2.53984 8.28265C2.56649 8.22043 2.60549 8.16426 2.65448 8.11756L8.92948 2.14156ZM9.59298 10.1806L13.3405 10.2536C13.4061 10.2548 13.4709 10.269 13.5311 10.2953C13.5913 10.3217 13.6457 10.3596 13.6912 10.4069C13.7367 10.4542 13.7725 10.5101 13.7964 10.5712C13.8203 10.6324 13.832 10.6976 13.8307 10.7633C13.8295 10.829 13.8152 10.8937 13.7889 10.9539C13.7626 11.0141 13.7247 11.0685 13.6774 11.114C13.6301 11.1595 13.5742 11.1953 13.5131 11.2192C13.4519 11.2432 13.3866 11.2548 13.321 11.2536L9.57348 11.1806C9.50782 11.1792 9.44306 11.165 9.3829 11.1387C9.32274 11.1123 9.26836 11.0744 9.22286 11.027C9.17736 10.9797 9.14163 10.9238 9.11771 10.8627C9.0938 10.8015 9.08217 10.7362 9.08348 10.6706C9.08479 10.6049 9.09903 10.5401 9.12537 10.48C9.15171 10.4198 9.18964 10.3654 9.237 10.3199C9.28436 10.2744 9.34021 10.2387 9.40138 10.2148C9.46254 10.1909 9.52782 10.1792 9.59348 10.1806H9.59298ZM9.26448 3.20356L3.49948 8.69356V10.4621L5.24948 10.4996L11.27 5.22656L9.26448 3.20356ZM13.316 12.6806C13.4486 12.6796 13.5762 12.7313 13.6706 12.8244C13.7651 12.9174 13.8187 13.0442 13.8197 13.1768C13.8207 13.3094 13.769 13.437 13.6759 13.5315C13.5829 13.6259 13.4561 13.6796 13.3235 13.6806L3.50348 13.7536C3.37087 13.7546 3.2433 13.7028 3.14883 13.6098C3.05436 13.5167 3.00073 13.3899 2.99973 13.2573C2.99874 13.1247 3.05046 12.9971 3.14353 12.9027C3.23659 12.8082 3.36337 12.7546 3.49598 12.7536L13.316 12.6806Z"
      fill="#999999"
    />
  </svg>
);
export const TipsIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <g clip-path="url(#clip0_20746_4100)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.71195 9.28805C3.584 10.1601 4.76674 10.65 6 10.65C7.23326 10.65 8.416 10.1601 9.28805 9.28805C10.1601 8.416 10.65 7.23326 10.65 6C10.65 4.76674 10.1601 3.584 9.28805 2.71195C8.416 1.83991 7.23326 1.35 6 1.35C4.76674 1.35 3.584 1.83991 2.71195 2.71195C1.83991 3.584 1.35 4.76674 1.35 6C1.35 7.23326 1.83991 8.416 2.71195 9.28805ZM8.00909 10.8504C7.37213 11.1142 6.68944 11.25 6 11.25C4.60761 11.25 3.27226 10.6969 2.28769 9.71231C1.30312 8.72774 0.75 7.39239 0.75 6C0.75 4.60761 1.30312 3.27226 2.28769 2.28769C3.27226 1.30312 4.60761 0.75 6 0.75C6.68944 0.75 7.37213 0.885795 8.00909 1.14963C8.64605 1.41347 9.2248 1.80018 9.71231 2.28769C10.1998 2.7752 10.5865 3.35395 10.8504 3.99091C11.1142 4.62787 11.25 5.31056 11.25 6C11.25 6.68944 11.1142 7.37213 10.8504 8.00909C10.5865 8.64605 10.1998 9.2248 9.71231 9.71231C9.2248 10.1998 8.64605 10.5865 8.00909 10.8504ZM6 3.3C6.07957 3.3 6.15587 3.33161 6.21213 3.38787C6.26839 3.44413 6.3 3.52043 6.3 3.6V6.6C6.3 6.67957 6.26839 6.75587 6.21213 6.81213C6.15587 6.86839 6.07956 6.9 6 6.9C5.92044 6.9 5.84413 6.86839 5.78787 6.81213C5.73161 6.75587 5.7 6.67957 5.7 6.6V3.6C5.7 3.52044 5.73161 3.44413 5.78787 3.38787C5.84413 3.33161 5.92043 3.3 6 3.3ZM6.17225 8.81577C6.11765 8.83838 6.05912 8.85001 6.00002 8.85C5.88069 8.84998 5.76625 8.80256 5.68187 8.71817C5.59749 8.63378 5.55009 8.51934 5.55009 8.4C5.55009 8.28066 5.59749 8.16622 5.68187 8.08183C5.76625 7.99744 5.88069 7.95002 6.00002 7.95C6.05912 7.94999 6.11765 7.96162 6.17225 7.98423C6.22686 8.00684 6.27647 8.03999 6.31827 8.08178C6.36006 8.12356 6.39321 8.17317 6.41583 8.22778C6.43845 8.28238 6.45009 8.3409 6.45009 8.4C6.45009 8.4591 6.43845 8.51762 6.41583 8.57222C6.39321 8.62682 6.36006 8.67644 6.31827 8.71822C6.27647 8.76001 6.22686 8.79316 6.17225 8.81577Z"
        fill="#FCB830"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.71195 9.28805C3.584 10.1601 4.76674 10.65 6 10.65C7.23326 10.65 8.416 10.1601 9.28805 9.28805C10.1601 8.416 10.65 7.23326 10.65 6C10.65 4.76674 10.1601 3.584 9.28805 2.71195C8.416 1.83991 7.23326 1.35 6 1.35C4.76674 1.35 3.584 1.83991 2.71195 2.71195C1.83991 3.584 1.35 4.76674 1.35 6C1.35 7.23326 1.83991 8.416 2.71195 9.28805ZM8.00909 10.8504C7.37213 11.1142 6.68944 11.25 6 11.25C4.60761 11.25 3.27226 10.6969 2.28769 9.71231C1.30312 8.72774 0.75 7.39239 0.75 6C0.75 4.60761 1.30312 3.27226 2.28769 2.28769C3.27226 1.30312 4.60761 0.75 6 0.75C6.68944 0.75 7.37213 0.885795 8.00909 1.14963C8.64605 1.41347 9.2248 1.80018 9.71231 2.28769C10.1998 2.7752 10.5865 3.35395 10.8504 3.99091C11.1142 4.62787 11.25 5.31056 11.25 6C11.25 6.68944 11.1142 7.37213 10.8504 8.00909C10.5865 8.64605 10.1998 9.2248 9.71231 9.71231C9.2248 10.1998 8.64605 10.5865 8.00909 10.8504ZM6 3.3C6.07957 3.3 6.15587 3.33161 6.21213 3.38787C6.26839 3.44413 6.3 3.52043 6.3 3.6V6.6C6.3 6.67957 6.26839 6.75587 6.21213 6.81213C6.15587 6.86839 6.07956 6.9 6 6.9C5.92044 6.9 5.84413 6.86839 5.78787 6.81213C5.73161 6.75587 5.7 6.67957 5.7 6.6V3.6C5.7 3.52044 5.73161 3.44413 5.78787 3.38787C5.84413 3.33161 5.92043 3.3 6 3.3ZM6.17225 8.81577C6.11765 8.83838 6.05912 8.85001 6.00002 8.85C5.88069 8.84998 5.76625 8.80256 5.68187 8.71817C5.59749 8.63378 5.55009 8.51934 5.55009 8.4C5.55009 8.28066 5.59749 8.16622 5.68187 8.08183C5.76625 7.99744 5.88069 7.95002 6.00002 7.95C6.05912 7.94999 6.11765 7.96162 6.17225 7.98423C6.22686 8.00684 6.27647 8.03999 6.31827 8.08178C6.36006 8.12356 6.39321 8.17317 6.41583 8.22778C6.43845 8.28238 6.45009 8.3409 6.45009 8.4C6.45009 8.4591 6.43845 8.51762 6.41583 8.57222C6.39321 8.62682 6.36006 8.67644 6.31827 8.71822C6.27647 8.76001 6.22686 8.79316 6.17225 8.81577Z"
        fill="#F22417"
      />
    </g>
    <defs>
      <clipPath id="clip0_20746_4100">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const TipsBackIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M9.99996 18.3334C14.6023 18.3334 18.3333 14.6024 18.3333 10C18.3333 5.39765 14.6023 1.66669 9.99996 1.66669C5.39759 1.66669 1.66663 5.39765 1.66663 10C1.66663 14.6024 5.39759 18.3334 9.99996 18.3334Z"
      fill="#E6A23C"
    />
    <path
      d="M10 6.66669V10"
      stroke="white"
      stroke-width="1.25"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10 13.3333H10.0083"
      stroke="white"
      stroke-width="1.25"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const TipsSuccessIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <g clip-path="url(#clip0_18143_3615)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.71195 9.28805C3.584 10.1601 4.76674 10.65 6 10.65C7.23326 10.65 8.416 10.1601 9.28805 9.28805C10.1601 8.416 10.65 7.23326 10.65 6C10.65 4.76674 10.1601 3.584 9.28805 2.71195C8.416 1.83991 7.23326 1.35 6 1.35C4.76674 1.35 3.584 1.83991 2.71195 2.71195C1.83991 3.584 1.35 4.76674 1.35 6C1.35 7.23326 1.83991 8.416 2.71195 9.28805ZM8.00909 10.8504C7.37213 11.1142 6.68944 11.25 6 11.25C4.60761 11.25 3.27226 10.6969 2.28769 9.71231C1.30312 8.72774 0.75 7.39239 0.75 6C0.75 4.60761 1.30312 3.27226 2.28769 2.28769C3.27226 1.30312 4.60761 0.75 6 0.75C6.68944 0.75 7.37213 0.885795 8.00909 1.14963C8.64605 1.41347 9.2248 1.80018 9.71231 2.28769C10.1998 2.7752 10.5865 3.35395 10.8504 3.99091C11.1142 4.62787 11.25 5.31056 11.25 6C11.25 6.68944 11.1142 7.37213 10.8504 8.00909C10.5865 8.64605 10.1998 9.2248 9.71231 9.71231C9.2248 10.1998 8.64605 10.5865 8.00909 10.8504ZM6 3.3C6.07957 3.3 6.15587 3.33161 6.21213 3.38787C6.26839 3.44413 6.3 3.52043 6.3 3.6V6.6C6.3 6.67957 6.26839 6.75587 6.21213 6.81213C6.15587 6.86839 6.07956 6.9 6 6.9C5.92044 6.9 5.84413 6.86839 5.78787 6.81213C5.73161 6.75587 5.7 6.67957 5.7 6.6V3.6C5.7 3.52044 5.73161 3.44413 5.78787 3.38787C5.84413 3.33161 5.92043 3.3 6 3.3ZM6.17225 8.81577C6.11765 8.83838 6.05912 8.85001 6.00002 8.85C5.88069 8.84998 5.76625 8.80256 5.68187 8.71817C5.59749 8.63378 5.55009 8.51934 5.55009 8.4C5.55009 8.28066 5.59749 8.16622 5.68187 8.08183C5.76625 7.99744 5.88069 7.95002 6.00002 7.95C6.05912 7.94999 6.11765 7.96162 6.17225 7.98423C6.22686 8.00684 6.27647 8.03999 6.31827 8.08178C6.36006 8.12356 6.39321 8.17317 6.41583 8.22778C6.43845 8.28238 6.45009 8.3409 6.45009 8.4C6.45009 8.4591 6.43845 8.51762 6.41583 8.57222C6.39321 8.62682 6.36006 8.67644 6.31827 8.71822C6.27647 8.76001 6.22686 8.79316 6.17225 8.81577Z"
        fill="#13C825"
      />
    </g>
    <defs>
      <clipPath id="clip0_18143_3615">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const WithDrawIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M8.20746 4.62335H2.03938L5.03882 1.95726C5.18401 1.82821 5.19709 1.60592 5.06803 1.46073C4.93899 1.31558 4.71673 1.30245 4.57153 1.43152L0.895594 4.69893C0.814025 4.76334 0.761597 4.86304 0.761597 4.97505C0.761597 4.9752 0.761609 4.97536 0.761609 4.97551C0.761609 4.97566 0.761597 4.97581 0.761597 4.97596C0.761597 4.98151 0.761777 4.98705 0.76204 4.99258C0.762052 4.99277 0.762052 4.99296 0.762052 4.99315C0.766657 5.08727 0.808845 5.17591 0.879645 5.23881L4.57152 8.52039C4.63852 8.57997 4.72194 8.60923 4.80503 8.60923C4.90199 8.60923 4.99855 8.56934 5.06802 8.49117C5.19707 8.34601 5.18399 8.1237 5.03881 7.99467L2.0373 5.32672H8.20744C9.48736 5.32672 10.5286 6.368 10.5286 7.64791C10.5286 8.92783 9.48736 9.96911 8.20744 9.96911H4.15223C3.95799 9.96911 3.80053 10.1266 3.80053 10.3208C3.80053 10.515 3.95799 10.6725 4.15223 10.6725H8.20746C9.87523 10.6725 11.232 9.31567 11.232 7.64791C11.232 5.98016 9.87521 4.62335 8.20746 4.62335Z"
      fill="#3463FC"
    />
  </svg>
);

export const ForWardIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M3.79254 4.62335H9.96062L6.96118 1.95726C6.81599 1.82821 6.80291 1.60592 6.93197 1.46073C7.06101 1.31558 7.28327 1.30245 7.42847 1.43152L11.1044 4.69893C11.186 4.76334 11.2384 4.86304 11.2384 4.97505C11.2384 4.9752 11.2384 4.97536 11.2384 4.97551C11.2384 4.97566 11.2384 4.97581 11.2384 4.97596C11.2384 4.98151 11.2382 4.98705 11.238 4.99258C11.2379 4.99277 11.2379 4.99296 11.2379 4.99315C11.2333 5.08727 11.1912 5.17591 11.1204 5.23881L7.42848 8.52039C7.36148 8.57997 7.27806 8.60923 7.19497 8.60923C7.09801 8.60923 7.00145 8.56934 6.93198 8.49117C6.80293 8.34601 6.81601 8.1237 6.96119 7.99467L9.9627 5.32672H3.79256C2.51264 5.32672 1.47136 6.368 1.47136 7.64791C1.47136 8.92783 2.51264 9.96911 3.79256 9.96911H7.84777C8.04201 9.96911 8.19947 10.1266 8.19947 10.3208C8.19947 10.515 8.04201 10.6725 7.84777 10.6725H3.79254C2.12477 10.6725 0.767954 9.31567 0.767954 7.64791C0.767954 5.98016 2.12479 4.62335 3.79254 4.62335Z"
      fill="#3463FC"
    />
  </svg>
);
export const VarControlsIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M4.67433 2V3.27544H3.77011C3.18774 3.27544 2.91188 3.60664 2.91188 4.2997V8.26224C2.91188 9.23882 2.46743 9.90038 1.59387 10.2307C2.46743 10.6088 2.91188 11.255 2.91188 12.2001V16.1779C2.91188 16.8395 3.18774 17.186 3.77011 17.186H4.67433V18.4623H3.49425C2.81992 18.4623 2.29885 18.2256 1.93103 17.7846C1.59387 17.3759 1.42529 16.808 1.42529 16.1158V12.2784C1.42529 11.8221 1.33333 11.4908 1.14943 11.2865C0.934866 11.0345 0.551724 10.9085 0 10.877V9.58535C0.551724 9.55385 0.934866 9.41166 1.14943 9.17582C1.33333 8.95445 1.42529 8.62409 1.42529 8.18306V4.36271C1.42529 3.65432 1.59387 3.08727 1.93103 2.67688C2.29885 2.22052 2.81992 2 3.49425 2H4.67433ZM16.5057 2C17.1801 2 17.7012 2.22052 18.069 2.67774C18.4061 3.08642 18.5747 3.65432 18.5747 4.36271V8.18306C18.5747 8.62409 18.6667 8.9553 18.8506 9.17582C19.0651 9.41166 19.4483 9.55385 20 9.5845V10.877C19.4483 10.9085 19.0651 11.0345 18.8506 11.2857C18.6667 11.4908 18.5747 11.8221 18.5747 12.2784V16.1149C18.5747 16.808 18.4061 17.3751 18.069 17.7846C17.7012 18.2256 17.1801 18.4623 16.5057 18.4623H15.3257V17.186H16.2299C16.8123 17.186 17.0881 16.8395 17.0881 16.1779V12.2001C17.0881 11.255 17.5326 10.6088 18.4061 10.2307C17.5326 9.90038 17.0881 9.23882 17.0881 8.26224V4.29885C17.0881 3.60579 16.8123 3.27458 16.2299 3.27458H15.3257V2H16.5057ZM8.36611 5.50021L10.2333 8.27586L12.0834 5.50021H14.1141L11.1835 9.59642L14.4742 14.2512H12.4436L10.2333 10.917L8.00681 14.2512H5.95998L9.26777 9.59642L6.33631 5.50106H8.36697L8.36611 5.50021Z"
      fill="#3463FC"
    />
  </svg>
);
export const VarApiIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M2.04499 12.9219L4.45813 6.47225C4.52991 6.1964 4.69039 5.95181 4.91485 5.77612C5.13931 5.60044 5.4153 5.50342 5.70031 5.5C6.29391 5.52124 6.81128 5.90865 6.99998 6.47225L9.28565 12.9219C9.30439 13.0781 9.33813 13.2305 9.38562 13.3805C9.38562 13.8516 9.07195 14.0803 8.54334 14.0803C8.35605 14.1028 8.16676 14.0595 8.00787 13.9578C7.84898 13.8562 7.73036 13.7024 7.67231 13.523L7.34364 12.407H4.08697L3.71582 13.523C3.64668 13.6981 3.52351 13.8468 3.36419 13.9472C3.20486 14.0476 3.01768 14.0947 2.82979 14.0816C2.72421 14.0992 2.616 14.0928 2.51322 14.0629C2.41044 14.033 2.31572 13.9803 2.23611 13.9087C2.1565 13.8372 2.09404 13.7486 2.05338 13.6496C2.01272 13.5505 1.99491 13.4436 2.00125 13.3368V13.0081C2.02648 12.9861 2.04217 12.9552 2.04499 12.9219ZM5.69906 7.35828L4.58684 10.7912H6.81503L5.69906 7.35828ZM9.97297 13.2518V6.57223C9.95781 6.4522 9.96952 6.3303 10.0073 6.21535C10.045 6.1004 10.1078 5.99529 10.1912 5.90761C10.2746 5.81994 10.3764 5.7519 10.4893 5.70842C10.6022 5.66494 10.7234 5.6471 10.844 5.65621H12.986C13.3734 5.6365 13.7609 5.696 14.1246 5.83106C14.4883 5.96612 14.8206 6.17392 15.1013 6.44175C15.382 6.70959 15.6052 7.03184 15.7571 7.38881C15.9091 7.74579 15.9867 8.13001 15.9852 8.51798C15.9975 8.90516 15.9287 9.29064 15.7832 9.64964C15.6376 10.0086 15.4186 10.3332 15.1401 10.6025C14.8616 10.8718 14.5299 11.0798 14.1662 11.2133C13.8026 11.3467 13.415 11.4026 13.0284 11.3773H11.7288V13.2518C11.7359 13.3674 11.7175 13.4832 11.6748 13.5909C11.632 13.6986 11.5661 13.7955 11.4816 13.8748C11.397 13.954 11.2961 14.0137 11.1859 14.0494C11.0757 14.0851 10.9589 14.0961 10.844 14.0816C10.2579 14.0816 9.97297 13.8091 9.97297 13.2518ZM11.7288 7.21581V9.71892H12.9285C13.0886 9.72515 13.2483 9.69841 13.3976 9.64037C13.547 9.58232 13.6829 9.4942 13.7967 9.38148C13.9106 9.26875 14.0002 9.13383 14.0598 8.98508C14.1193 8.83632 14.1477 8.67691 14.1432 8.51673C14.1468 8.35414 14.1182 8.19244 14.059 8.04097C13.9998 7.88951 13.9111 7.75127 13.7982 7.63426C13.6852 7.51725 13.5502 7.42378 13.401 7.35925C13.2517 7.29472 13.0911 7.26041 12.9285 7.2583L11.7288 7.21581ZM16.5425 13.2518V6.47225C16.5274 6.35041 16.5395 6.22674 16.578 6.11014C16.6164 5.99354 16.6803 5.88692 16.7649 5.79798C16.8495 5.70904 16.9529 5.63999 17.0674 5.59581C17.182 5.55163 17.3049 5.53342 17.4273 5.54249C17.5481 5.5377 17.6685 5.55896 17.7803 5.60482C17.8921 5.65067 17.9927 5.72006 18.0754 5.80826C18.158 5.89646 18.2207 6.00141 18.2592 6.11598C18.2976 6.23055 18.311 6.35206 18.2983 6.47225V13.2505C18.3077 13.3655 18.2912 13.4811 18.2502 13.5888C18.2091 13.6966 18.1444 13.7938 18.0609 13.8733C17.9774 13.9529 17.8772 14.0128 17.7676 14.0486C17.6579 14.0844 17.5417 14.0952 17.4273 14.0803C16.8425 14.0803 16.5425 13.8079 16.5425 13.2505V13.2518Z"
      fill="#3463FC"
    />
  </svg>
);
export const VarHorizontalIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M6.09727 2.70904L6.24977 2.71029H6.40227C6.69852 2.70654 7.08977 2.70279 7.44602 2.77529C7.90602 2.86904 8.39227 3.10279 8.70727 3.64779C8.85477 3.90404 8.90977 4.17529 8.93477 4.45029C8.95852 4.70904 8.95852 5.02529 8.95852 5.39154V9.37529H11.041V8.72529C11.041 8.35904 11.041 8.04279 11.0648 7.78279C11.0898 7.50904 11.1448 7.23779 11.2923 6.98154C11.6073 6.43654 12.0923 6.20279 12.5535 6.10904C12.9098 6.03654 13.2998 6.04029 13.5973 6.04279C13.6989 6.04377 13.8006 6.04377 13.9023 6.04279C14.1985 6.04029 14.5898 6.03654 14.946 6.10904C15.406 6.20279 15.8923 6.43654 16.206 6.98154C16.3548 7.23779 16.4098 7.50904 16.4348 7.78279C16.4573 8.04279 16.4573 8.35779 16.4573 8.72529V9.37529H18.3323C18.498 9.37529 18.657 9.44114 18.7742 9.55835C18.8914 9.67556 18.9573 9.83453 18.9573 10.0003C18.9573 10.166 18.8914 10.325 18.7742 10.4422C18.657 10.5594 18.498 10.6253 18.3323 10.6253H16.4573V11.279C16.4573 11.6453 16.4573 11.9615 16.4348 12.2215C16.4191 12.5022 16.3414 12.7758 16.2073 13.0228C15.8923 13.5678 15.4073 13.8015 14.9448 13.8965C14.5898 13.9678 14.1985 13.964 13.9023 13.9615C13.8006 13.9604 13.6989 13.9604 13.5973 13.9615C13.2998 13.964 12.9098 13.9678 12.5535 13.8953C12.0923 13.8015 11.6073 13.5678 11.2923 13.0228C11.1581 12.7758 11.0804 12.5022 11.0648 12.2215C11.041 11.9615 11.041 11.6465 11.041 11.279V10.6253H8.95852V14.6128C8.95852 14.979 8.95852 15.2953 8.93352 15.554C8.90852 15.829 8.85477 16.1003 8.70727 16.3565C8.39227 16.9015 7.90727 17.1353 7.44477 17.229C7.08977 17.3015 6.69852 17.2978 6.40227 17.2953L6.24977 17.294H6.09727C5.79977 17.2978 5.40977 17.3015 5.05352 17.229C4.59227 17.1353 4.10727 16.9015 3.79227 16.3565C3.658 16.1092 3.5803 15.8351 3.56477 15.554C3.54102 15.2953 3.54102 14.979 3.54102 14.6128V10.6253H1.66602C1.50026 10.6253 1.34128 10.5594 1.22407 10.4422C1.10686 10.325 1.04102 10.166 1.04102 10.0003C1.04102 9.83453 1.10686 9.67556 1.22407 9.55835C1.34128 9.44114 1.50026 9.37529 1.66602 9.37529H3.54102V5.39154C3.54102 5.02529 3.54102 4.70904 3.56477 4.45029C3.58977 4.17529 3.64477 3.90404 3.79227 3.64779C4.10727 3.10279 4.59227 2.86904 5.05352 2.77529C5.40977 2.70279 5.79977 2.70654 6.09727 2.70904ZM15.2073 9.99654V8.75279C15.2073 8.35154 15.2073 8.09279 15.1898 7.89654C15.1723 7.70904 15.1435 7.64029 15.1248 7.60654C15.0373 7.45654 14.9173 7.37779 14.696 7.33404C14.481 7.29029 14.2485 7.29154 13.951 7.29279C13.8164 7.29367 13.6818 7.29367 13.5473 7.29279C13.251 7.29154 13.0173 7.29029 12.8035 7.33404C12.5823 7.37779 12.461 7.45654 12.3748 7.60654C12.3548 7.64029 12.326 7.70904 12.3098 7.89654C12.2921 8.18159 12.2858 8.46723 12.291 8.75279V11.2528C12.291 11.6528 12.291 11.9115 12.3098 12.1078C12.326 12.2953 12.3548 12.364 12.3748 12.3978C12.4623 12.5478 12.5823 12.6265 12.8035 12.6715C13.0173 12.7153 13.2498 12.7128 13.5473 12.7115H13.951C14.2485 12.7128 14.481 12.7153 14.696 12.6715C14.9173 12.6265 15.0385 12.5465 15.1248 12.3978C15.1435 12.364 15.1723 12.2953 15.1898 12.1078C15.2073 11.9115 15.2073 11.6528 15.2073 11.2528V10.0028V10.0003V9.99654ZM7.70727 5.41904C7.70727 5.01904 7.70727 4.75904 7.68977 4.56279C7.67227 4.37529 7.64352 4.30654 7.62477 4.27279C7.53727 4.12279 7.41727 4.04529 7.19602 4.00029C6.98102 3.95654 6.74852 3.95779 6.45102 3.96029H6.04727C5.74977 3.95779 5.51727 3.95654 5.30352 4.00029C5.08227 4.04529 4.96102 4.12404 4.87477 4.27279C4.85477 4.30654 4.82602 4.37529 4.80977 4.56279C4.79207 4.84784 4.78581 5.13349 4.79102 5.41904V14.5853C4.79102 14.9865 4.79102 15.2453 4.80977 15.4415C4.82602 15.629 4.85477 15.6978 4.87477 15.7315C4.96227 15.8815 5.08227 15.959 5.30352 16.004C5.51727 16.0478 5.74977 16.0465 6.04727 16.0453C6.18185 16.0442 6.31643 16.0442 6.45102 16.0453C6.74852 16.0465 6.98102 16.0478 7.19602 16.004C7.41727 15.959 7.53852 15.8803 7.62477 15.7315C7.64352 15.6978 7.67227 15.629 7.68977 15.4415C7.70727 15.2453 7.70852 14.9865 7.70852 14.5853V5.41904H7.70727Z"
      fill="#333333"
    />
  </svg>
);
export const VarVerticalIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M10.6249 1.66602C10.6249 1.50026 10.5591 1.34128 10.4419 1.22407C10.3247 1.10686 10.1657 1.04102 9.99993 1.04102C9.83417 1.04102 9.6752 1.10686 9.55799 1.22407C9.44078 1.34128 9.37493 1.50026 9.37493 1.66602V3.54102H8.72368C8.35618 3.54102 8.04118 3.54102 7.78118 3.56602C7.50618 3.58977 7.23493 3.64477 6.97868 3.79227C6.43368 4.10727 6.19993 4.59227 6.10618 5.05477C6.03368 5.40977 6.03743 5.79976 6.04118 6.09726V6.40227C6.03743 6.69851 6.03368 7.08976 6.10618 7.44601C6.19993 7.90726 6.43368 8.39226 6.97868 8.70726C7.23493 8.85476 7.50743 8.90976 7.78118 8.93476C8.04118 8.95851 8.35618 8.95851 8.72368 8.95851H9.37368V11.041H5.39118C5.02493 11.041 4.70868 11.041 4.44868 11.066C4.17368 11.0898 3.90368 11.1448 3.64743 11.2923C3.10243 11.6073 2.86868 12.0923 2.77368 12.5548C2.70118 12.9098 2.70618 13.2998 2.70868 13.5973C2.70966 13.6989 2.70966 13.8006 2.70868 13.9023C2.70618 14.1998 2.70118 14.5898 2.77368 14.946C2.86868 15.4073 3.10243 15.8923 3.64743 16.2073C3.90368 16.3548 4.17493 16.4098 4.44868 16.4348C4.70868 16.4585 5.02368 16.4585 5.39118 16.4585H9.37493V18.3335C9.37493 18.4993 9.44078 18.6582 9.55799 18.7755C9.6752 18.8927 9.83417 18.9585 9.99993 18.9585C10.1657 18.9585 10.3247 18.8927 10.4419 18.7755C10.5591 18.6582 10.6249 18.4993 10.6249 18.3335V16.4585H14.6099C14.9762 16.4585 15.2924 16.4585 15.5524 16.4335C15.8274 16.4098 16.0987 16.3548 16.3537 16.2073C16.8999 15.8923 17.1337 15.4073 17.2274 14.9448C17.2999 14.5898 17.2962 14.1998 17.2924 13.9023V13.7498V13.5973C17.2962 13.2998 17.2999 12.9098 17.2274 12.5535C17.1337 12.0923 16.8999 11.6073 16.3537 11.2923C16.1067 11.1582 15.833 11.0805 15.5524 11.0648C15.2924 11.041 14.9774 11.041 14.6099 11.041H10.6249V8.95851H11.2774C11.6437 8.95851 11.9599 8.95851 12.2187 8.93351C12.4937 8.90976 12.7649 8.85476 13.0212 8.70726C13.5662 8.39226 13.7999 7.90726 13.8937 7.44476C13.9662 7.08976 13.9624 6.69851 13.9599 6.40227C13.9588 6.3006 13.9588 6.19893 13.9599 6.09726C13.9624 5.79976 13.9662 5.40977 13.8937 5.05352C13.7999 4.59227 13.5662 4.10727 13.0212 3.79227C12.7738 3.65796 12.4997 3.58026 12.2187 3.56477C11.9599 3.54102 11.6437 3.54102 11.2774 3.54102H10.6249V1.66602ZM10.0062 15.2085H5.41618C5.01618 15.2085 4.75743 15.2085 4.56118 15.1898C4.37368 15.1723 4.30493 15.1435 4.27118 15.1248C4.12118 15.0373 4.04243 14.9173 3.99743 14.696C3.95368 14.4823 3.95618 14.2485 3.95743 13.951C3.95831 13.8168 3.95831 13.6827 3.95743 13.5485C3.95618 13.251 3.95368 13.0173 3.99743 12.8035C4.04243 12.5823 4.12243 12.461 4.27118 12.3748C4.30493 12.356 4.37243 12.3273 4.56118 12.3098C4.75743 12.2923 5.01618 12.291 5.41618 12.291H14.5837C14.9837 12.291 15.2437 12.291 15.4399 12.3098C15.6274 12.3273 15.6949 12.356 15.7287 12.3748C15.8787 12.4623 15.9574 12.5823 16.0024 12.8035C16.0462 13.0173 16.0449 13.251 16.0424 13.5473V13.951C16.0449 14.2485 16.0462 14.4823 16.0024 14.696C15.9574 14.9173 15.8774 15.0385 15.7287 15.1248C15.6949 15.1435 15.6274 15.1723 15.4399 15.1898C15.1549 15.2075 14.8692 15.2138 14.5837 15.2085H10.0062ZM9.99993 7.70851H8.74993C8.34993 7.70851 8.08993 7.70851 7.89368 7.68976C7.70618 7.67226 7.63868 7.64351 7.60368 7.62476C7.45493 7.53726 7.37618 7.41726 7.33118 7.19601C7.28743 6.98101 7.28868 6.74851 7.29118 6.45101V6.24976V6.04852C7.28868 5.75102 7.28743 5.51727 7.33118 5.30352C7.37618 5.08227 7.45493 4.96102 7.60368 4.87477C7.63868 4.85602 7.70618 4.82727 7.89368 4.80977C8.08993 4.79227 8.34868 4.79102 8.74993 4.79102H11.2499C11.6499 4.79102 11.9099 4.79102 12.1062 4.80977C12.2937 4.82727 12.3612 4.85602 12.3962 4.87477C12.5462 4.96227 12.6237 5.08227 12.6687 5.30352C12.7124 5.51727 12.7112 5.75102 12.7087 6.04852V6.45101C12.7112 6.74851 12.7124 6.98101 12.6687 7.19601C12.6237 7.41726 12.5449 7.53851 12.3962 7.62476C12.3624 7.64351 12.2937 7.67226 12.1062 7.68976C11.8211 7.70747 11.5355 7.71373 11.2499 7.70851H9.99993Z"
      fill="#333333"
    />
  </svg>
);
export const VarSettingIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <g clip-path="url(#clip0_18267_10310)">
      <path
        d="M9.99992 6.66919C8.16333 6.66919 6.66911 8.16337 6.66911 9.99996C6.66911 11.8366 8.16331 13.3308 9.99992 13.3308C11.8365 13.3308 13.3307 11.8366 13.3307 9.99996C13.3307 8.16337 11.8365 6.66919 9.99992 6.66919ZM9.99992 12.3976C8.67785 12.3976 7.60224 11.322 7.60224 9.99996C7.60224 8.67784 8.67785 7.6022 9.99992 7.6022C11.322 7.6022 12.3976 8.67784 12.3976 9.99996C12.3976 11.322 11.322 12.3976 9.99992 12.3976ZM17.2708 7.55044H16.87L16.8232 7.42607L16.8211 7.42143C16.8167 7.41222 16.8124 7.40294 16.8086 7.3931L16.8078 7.39127C16.7506 7.24209 16.6973 7.1132 16.6449 6.99723L16.5895 6.87481L16.8732 6.59069C17.2943 6.16975 17.5262 5.61004 17.5262 5.0146C17.5262 4.41914 17.2943 3.85929 16.8732 3.43811L16.5617 3.12655C16.1407 2.7056 15.5808 2.47379 14.9854 2.47379C14.3899 2.47379 13.8302 2.7056 13.4093 3.12648L13.1254 3.41057L13.0028 3.35519C12.8774 3.29848 12.7376 3.24118 12.5753 3.18L12.4495 3.13255V2.72917C12.4495 1.5 11.4495 0.5 10.2203 0.5H9.77967C8.55049 0.5 7.55047 1.50002 7.55047 2.72917V3.13255L7.42464 3.18C7.2629 3.24099 7.12311 3.29829 6.99726 3.35519L6.87475 3.41055L6.59071 3.12651C6.16979 2.7056 5.61006 2.47379 5.01464 2.47379C4.41921 2.47379 3.8594 2.7056 3.43838 3.12651L3.12681 3.43813C2.70577 3.85927 2.47389 4.41914 2.47389 5.0146C2.47389 5.61004 2.70577 6.16977 3.12688 6.5908L3.41061 6.87487L3.35514 6.99735C3.30233 7.11398 3.25052 7.23917 3.19168 7.39245C3.18765 7.40279 3.18322 7.41243 3.17868 7.42197L3.17658 7.42645L3.12969 7.55048H2.7292C1.50002 7.55048 0.5 8.55048 0.5 9.77963V10.2204C0.5 11.4495 1.50002 12.4495 2.7292 12.4495H3.13013L3.17677 12.574L3.17883 12.5784C3.18332 12.5878 3.18767 12.5974 3.19212 12.6087C3.24943 12.7581 3.30275 12.887 3.35514 13.0027L3.41061 13.1251L3.12679 13.4093C2.70577 13.8302 2.47389 14.39 2.47389 14.9854C2.47389 15.5809 2.70577 16.1407 3.12681 16.5619L3.43838 16.8734C3.8594 17.2944 4.41921 17.5262 5.01466 17.5262C5.6101 17.5262 6.16983 17.2944 6.59073 16.8734L6.87479 16.5894L6.9973 16.6448C7.12332 16.7018 7.26308 16.759 7.42458 16.8199L7.55049 16.8673V17.2708C7.55049 18.5 8.55051 19.5 9.77969 19.5H10.2203C11.4495 19.5 12.4495 18.5 12.4495 17.2708V16.8673L12.5754 16.8199C12.7374 16.7588 12.8772 16.7016 13.0028 16.6448L13.1254 16.5894L13.4092 16.8734C13.8302 17.2944 14.3899 17.5262 14.9853 17.5262C15.5808 17.5262 16.1406 17.2944 16.5617 16.8735L16.8732 16.5619C17.2943 16.1407 17.5262 15.5809 17.5262 14.9854C17.5262 14.39 17.2943 13.8302 16.8731 13.4092L16.5895 13.1252L16.6449 13.0027C16.6976 12.8862 16.7509 12.7573 16.8083 12.6076C16.8123 12.5974 16.8166 12.5878 16.8211 12.5784L16.8231 12.5742L16.8695 12.4495H17.2708C18.5 12.4495 19.5 11.4495 19.5 10.2204V9.77963C19.4999 8.55044 18.4999 7.55044 17.2708 7.55044ZM18.5669 10.2202C18.5669 10.3043 18.5583 10.3896 18.5416 10.4724C18.5354 10.5049 18.5262 10.5326 18.5188 10.5548C18.5165 10.5616 18.5142 10.5684 18.5121 10.5752L18.5065 10.5959C18.4973 10.6296 18.4868 10.6679 18.4702 10.7088C18.4531 10.7505 18.4328 10.787 18.4149 10.8192L18.404 10.839C18.4008 10.8447 18.3979 10.8507 18.3949 10.8566C18.3859 10.8742 18.3748 10.8961 18.3596 10.9191C18.3242 10.9741 18.2842 11.0224 18.2496 11.0624L18.2483 11.0639C18.2469 11.0656 18.2456 11.0674 18.2442 11.0692C18.2379 11.0774 18.2292 11.0887 18.2177 11.1009C18.173 11.1488 18.1225 11.1943 18.0625 11.2407C18.0574 11.245 18.0522 11.2493 18.0466 11.2534C17.8564 11.3966 17.6282 11.4838 17.3872 11.5055C17.3782 11.5064 17.3693 11.5076 17.3604 11.5088C17.336 11.5122 17.3056 11.5163 17.2708 11.5163H16.1754C16.0255 12.1213 15.7746 12.7145 15.4278 13.2836L16.2135 14.0689C16.4114 14.2667 16.5369 14.5214 16.5766 14.8061C16.5775 14.8124 16.578 14.8185 16.5786 14.8254C16.588 14.9013 16.5914 14.9696 16.589 15.0326C16.5885 15.0497 16.5867 15.0644 16.5854 15.0751C16.585 15.0782 16.5846 15.0812 16.5844 15.0838C16.5803 15.1375 16.5748 15.2001 16.5608 15.2627C16.5553 15.2878 16.5482 15.31 16.5424 15.328C16.5402 15.3349 16.5379 15.342 16.5359 15.349C16.5359 15.349 16.5307 15.3669 16.5283 15.3749C16.5184 15.4093 16.5072 15.4482 16.4898 15.4889C16.4735 15.5279 16.4543 15.5621 16.4372 15.5922C16.4334 15.599 16.4295 15.6058 16.4249 15.6141C16.4208 15.6214 16.4171 15.6287 16.4134 15.6361C16.4035 15.6555 16.3913 15.6796 16.3744 15.7048C16.3262 15.777 16.272 15.8433 16.2135 15.902L15.9016 16.2139C15.8432 16.272 15.7772 16.326 15.7053 16.3743L15.7033 16.3756C15.6787 16.3917 15.6552 16.4037 15.6362 16.4133C15.6285 16.4172 15.6208 16.4211 15.6137 16.425L15.5959 16.4351C15.5646 16.4529 15.529 16.4732 15.4897 16.4896C15.4498 16.5065 15.4119 16.5175 15.3785 16.5272C15.3697 16.5298 15.3608 16.5324 15.3519 16.535L15.3478 16.5362C15.3409 16.5382 15.3341 16.5404 15.3273 16.5426C15.3096 16.5482 15.2878 16.5552 15.2627 16.5605C15.1987 16.5747 15.1363 16.5806 15.0837 16.5846L15.0816 16.5847C15.0786 16.5849 15.0755 16.5853 15.0725 16.5857C15.0626 16.5868 15.0493 16.5882 15.0339 16.5888C15.0193 16.5894 15.0047 16.5897 14.9901 16.5897C14.9395 16.5897 14.8851 16.5861 14.8239 16.5785L14.8227 16.5783C14.8169 16.5778 14.811 16.5773 14.8052 16.5764C14.5217 16.5368 14.2671 16.4114 14.069 16.2136L13.2845 15.4292C12.7151 15.7768 12.1216 16.0281 11.5163 16.1779V17.2707C11.5163 17.3052 11.5122 17.3354 11.5089 17.3597C11.5077 17.3686 11.5064 17.3775 11.5056 17.3864C11.4838 17.6278 11.3968 17.8556 11.2541 18.0454C11.2511 18.0495 11.248 18.0535 11.2447 18.0576L11.2417 18.0613C11.1947 18.1219 11.1486 18.1729 11.101 18.2172C11.0882 18.2292 11.0767 18.2382 11.0681 18.2448C11.0662 18.2463 11.0643 18.2477 11.0624 18.2493C11.0194 18.2864 10.9721 18.3255 10.9185 18.3599C10.8966 18.3741 10.8758 18.3848 10.859 18.3934C10.8527 18.3966 10.8463 18.3999 10.8397 18.4035L10.8254 18.4114C10.7917 18.4301 10.7535 18.4513 10.7104 18.4696L10.7076 18.4708C10.6682 18.4867 10.6302 18.4972 10.5966 18.5064L10.5745 18.5125C10.5666 18.5147 10.5587 18.5174 10.5509 18.52C10.5299 18.5269 10.5038 18.5355 10.4736 18.5414C10.3891 18.5583 10.3038 18.5669 10.2203 18.5669H9.77963C9.6961 18.5669 9.61085 18.5583 9.52629 18.5414C9.49615 18.5355 9.47026 18.527 9.44948 18.5202C9.44153 18.5175 9.43361 18.5149 9.42503 18.5125L9.40298 18.5064C9.36952 18.4971 9.33159 18.4867 9.29231 18.4708L9.28958 18.4696C9.24636 18.4514 9.20807 18.4301 9.17432 18.4114L9.15998 18.4034C9.1537 18.3999 9.14732 18.3967 9.14096 18.3934C9.12414 18.3848 9.10321 18.3741 9.08218 18.3605C9.02567 18.3243 8.97791 18.2842 8.93799 18.2499C8.93608 18.2482 8.93408 18.2467 8.93209 18.2452C8.92336 18.2384 8.91151 18.2292 8.89846 18.2169C8.85089 18.1725 8.80498 18.1216 8.75815 18.0613L8.75722 18.0601C8.75443 18.0568 8.75006 18.0514 8.74507 18.0445C8.60264 17.8547 8.51594 17.6272 8.49432 17.387C8.4935 17.3781 8.49224 17.3693 8.49104 17.3604C8.48772 17.3359 8.48359 17.3055 8.48359 17.2707V16.1779C7.87799 16.0279 7.28454 15.7767 6.71542 15.4292L5.93078 16.2138C5.73265 16.4114 5.47814 16.5368 5.1951 16.5764C5.18923 16.5772 5.18346 16.5778 5.17699 16.5783L5.17594 16.5785C5.11479 16.586 5.06044 16.5897 5.00974 16.5897C4.99511 16.5897 4.98044 16.5894 4.96577 16.5888C4.95046 16.5882 4.93715 16.5868 4.92735 16.5856C4.92429 16.5853 4.92126 16.5849 4.9182 16.5847L4.9162 16.5846C4.86356 16.5806 4.80119 16.5747 4.73716 16.5605C4.7122 16.5552 4.69048 16.5483 4.67295 16.5427C4.66608 16.5405 4.65922 16.5382 4.65225 16.5363L4.64759 16.535C4.63858 16.5322 4.62956 16.5296 4.62053 16.527C4.58734 16.5173 4.54972 16.5064 4.51147 16.4901C4.47109 16.4733 4.43592 16.4533 4.40488 16.4356L4.3864 16.4251C4.37889 16.4209 4.37123 16.4171 4.36359 16.4132C4.34457 16.4036 4.32093 16.3915 4.29479 16.3744C4.22279 16.3261 4.15665 16.272 4.09803 16.2135L3.7863 15.9018C3.72742 15.8428 3.67326 15.7764 3.62554 15.7048C3.60858 15.6795 3.59634 15.6554 3.58654 15.636C3.58278 15.6286 3.57909 15.6213 3.57447 15.6131L3.56351 15.5936C3.54613 15.563 3.52646 15.5283 3.50992 15.4886C3.4927 15.4481 3.4815 15.4093 3.47163 15.3751C3.46932 15.3671 3.46376 15.3482 3.46376 15.3482C3.46174 15.341 3.45941 15.3339 3.45712 15.3268C3.45148 15.3093 3.44446 15.2874 3.43928 15.2636C3.42555 15.2016 3.41972 15.1405 3.41547 15.0838C3.41524 15.0807 3.41482 15.0776 3.41443 15.0745C3.41321 15.064 3.41153 15.0499 3.411 15.0334C3.40848 14.97 3.41184 14.9022 3.42121 14.8269C3.42175 14.8194 3.42247 14.8118 3.42364 14.804C3.46336 14.5207 3.58881 14.2664 3.78647 14.0688L4.57197 13.2835C4.22521 12.7144 3.97437 12.1213 3.82453 11.5163H2.72909C2.69425 11.5163 2.66391 11.5121 2.63954 11.5088C2.63066 11.5076 2.62178 11.5063 2.61339 11.5055C2.37166 11.4837 2.14343 11.3966 1.95331 11.2533C1.94829 11.2496 1.94344 11.2457 1.93824 11.2412C1.879 11.1956 1.82799 11.1498 1.78235 11.1011C1.77066 11.0886 1.76169 11.0769 1.75573 11.0692C1.7546 11.0677 1.75351 11.0663 1.75237 11.0648L1.74998 11.0621C1.71542 11.0219 1.67539 10.9734 1.64015 10.919C1.62537 10.8964 1.61449 10.875 1.6057 10.8578C1.60261 10.8517 1.59953 10.8455 1.59583 10.8388L1.58519 10.8195C1.56728 10.7872 1.54698 10.7506 1.52943 10.708C1.513 10.6678 1.5025 10.6292 1.49322 10.5952L1.48762 10.5749C1.48554 10.5679 1.48323 10.561 1.48092 10.5541C1.47362 10.532 1.46453 10.5045 1.45859 10.4737C1.44162 10.3893 1.433 10.304 1.433 10.2201V9.77948C1.433 9.69572 1.44162 9.61043 1.45831 9.52742C1.46457 9.49499 1.47372 9.46739 1.48105 9.44523C1.48338 9.4382 1.48573 9.43121 1.48783 9.42416C1.48989 9.41682 1.4919 9.40945 1.49392 9.4021C1.50303 9.3688 1.51337 9.3311 1.52966 9.29114C1.54688 9.24929 1.56712 9.21277 1.58498 9.18052L1.596 9.16056C1.59927 9.1546 1.60232 9.14849 1.60541 9.14242C1.61424 9.12502 1.6252 9.10345 1.64004 9.0808C1.67468 9.02718 1.71299 8.98103 1.74928 8.93832L1.75201 8.93523C1.75332 8.93364 1.75457 8.932 1.75583 8.93036C1.7619 8.92255 1.77091 8.91091 1.78281 8.89846C1.8283 8.84968 1.87931 8.8037 1.93866 8.75799C1.94344 8.75392 1.94833 8.74997 1.95343 8.74619C2.14335 8.60318 2.37149 8.51602 2.6133 8.49413C2.62237 8.49333 2.63133 8.49203 2.64026 8.49082C2.66444 8.48754 2.69454 8.48345 2.72909 8.48345H3.82455C3.97448 7.87819 4.22533 7.28505 4.57197 6.71624L3.78643 5.93088C3.58876 5.7331 3.46332 5.47885 3.42362 5.19558C3.42251 5.18804 3.42179 5.18076 3.42121 5.17283C3.41184 5.09719 3.40848 5.02954 3.411 4.96622C3.41153 4.94975 3.41321 4.93556 3.41445 4.92513C3.41482 4.92203 3.41526 4.91894 3.41547 4.91598C3.41974 4.85919 3.42557 4.79811 3.43909 4.7371C3.44451 4.71202 3.45166 4.68975 3.45738 4.67185C3.45964 4.66482 3.46193 4.65779 3.46395 4.65067C3.46395 4.65067 3.46932 4.63244 3.47163 4.62442C3.48158 4.58998 3.49287 4.55094 3.50986 4.51107C3.52667 4.471 3.5467 4.4357 3.56437 4.40453L3.57506 4.3856C3.57913 4.37842 3.58282 4.37099 3.58656 4.3636C3.59636 4.34423 3.60854 4.32014 3.62544 4.29493C3.67336 4.22318 3.72746 4.15692 3.78647 4.09769L4.0982 3.78592C4.15625 3.72801 4.22231 3.67401 4.2946 3.6254L4.29645 3.62415C4.32097 3.60809 4.34453 3.59611 4.36346 3.58651C4.37112 3.58261 4.37883 3.57875 4.38666 3.57438L4.40622 3.56332C4.43676 3.54597 4.47138 3.52632 4.51011 3.51008C4.55039 3.49297 4.58862 3.48193 4.62233 3.47219C4.63094 3.46972 4.63957 3.46722 4.64818 3.46462L4.65206 3.46349C4.65899 3.46157 4.66583 3.45933 4.67265 3.45713C4.69031 3.45148 4.71222 3.44445 4.73739 3.43918C4.80222 3.42485 4.86568 3.41905 4.91442 3.41567C4.91704 3.41538 4.91967 3.41504 4.92231 3.41471C4.93367 3.4133 4.94922 3.41137 4.96623 3.41081C4.97964 3.41034 4.99301 3.41009 5.00637 3.41009C5.05849 3.41009 5.11225 3.41364 5.17054 3.42094C5.1763 3.42126 5.18528 3.42189 5.19603 3.4235C5.47888 3.46317 5.73293 3.58847 5.93096 3.78605L6.71544 4.5705C7.2845 4.22299 7.87797 3.9717 8.48361 3.82174V2.72892C8.48361 2.69418 8.48773 2.66377 8.49106 2.63936C8.49226 2.63052 8.49352 2.62167 8.49428 2.61346C8.51598 2.37229 8.60285 2.14449 8.74555 1.95459C8.74916 1.94964 8.7529 1.94486 8.75705 1.93988L8.75819 1.93839C8.8054 1.87767 8.85139 1.82668 8.89875 1.78264C8.91168 1.77039 8.92346 1.76123 8.93215 1.75448C8.93417 1.7529 8.93624 1.75137 8.93736 1.75045C8.98068 1.71305 9.02823 1.6737 9.08134 1.63982C9.10322 1.62563 9.12416 1.61493 9.14096 1.60633C9.14732 1.60309 9.15372 1.59984 9.15987 1.59644L9.17182 1.58977C9.20673 1.57029 9.2463 1.54823 9.29095 1.52989C9.33119 1.51324 9.36984 1.50258 9.40395 1.49316L9.42481 1.48735C9.43285 1.48504 9.44073 1.48243 9.44862 1.47983C9.46965 1.47291 9.49583 1.46434 9.5264 1.45824C9.61076 1.44151 9.69597 1.43301 9.77967 1.43301H10.2203C10.304 1.43301 10.3892 1.44151 10.4735 1.45826C10.5042 1.46438 10.5306 1.47305 10.5519 1.48002C10.5596 1.48258 10.5674 1.4852 10.5748 1.4873L10.5961 1.49324C10.6302 1.50263 10.6688 1.51327 10.7086 1.52974C10.7528 1.5479 10.7915 1.56945 10.8257 1.58846L10.84 1.59642C10.8463 1.59992 10.8527 1.60314 10.8591 1.60641C10.8758 1.61499 10.8967 1.62566 10.9174 1.63909C10.9723 1.67412 11.0197 1.71342 11.0617 1.74978C11.0637 1.75145 11.0657 1.75299 11.0678 1.75458C11.0764 1.76124 11.088 1.7703 11.1008 1.78235C11.1482 1.82632 11.1943 1.87738 11.2418 1.93837L11.2435 1.94064C11.2472 1.94509 11.2508 1.94966 11.2543 1.95445C11.3969 2.14432 11.4839 2.37212 11.5057 2.61323C11.5065 2.62221 11.5078 2.63111 11.509 2.64001C11.5122 2.66425 11.5164 2.69443 11.5164 2.72891V3.82174C12.1219 3.9717 12.7154 4.22299 13.2846 4.5705L14.0692 3.78594C14.267 3.58845 14.5212 3.46309 14.8042 3.42344C14.8148 3.42187 14.8235 3.42126 14.829 3.42096C14.8875 3.41362 14.9414 3.41007 14.9936 3.41007C15.0069 3.41007 15.0203 3.41032 15.0327 3.41076C15.0506 3.41131 15.0662 3.41326 15.0775 3.41467C15.0802 3.415 15.0828 3.41536 15.0855 3.41565C15.1343 3.41905 15.1979 3.42485 15.2628 3.43924C15.288 3.44453 15.31 3.45158 15.3277 3.45727C15.3344 3.45945 15.3412 3.46166 15.3481 3.46359C15.3481 3.46359 15.3683 3.46951 15.3767 3.47196C15.4107 3.48174 15.4492 3.49285 15.4887 3.50964C15.5287 3.52636 15.5636 3.54618 15.5944 3.56368L15.6134 3.57443C15.6209 3.57867 15.6287 3.58253 15.6363 3.58645C15.6553 3.59604 15.6787 3.60798 15.7032 3.62394L15.7055 3.62547C15.7776 3.67403 15.8436 3.72795 15.9019 3.78605L16.2138 4.09792C16.272 4.15641 16.3261 4.22261 16.3743 4.29462C16.3912 4.31985 16.4033 4.34381 16.4131 4.36304C16.4168 4.37043 16.4205 4.37786 16.4246 4.38506L16.4366 4.40648C16.454 4.43717 16.4737 4.47196 16.4894 4.50987C16.507 4.55096 16.5183 4.59013 16.5283 4.62473C16.5306 4.63265 16.5359 4.6507 16.5359 4.6507C16.5379 4.65783 16.5402 4.66488 16.5425 4.67189C16.5482 4.6898 16.5553 4.71208 16.5604 4.73568C16.5748 4.79967 16.5803 4.86223 16.5843 4.91558C16.5846 4.9186 16.585 4.92163 16.5854 4.92467C16.5867 4.93537 16.5884 4.95004 16.5889 4.96522C16.5915 5.02929 16.5881 5.09778 16.5785 5.17457C16.5781 5.18061 16.5775 5.18681 16.5765 5.19453C16.5369 5.47824 16.4114 5.73287 16.2135 5.93093L15.4279 6.71626C15.7745 7.28503 16.0254 7.8781 16.1754 8.48345H17.2708C17.3053 8.48345 17.3354 8.48754 17.3597 8.49082C17.3686 8.49203 17.3776 8.49333 17.3865 8.49413C17.6281 8.51585 17.8562 8.60298 18.0464 8.74615C18.0516 8.74997 18.0566 8.75404 18.0618 8.75849C18.1214 8.80442 18.1723 8.85031 18.2172 8.89863C18.229 8.91103 18.2381 8.92266 18.2441 8.93047C18.2455 8.93221 18.2468 8.93395 18.2473 8.93466L18.2505 8.9383C18.2867 8.98088 18.3249 9.02691 18.3596 9.08047C18.3749 9.10372 18.3861 9.12586 18.3952 9.14372C18.3981 9.14962 18.4011 9.15552 18.4037 9.16035L18.4136 9.17827C18.432 9.2112 18.4528 9.24849 18.4706 9.29196C18.4865 9.33108 18.4967 9.36852 18.5058 9.40156C18.5078 9.40903 18.5098 9.41648 18.5119 9.42389C18.514 9.43083 18.5163 9.4377 18.5186 9.44462C18.5261 9.46691 18.5353 9.49466 18.5413 9.52595C18.5583 9.6103 18.5669 9.69557 18.5669 9.77946V10.2202H18.5669Z"
        fill="#3463FC"
        stroke="#3463FC"
        stroke-width="0.25"
      />
    </g>
    <defs>
      <clipPath id="clip0_18267_10310">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const ConstIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clip-path="url(#clip0_17518_67014)">
      <path
        d="M3.0297 -3.05176e-05V1.19038H2.18577C1.64222 1.19038 1.38474 1.4995 1.38474 2.14636V5.84473C1.38474 6.75621 0.96993 7.37366 0.154604 7.68199C0.96993 8.03482 1.38474 8.63797 1.38474 9.52005V13.2327C1.38474 13.8502 1.64222 14.1736 2.18577 14.1736H3.0297V15.3648H1.9283C1.29892 15.3648 0.812586 15.1439 0.469291 14.7323C0.154604 14.3508 -0.00273967 13.8208 -0.00273967 13.1747V9.59316C-0.00273967 9.16722 -0.0885636 8.85809 -0.260211 8.66737C-0.460467 8.43215 -0.818065 8.31454 -1.33301 8.28514V7.07963C-0.818065 7.05023 -0.460467 6.91752 -0.260211 6.6974C-0.0885636 6.49079 -0.00273967 6.18246 -0.00273967 5.77082V2.20516C-0.00273967 1.544 0.154604 1.01476 0.469291 0.631728C0.812586 0.205788 1.29892 -3.05176e-05 1.9283 -3.05176e-05H3.0297ZM14.0724 -3.05176e-05C14.7017 -3.05176e-05 15.1881 0.205788 15.5314 0.632522C15.846 1.01396 16.0034 1.544 16.0034 2.20516V5.77082C16.0034 6.18246 16.0892 6.49158 16.2609 6.6974C16.4611 6.91752 16.8187 7.05023 17.3337 7.07884V8.28514C16.8187 8.31454 16.4611 8.43215 16.2609 8.66658C16.0892 8.85809 16.0034 9.16722 16.0034 9.59316V13.1739C16.0034 13.8208 15.846 14.35 15.5314 14.7323C15.1881 15.1439 14.7017 15.3648 14.0724 15.3648H12.971V14.1736H13.8149C14.3584 14.1736 14.6159 13.8502 14.6159 13.2327V9.52005C14.6159 8.63797 15.0307 8.03482 15.846 7.68199C15.0307 7.37366 14.6159 6.75621 14.6159 5.84473V2.14556C14.6159 1.49871 14.3584 1.18958 13.8149 1.18958H12.971V-3.05176e-05H14.0724ZM6.47536 3.26683L8.21806 5.85744L9.94487 3.26683H11.8401L9.10491 7.08996L12.1763 11.4344H10.281L8.21806 8.32249L6.14002 11.4344H4.22964L7.31691 7.08996L4.58088 3.26763H6.47616L6.47536 3.26683Z"
        fill="#999999"
      />
    </g>
    <defs>
      <clipPath id="clip0_17518_67014">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const shortcutTipsIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_1945_21501)">
      <path
        d="M6.42958 15.532C6.28106 15.532 6.13862 15.473 6.0336 15.368C5.92858 15.263 5.86958 15.1205 5.86958 14.972C5.86958 14.8985 5.88406 14.8256 5.91221 14.7577C5.94035 14.6898 5.9816 14.628 6.0336 14.576C6.0856 14.524 6.14733 14.4828 6.21527 14.4546C6.28322 14.4265 6.35604 14.412 6.42958 14.412H9.70958C9.78312 14.412 9.85594 14.4265 9.92388 14.4546C9.99182 14.4828 10.0536 14.524 10.1056 14.576C10.1576 14.628 10.1988 14.6898 10.227 14.7577C10.2551 14.8256 10.2696 14.8985 10.2696 14.972C10.2696 15.1205 10.2106 15.263 10.1056 15.368C10.0005 15.473 9.8581 15.532 9.70958 15.532H6.42958ZM6.66958 13.692C6.56452 13.692 6.46049 13.6713 6.36343 13.6311C6.26637 13.5909 6.17818 13.532 6.10389 13.4577C6.02961 13.3834 5.97068 13.2952 5.93047 13.1981C5.89027 13.1011 5.86958 12.9971 5.86958 12.892V11.9032C5.20059 11.517 4.64508 10.9614 4.25889 10.2924C3.87271 9.62338 3.66946 8.86448 3.66958 8.092C3.66958 6.92505 4.13315 5.80589 4.95831 4.98073C5.78347 4.15557 6.90263 3.692 8.06958 3.692C9.23653 3.692 10.3557 4.15557 11.1808 4.98073C12.006 5.80589 12.4696 6.92505 12.4696 8.092C12.4696 8.86456 12.2662 9.62351 11.8799 10.2925C11.4936 10.9616 10.9379 11.5171 10.2688 11.9032V12.892C10.2688 12.9971 10.2481 13.1011 10.2079 13.1981C10.1677 13.2952 10.1088 13.3834 10.0345 13.4577C9.96018 13.532 9.87199 13.5909 9.77493 13.6311C9.67787 13.6713 9.57384 13.692 9.46878 13.692H6.66958ZM13.828 8.3592C13.6795 8.3592 13.537 8.3002 13.432 8.19518C13.327 8.09016 13.268 7.94772 13.268 7.7992C13.268 7.65068 13.327 7.50824 13.432 7.40322C13.537 7.2982 13.6795 7.2392 13.828 7.2392H14.708C14.8565 7.2392 14.9989 7.2982 15.104 7.40322C15.209 7.50824 15.268 7.65068 15.268 7.7992C15.268 7.94772 15.209 8.09016 15.104 8.19518C14.9989 8.3002 14.8565 8.3592 14.708 8.3592H13.828ZM1.27918 8.3592C1.13066 8.3592 0.988218 8.3002 0.883197 8.19518C0.778177 8.09016 0.719177 7.94772 0.719177 7.7992C0.719177 7.65068 0.778177 7.50824 0.883197 7.40322C0.988218 7.2982 1.13066 7.2392 1.27918 7.2392H2.15918C2.3077 7.2392 2.45014 7.2982 2.55516 7.40322C2.66018 7.50824 2.71918 7.65068 2.71918 7.7992C2.71918 7.94772 2.66018 8.09016 2.55516 8.19518C2.45014 8.3002 2.3077 8.3592 2.15918 8.3592H1.27918ZM11.964 3.8488C11.8592 3.74381 11.8003 3.60154 11.8003 3.4532C11.8003 3.30487 11.8592 3.16259 11.964 3.0576L12.5848 2.4352C12.6368 2.38308 12.6986 2.34172 12.7666 2.3135C12.8346 2.28529 12.9075 2.27076 12.9812 2.27076C13.0548 2.27076 13.1277 2.28529 13.1958 2.3135C13.2638 2.34172 13.3256 2.38308 13.3776 2.4352C13.4824 2.54019 13.5412 2.68247 13.5412 2.8308C13.5412 2.97914 13.4824 3.12141 13.3776 3.2264L12.7552 3.8488C12.6497 3.95216 12.5077 4.00967 12.36 4.0088C12.2127 4.00967 12.071 3.95247 11.9656 3.8496L11.964 3.8488ZM3.21758 3.832L2.59518 3.2096C2.54305 3.15759 2.50169 3.0958 2.47348 3.02778C2.44526 2.95976 2.43074 2.88684 2.43074 2.8132C2.43074 2.73956 2.44526 2.66664 2.47348 2.59862C2.50169 2.5306 2.54305 2.46882 2.59518 2.4168C2.64719 2.36468 2.70898 2.32332 2.777 2.2951C2.84502 2.26689 2.91794 2.25236 2.99158 2.25236C3.06522 2.25236 3.13813 2.26689 3.20615 2.2951C3.27417 2.32332 3.33596 2.36468 3.38798 2.4168L4.01038 3.0392C4.0625 3.09122 4.10386 3.15301 4.13208 3.22102C4.16029 3.28904 4.17482 3.36196 4.17482 3.4356C4.17482 3.50924 4.16029 3.58216 4.13208 3.65018C4.10386 3.7182 4.0625 3.77999 4.01038 3.832C3.95757 3.88376 3.89503 3.92455 3.82638 3.95201C3.75773 3.97947 3.68431 3.99306 3.61038 3.992C3.46364 3.99245 3.3226 3.93528 3.21758 3.8328V3.832ZM7.48878 1.9192V1.0392C7.48878 0.890681 7.54778 0.748243 7.6528 0.643222C7.75782 0.538202 7.90026 0.479202 8.04878 0.479202C8.12232 0.479202 8.19514 0.493687 8.26308 0.52183C8.33102 0.549972 8.39276 0.591222 8.44476 0.643222C8.49676 0.695223 8.53801 0.756957 8.56615 0.8249C8.59429 0.892842 8.60878 0.965662 8.60878 1.0392V1.9192C8.60878 1.99274 8.59429 2.06556 8.56615 2.1335C8.53801 2.20145 8.49676 2.26318 8.44476 2.31518C8.39276 2.36718 8.33102 2.40843 8.26308 2.43657C8.19514 2.46472 8.12232 2.4792 8.04878 2.4792C7.90067 2.47878 7.75877 2.4197 7.65411 2.3149C7.54946 2.2101 7.49059 2.06811 7.49038 1.92L7.48878 1.9192Z"
        fill="#FFAA00"
      />
      <path
        d="M7.92801 9.9184C7.84047 9.91837 7.75417 9.89782 7.67601 9.8584C7.54342 9.79155 7.44281 9.67477 7.3963 9.53375C7.34979 9.39273 7.3612 9.23901 7.42801 9.1064L7.85041 8.2688H7.25361C7.16083 8.26885 7.06949 8.24585 6.98781 8.20186C6.90612 8.15788 6.83664 8.09428 6.78561 8.0168C6.7346 7.93928 6.70366 7.8503 6.69556 7.75786C6.68746 7.66541 6.70246 7.57241 6.73921 7.4872L7.41281 5.924C7.4419 5.85644 7.48402 5.79527 7.53675 5.74399C7.58948 5.69271 7.65181 5.65232 7.72015 5.62513C7.7885 5.59794 7.86154 5.58448 7.93509 5.58552C8.00864 5.58656 8.08126 5.60208 8.14881 5.6312C8.21637 5.66029 8.27754 5.7024 8.32882 5.75514C8.3801 5.80787 8.42049 5.87019 8.44768 5.93854C8.47487 6.00689 8.48833 6.07992 8.48729 6.15347C8.48625 6.22702 8.47072 6.29965 8.44161 6.3672L8.10481 7.1488H8.76001C8.85616 7.14826 8.95082 7.17249 9.03489 7.21915C9.11896 7.2658 9.1896 7.33332 9.24001 7.4152C9.29012 7.4966 9.31856 7.58947 9.32262 7.68498C9.32668 7.78048 9.30623 7.87544 9.26321 7.9608L8.43041 9.6112C8.38346 9.70401 8.31159 9.78191 8.22285 9.83617C8.13411 9.89043 8.03202 9.9189 7.92801 9.9184Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_1945_21501">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ShareCodeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M10.725 3.5C10.625 3.4 10.625 3.25 10.725 3.15L11.7875 2.0875C11.8875 1.9875 12.0375 1.9875 12.1375 2.0875L13.9 3.85C14 3.95 14 4.1 13.9 4.2L12.8375 5.2625C12.7375 5.3625 12.5875 5.3625 12.4875 5.2625L10.725 3.5ZM6 12.125L11.8125 6.3125C11.9 6.225 11.8875 6.075 11.7875 5.9875L10.025 4.2125C9.925 4.1125 9.775 4.1 9.7 4.1875L3.875 10L3 13L6 12.125ZM1 14V15H15V14H1Z"
      fill="#8501BB"
    />
  </svg>
);
