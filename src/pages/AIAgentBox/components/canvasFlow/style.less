.canvasFlow {
  width: 100%;
  height: calc(100vh - 100px);
  position: relative;
  overscroll-behavior: none;

  .canvasFlow_header {
    height: 32;
    width: 100%;
    // background-color: rgba(243, 247, 254, 1);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .handle {
      cursor: pointer;
    }

    .canvasFlow_header_left {
      display: flex;
      align-items: center;
      flex: 1;

      .canvasFlow_header_name {
        color: #333;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        margin: 0 5px 0 5px;
        white-space: nowrap;
        /* 24px */
      }

      .canvasFlow_header_channel {
        display: inline-flex;
        padding: 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 100px;
        background: #fff;
        margin-bottom: 5px;
        margin-left: 10%;
        /* 阴影 */
        box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
      }

      .canvasFlow_header_shareCode {
        margin-left: 20px;
        cursor: pointer;
        text-align: center;
        color: #8501bb;
        font-variant-numeric: lining-nums tabular-nums;
        font-family: 'Microsoft YaHei';
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        display: flex;
        align-items: center;
        gap: 5px;
        /* 19.5px */
      }
    }

    .canvasFlow_header_right {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: end;

      .canvasFlow_header_shortcut_tips {
        margin-right: 10px;

        .shortcut_tips_content {
          display: flex;
          flex-direction: column;
          gap: 5px;
          color: #333;

          .shortcut_tips_content_title {
            margin-bottom: 5px;

            b {
              font-weight: 700;
              font-family: 'MicrosoftYaHei';
            }
          }
        }

        :global {
          .ant-tooltip {
            min-width: max-content;
          }

          .ant-tooltip-inner {
            padding: 14px 17px;
            border-radius: 4px;
          }
        }
      }

      .canvasFlow_header_tips {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        white-space: nowrap;
        margin-right: 20%;

        span {
          display: flex;
          align-items: center;
        }
      }

      .canvasFlow_header_operation {
        display: inline-flex;
        padding: 6px 6px 0 6px;
        align-items: center;
        gap: 10px;
        border-radius: 100px;
        background: #fff;

        /* 阴影 */
        box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
        margin-right: 2%;
        margin-bottom: 5px;
      }

      .canvasFlow_header_btnTest {
        margin-right: 2%;
        padding: 5px 24px;
        font-size: 14px;
        margin-bottom: 3px;
      }

      .canvasFlow_header_save {
        margin-right: 2%;
        padding: 5px 24px;
        font-size: 14px;
        margin-bottom: 3px;
      }

      .canvasFlow_header_deploy {
        padding: 5px 24px;
        font-size: 14px;
        margin-bottom: 3px;
        background-color: rgba(52, 99, 252, 1);
        color: #fff;
      }
    }
  }

  .varControlsIcon {
    order: 1;
  }

  .varApiIcon {
    order: 2;
  }

  .varSettingIcon {
    order: 5;
  }

  .canvasFlow_backModal {
    .canvasFlow_modalFoot {
      text-align: center;

      :global {
        .ant-btn {
          font-size: 14px !important;
        }

        .ant-btn:nth-child(1) {
          margin-right: 10px;
        }

        .ant-btn:nth-child(2) {
          margin-right: 10px;
        }
      }
    }

    .canvasFlow_backContent {
      flex: 1;
      margin-bottom: 20px;

      .canvasFlow_backContent_title {
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
        /* 116.667% */
        display: flex;
        align-items: center;
      }
    }

    :global {
      .ant-modal-body {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .ant-modal-content {
        margin-top: 20%;
        border-radius: 10px;
        border: 1px solid #e6e6e6;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);

        .ant-modal-header {
          border-radius: 10px;
          background: none;
        }

        .ant-modal-title {
          font-size: 20px !important;
        }

        .ant-modal-close-icon {
          font-size: 20px;

          svg path {
            fill: rgba(51, 51, 51, 1);
          }
        }
      }
    }
  }

  .canvasFlow_channelModal {
    .canvasFlow_modalFoot {
      margin-bottom: 12px;
      text-align: center;

      :global {
        .ant-btn {
          font-size: 14px !important;
        }

        .ant-btn:nth-child(1) {
          margin-right: 20px;
        }
      }
    }

    .canvasFlow_modalContent {
      flex: 1;

      .canvasFlow_modalContent_title {
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 14px;
        /* 116.667% */
      }
    }

    :global {
      .ant-form-item {
        margin-bottom: 10px;
      }

      .ant-checkbox {
        display: none;
      }

      .ant-checkbox-wrapper {
        margin-bottom: 20px;
        margin-left: 0px;
        margin-right: 3px;

        .channelContainer {
          text-align: center;
          width: 95px;
          height: 95px;
          padding: 12px 15px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #e6e6e6;
          /* 模块阴影 */
          box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
          display: table-cell;

          //justify-content: center;
          //align-content: center;
          img {
            width: 40px;
          }

          p {
            color: #333;
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 14px;
            /* 18px */
            margin-bottom: 0px;
            margin-top: 5px;
            height: 24px;
            display: grid;
            justify-content: center;
            align-content: center;
          }
        }
      }

      // .ant-checkbox-wrapper:hover
      .ant-checkbox-wrapper-checked {
        .channelContainer {
          text-align: center;
          width: 95px;
          height: 95px;
          padding: 12px 15px;
          border-radius: 4px;
          border: 1px solid #3463fc;
          background: linear-gradient(0deg, #ebefff 0%, #ebefff 100%), #fff;
          /* 模块阴影 */
          box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
          display: table-cell;

          //justify-content: center;
          //align-content: center;
          img {
            width: 40px;
          }

          p {
            color: #333;
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 14px;
            /* 18px */
            margin-bottom: 0px;
            margin-top: 5px;
            height: 24px;
            display: grid;
            justify-content: center;
            align-content: center;
          }
        }
      }

      .ant-form-item-label {
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 14px;
      }

      .ant-form-item-label
        > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        display: none;
      }

      .ant-modal-body {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-top: 10px;
      }

      .ant-modal-content {
        width: 45vw;
        margin-top: 20%;
        border-radius: 10px;
        border: 1px solid #e6e6e6;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);

        .ant-modal-header {
          border-radius: 10px;
          background: none;
        }

        .ant-modal-title {
          font-size: 20px !important;
          font-weight: 700 !important;
        }

        .ant-modal-close-icon {
          font-size: 20px;

          svg path {
            fill: rgba(51, 51, 51, 1);
          }
        }
      }
    }
  }

  .canvasFlow_variablesModal {
    .canvasFlow_variablesModal_content {
      display: flex;
      flex-direction: column;

      .canvasFlow_variablesModal_form {
        margin-bottom: 10px;

        .canvasFlow_variablesModal_form_left {
          width: 60%;
          float: left;
        }

        .canvasFlow_variablesModal_form_right {
          width: 40%;
          float: right;
          text-align: end;
        }
      }
    }

    .operationArray {
      display: flex;

      svg {
        width: 12px;
        height: 12px;
      }

      .deleteClass {
        :global {
          .ant-btn {
            border: 1px solid #f22417;
          }
        }
      }

      :global {
        .ant-btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
        }
      }
    }

    .varInput {
      height: 32px !important;
      border: 1px solid #e6e6e6 !important;
      border-radius: 4px;
    }

    :global {
      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #3463fc;
        text-shadow: 0 0 0.25px currentcolor;
        font-weight: 700;
      }

      .ant-modal-body {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 45vh;
        overflow-y: hidden;
      }

      .ant-modal-content {
        width: 51vw;
        height: 58vh;
        margin-top: 20%;
        border-radius: 10px;
        border: 1px solid #e6e6e6;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);

        .ant-modal-header {
          border-radius: 10px;
          background: none;
        }

        .ant-modal-title {
          font-size: 20px !important;
          font-weight: 700 !important;
        }

        .ant-modal-close-icon {
          font-size: 20px;

          svg path {
            fill: rgba(51, 51, 51, 1);
          }
        }
      }

      .ant-input-affix-wrapper {
        border-radius: 4px;
        width: 47%;
        height: 32px;
        margin-right: 20px;
      }

      .ant-input {
        height: 24px;
      }

      .ant-input-affix-wrapper:focus,
      .ant-input-affix-wrapper-focused {
        box-shadow: none;
      }

      .ant-select {
        width: 47%;
      }

      .ant-select-selector {
        border-radius: 4px !important;
        border: 1px solid #e6e6e6 !important;
        box-shadow: none !important;
        background: #fff;
        color: #333;
        font-size: 12px;
      }

      .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab,
      .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {
        background: transparent;
        border: none;
        padding: 4px 8px;
        font-size: 12px;
        color: #999;
        transition: none;
      }

      .ant-tabs-nav-list {
        margin-left: 30px;
      }

      .ant-tabs-top > .ant-tabs-nav::before,
      .ant-tabs-bottom > .ant-tabs-nav::before,
      .ant-tabs-top > div > .ant-tabs-nav::before,
      .ant-tabs-bottom > div > .ant-tabs-nav::before {
        border-bottom: 1px solid #3463fc;
      }

      .ant-tabs-tab-active {
        border-radius: 4px 4px 0px 0px !important;
        border-top: 1px solid #3463fc !important;
        border-right: 1px solid #3463fc !important;
        border-left: 1px solid #3463fc !important;
        background: #fff !important;
      }
    }
  }

  .canvasFlow_settingModal {
    margin: 0 !important;

    .canvasFlow_settingModal_content {
      .canvasFlow_settingModal_title {
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 150%;
        /* 18px */
        margin-bottom: 10px;
      }

      .canvasFlow_settingModal_tip {
        color: #999;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        /* 18px */
        margin-bottom: 10px;
      }

      .canvasFlow_settingModal_add {
        margin-bottom: 15px;
      }

      .canvasFlow_settingModal_table {
        img {
          width: 18px;
          margin-right: 8px;
          cursor: pointer;
        }
      }
    }

    .canvasFlow_modalFoot {
      text-align: center;

      :global {
        .ant-btn {
          font-size: 14px !important;
        }

        .ant-btn:nth-child(1) {
          margin-right: 20px;
        }
      }
    }

    :global {
      .ant-modal-body {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: scroll;
      }

      .ant-modal-content {
        width: 82vw;
        height: 80vh;
        margin-top: 6%;
        margin-left: 44%;
        border-radius: 10px;
        border: 1px solid #e6e6e6;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);

        .ant-modal-header {
          border-radius: 10px;
          background: none;
        }

        .ant-modal-title {
          font-size: 20px !important;
          font-weight: 700 !important;
        }

        .ant-modal-close-icon {
          font-size: 20px;

          svg path {
            fill: rgba(51, 51, 51, 1);
          }
        }
      }

      .ant-input-affix-wrapper {
        border-radius: 4px;
        width: 47%;
        height: 32px;
        margin-right: 20px;
      }

      .ant-input {
        height: 24px;
      }

      .ant-input-affix-wrapper:focus,
      .ant-input-affix-wrapper-focused {
        box-shadow: none;
      }

      .ant-select {
        width: 47%;
      }

      .ant-select-selector {
        border-radius: 4px !important;
        border: 1px solid #e6e6e6 !important;
        box-shadow: none !important;
        background: #fff;
        color: #333;
        font-size: 12px;
      }

      .ant-form-item {
        margin-bottom: 0px;
      }

      .ant-input-number,
      .ant-input {
        border-radius: 6px;
        background: #fff;
        font-size: 12px;
      }

      .ant-col-3 {
        line-height: 32px;
        padding-left: 0px !important;
        padding-right: 0px !important;
        text-align: center;
      }

      .ant-col-8 {
        padding-right: 5px !important;
        padding-left: 5px !important;

        .ant-input-number {
          width: 100%;
        }
      }

      .ant-input-number-handler-wrap {
        border-radius: 0px 6px 6px 0px;
      }
    }
  }

  .canvasFlow_apiManageModal {
    display: flex;
    margin: 0 !important;

    .api_manage_left {
      min-width: 174px;
      // max-height: 450px;
      // overflow-y: scroll;
      padding-right: 20px;
      border-right: 1px solid #e6e6e6;
      position: relative;

      .search_box {
        width: 100%;
        margin-bottom: 20px;

        :global {
          .ant-input-affix-wrapper {
            padding: 0 10px;
          }
        }
      }

      .api_list {
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-height: 400px;
        overflow-y: scroll;

        .list_item {
          display: flex;
          gap: 10px;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          padding-right: 5px;
          color: #333;
          border-radius: 4px;
          min-height: 31px;

          &:hover {
            background-color: #ebefff;
          }

          .item_content {
            padding: 5px;
            flex: 1;
            min-width: max-content;
          }

          .item_content_input {
            flex: 1;

            :global {
              .ant-input {
                height: 100%;
                font-size: 12px;
                padding-left: 5px;
              }
            }
          }

          .item_operation {
            display: flex;
            gap: 5px;

            img {
              width: 18px;
              height: 18px;
              cursor: pointer;
            }
          }
        }
      }

      .add_api_button {
        position: fixed;
        left: 2%;
        bottom: 5%;
        width: 200px;
      }
    }

    .api_manage_right {
      flex: 1;
      overflow: hidden;

      .api_manage_right_tabs {
        display: flex;
        border-bottom: 1px solid #e6e6e6;
        padding-left: 20px;
        overflow-x: scroll;
        max-width: 100%;

        // 隐藏滚动条
        &::-webkit-scrollbar {
          display: none;
        }

        .api_manage_right_tab {
          min-width: max-content;
          padding: 0 17px 0 17px;
          color: #666;
          font-family: 'MicrosoftYaHei Regular';
          font-size: 12px;
          cursor: pointer;
          overflow-x: scroll;
          border-bottom: 2px solid transparent;

          &:hover {
            color: #3463fc;
            font-weight: 700;
          }
        }

        .api_manage_right_tab_active {
          color: #3463fc;
          font-weight: 700;
          // font-size: 14px;
          border-bottom: 2px solid #3463fc;
        }
      }

      .api_manage_right_api_test {
        display: flex;
        gap: 20px;
        margin-top: 20px;
        margin-left: 20px;

        .api_manage_right_api_test_url {
          flex: 1;

          :global {
            .ant-input {
              border-right: none;
              border-radius: 6px 0 0 6px;
              height: 32px !important;
            }

            .ant-input-group-addon {
              background-color: transparent;

              span {
                display: flex;
              }
            }
          }
        }
      }

      .api_manage_right_api_test_body {
        margin-top: 20px;

        .api_manage_right_api_test_body_tab {
          display: flex;
          gap: 10px;
          border-bottom: 1px solid #3463fc;
          padding-left: 20px;
          position: relative;

          .api_test_body_tab_item {
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            color: #666;
            font-size: 14px;
            font-family: 'MicrosoftYaHei Regular';
            position: relative;
          }

          .api_test_body_tab_item_active {
            border: 1px solid #3463fc;
            border-bottom: none;
            color: #3463fc;
            border-radius: 4px 4px 0px 0px;

            &::after {
              content: '';
              position: absolute;
              bottom: -1px;
              left: 0;
              width: 100%;
              height: 1px;
              background-color: #fff;
            }
          }
        }

        .api_test_body_content {
          padding: 20px;

          // 深度
          :global {
            .highLightTextarea {
              width: 100%;
            }
          }

          .api_test_body_content_title {
            color: #333;
            font-family: 'MicrosoftYaHei Regular';
            font-size: 12px;
            font-weight: 700;
            margin-bottom: 10px;
          }

          .api_manage_right_table {
            .api_manage_right_table_header {
              margin-top: 10px;
            }

            :global {
              .ant-table-cell {
                padding: 5px;

                .ant-input {
                  height: 100%;
                  padding: 0;
                  border: none;
                  background: transparent;

                  &:focus {
                    box-shadow: none;
                  }
                }
              }

              .ant-input-group-addon {
                background-color: transparent;
                border: none;
                vertical-align: bottom;
              }
            }
          }

          .api_auth {
            font-size: 12px;

            .api_auth_type {
              margin: 10px 0;
            }

            .api_auth_label {
              margin: 10px 0;
            }

            .api_auth_input {
              border: 1px solid #d9d9d9;
              border-radius: 6px;

              :global {
                .ant-input {
                  border-right: none;
                  border-radius: 6px 0 0 6px;
                  height: 32px;
                  border: none;
                }

                .ant-input-group-addon {
                  background-color: transparent;
                  border: none;

                  span {
                    display: flex;
                  }
                }
              }
            }
          }
        }
      }

      .api_manage_right_table_response {
        margin-top: 20px;
        margin-left: 20px;
        font-size: 12px;
      }

      .api_manage_right_empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        > span {
          color: #999999;
          margin: 20px 0;
          font-size: 12px;
        }
      }
    }

    :global {
      .ant-modal-body {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: scroll;
        flex: 1;

        .ant-spin-nested-loading {
          height: 100%;

          .ant-spin-container {
            height: 100%;
          }
        }
      }

      .ant-modal-content {
        display: flex;
        flex-direction: column;
        width: 82vw;
        height: 80vh;
        margin-top: 6%;
        margin-left: 44%;
        border-radius: 10px;
        border: 1px solid #e6e6e6;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);

        .ant-modal-header {
          border-radius: 10px;
          background: none;
          width: 82vw;
        }

        .ant-modal-title {
          font-size: 20px !important;
          font-weight: 700 !important;
        }

        .ant-modal-close-icon {
          font-size: 20px;

          svg path {
            fill: rgba(51, 51, 51, 1);
          }
        }
      }

      .ant-input-affix-wrapper {
        border-radius: 4px;
        width: 47%;
        height: 32px;
        margin-right: 20px;
      }

      .ant-input {
        // height: 24px;
      }

      .ant-input-affix-wrapper:focus,
      .ant-input-affix-wrapper-focused {
        box-shadow: none;
      }

      .ant-select {
        width: 47%;
      }

      .ant-select-selector {
        border-radius: 4px !important;
        border: 1px solid #e6e6e6 !important;
        box-shadow: none !important;
        background: #fff;
        color: #333;
        font-size: 12px;
      }

      .ant-form-item {
        margin-bottom: 0px;
      }

      .ant-input-number,
      .ant-input {
        border-radius: 6px;
        background: #fff;
        font-size: 12px;
      }

      .ant-col-3 {
        line-height: 32px;
        padding-left: 0px !important;
        padding-right: 0px !important;
        text-align: center;
      }

      .ant-col-8 {
        padding-right: 5px !important;
        padding-left: 5px !important;

        .ant-input-number {
          width: 100%;
        }
      }

      .ant-input-number-handler-wrap {
        border-radius: 0px 6px 6px 0px;
      }
    }
  }

  .numBox {
    display: flex;
    padding: 3px 4px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 4px;
    background: linear-gradient(0deg, #e5f8e5 0%, #e5f8e5 100%), #fff;
    color: #00b900;
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 12px;
    margin-left: 5px;
    /* 100% */
  }

  :global {
    .react-flow {
      .react-flow__controls {
        z-index: 889 !important;
        display: flex;
        padding: 4px;
        align-items: center;
        gap: 10px;
        border-radius: 10px;
        border: 1px solid #e6e6e6;
        height: 52px;
      }

      .react-flow__controls-fitview {
        order: 3;
      }

      .react-flow__controls-interactive {
        order: 4;
      }

      .react-flow__controls-button {
        border: none;
        background: transparent;
      }

      .react-flow__controls-button svg {
        max-height: 18px !important;
        max-width: 18px !important;
      }

      .react-flow__handle-right {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #3463fc;
        border: none;
        right: -8px;
      }

      .react-flow__handle-left {
        width: 20%;
        height: 100%;
        opacity: 0;
      }

      .react-flow__edge-path {
        stroke: #3463fc;
      }

      .react-flow__nodes {
        .react-flow__node-modalNodes {
          background: #fff;
        }

        .selected {
          border-radius: 12px;
          border: 2px solid #3463fc;
          background: linear-gradient(0deg, #ebefff 0%, #ebefff 100%), #fff !important;
          /* 卡信息片 */
          box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

.canvasFlow_header_deploy_dropdown {
  :global {
    .ant-dropdown-menu {
      overflow: scroll;
      max-height: 65vh;
    }

    .ant-dropdown-menu-item {
      .ant-dropdown-menu-item-icon {
        margin-top: 6px;
        float: right;
        margin-left: 20px;
      }

      display: block;
    }
  }
}
