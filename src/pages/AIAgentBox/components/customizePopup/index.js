import styles from './style.less';
import { PopupCloseIcon } from './icon.js';
import { FormattedMessage, getIntl, useDispatch } from 'umi';
import { Input, Button } from 'antd';
import { useReactFlow } from 'react-flow-renderer';
import { useEffect, useState } from 'react';
import MessageText from './components/MessageText/index';
import MessageImage from './components/MessageImage/index';
import MessageRichText from './components/MessageRichText/index';
import MessageVideo from './components/MessageVideo/index';
import MessageDoc from './components/MessageDoc/index';
import StartNode from './components/StartNode/index';
import ToolFailure from './components/ToolFailure/index';
import ToolLLM from './components/ToolLLM/index';
import ToolToAgent from './components/ToolToAgent/index';
import AskQuestionText from './components/AskQuestionText/index';
import AskQuestionButton from './components/AskQuestionButton/index';
import AskQuestionLLM from './components/AskQuestionLLM/index';
import AskQuestionForm from './components/AskQuestionForm/index';
import ConditionIntent from './components/ConditionIntent/index';
import ToolRag from './components/ToolRag/index';
import ToolAPI from './components/ToolAPI/index';
import ToolWorkHours from './components/ToolWorkHours/index';
import ToolVariableSetting from './components/ToolVariableSetting/index';
import ToolUpdateCustomer from './components/ToolUpdateCustomer/index';
import ToolUpdateTicket from './components/ToolUpdateTicket/index';
import ConditionCheck from './components/ConditionCheck/index';
import ToolSetCustomerTag from './components/ToolSetCustomerTag/index';
import MessageHotIssue from './components/MessageHotIssue/index';
import AskQuestionCard from './components/AskQuestionCard/index';
const CustomizePopup = ({
  node,
  setCurrentNodes,
  setIsModalOpenVariables,
  setIsModalOpenApi,
}) => {
  const { id, data } = node;
  const { componentName, componentColor, componentType, nodeAlias, key } =
    data || {};
  const { setNodes } = useReactFlow();
  const [componentTitleValue, setComponentTitleValue] = useState(nodeAlias);
  const dispatch = useDispatch();

  //删除节点
  const closePopup = e => {
    e.stopPropagation();
    setNodes(nds => {
      let newNds = nds?.map(item => {
        let it = { ...item };
        if (item.id === id) {
          it.data.isEdit = false;
          setCurrentNodes(it); //修改外层currentNode保证关闭
        }
        return it;
      });
      return newNds;
    });
  };

  //编辑组件自定义名称
  useEffect(() => {
    if (nodeAlias !== componentTitleValue) {
      setNodes(nds => {
        let newNds = nds?.map(item => {
          let it = item;
          if (item.id === id) {
            it.data.nodeAlias = componentTitleValue;
          }
          return it;
        });
        return newNds;
      });
      //因为版本问题，不能同步响应修改title，所以使用框架中的redux实现
      dispatch({
        type: 'aiagent/setCurrentTitle',
        payload: componentTitleValue,
      }); // tab值存入全局
    }
  }, [componentTitleValue, setNodes]);
  //编辑组件自定义名称
  useEffect(() => {
    if (nodeAlias !== componentTitleValue && nodeAlias) {
      setComponentTitleValue(nodeAlias);
      dispatch({
        type: 'aiagent/setCurrentTitle',
        payload: nodeAlias,
      }); // tab值存入全局
    }
  }, [nodeAlias]);
  return (
    <div className={styles.popupBox}>
      {/* 自定义标题*/}
      <div
        className={styles.popupBoxTitle}
        style={{ backgroundColor: componentColor }}
      >
        <div style={{ display: 'flex', alignItems: 'center', width: '90%' }}>
          <div style={{ whiteSpace: 'nowrap' }}>{componentName}</div>
          <Input
            placeholder={getIntl().formatMessage({
              id: 'ai.agent.nodes.form.popup.p',
            })}
            value={componentTitleValue}
            onChange={e => {
              setComponentTitleValue(e.target.value);
            }}
          />
        </div>
        <span onClick={e => closePopup(e)}>{PopupCloseIcon()}</span>
      </div>
      {/* 自定义内容*/}
      <div className={styles.popupBoxContent}>
        {(() => {
          switch (componentType) {
            case 'Start':
              return <StartNode node={node} key={key} />;
            case 'MessageText':
              return <MessageText node={node} key={key} />;
            case 'MessageRichText':
              return <MessageRichText node={node} key={key} />;
            case 'MessageImage':
              return <MessageImage node={node} key={key} />;
            case 'MessageMedia':
              return <MessageVideo node={node} key={key} />;
            case 'MessageDoc':
              return <MessageDoc node={node} key={key} />;
            case 'ToolToAgent':
              return <ToolToAgent node={node} key={key} />;
            case 'ToolLLM':
              return (
                <ToolLLM
                  setIsModalOpenVariables={setIsModalOpenVariables}
                  node={node}
                  key={key}
                />
              );
            case 'ToolFailure':
              return <ToolFailure node={node} key={key} />;
            case 'AskQuestionText':
              return (
                <AskQuestionText
                  setIsModalOpenVariables={setIsModalOpenVariables}
                  node={node}
                  key={key}
                />
              );
            case 'AskQuestionButton':
              return (
                <AskQuestionButton
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'AskQuestionForm':
              return (
                <AskQuestionForm
                  setIsModalOpenVariables={setIsModalOpenVariables}
                  node={node}
                  key={key}
                />
              );
            case 'AskQuestionLLMInfoCollection':
              return (
                <AskQuestionLLM
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'ConditionIntent':
              return (
                <ConditionIntent
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'ToolRag':
              return <ToolRag node={node} key={key} />;
            case 'ToolAPI':
              return (
                <ToolAPI
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                  setIsModalOpenApi={setIsModalOpenApi}
                />
              );
            case 'ToolWorkHours':
              return <ToolWorkHours node={node} key={key} />;
            case 'ToolVariableSetting':
              return (
                <ToolVariableSetting
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'ToolUpdateCustomer':
              return (
                <ToolUpdateCustomer
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'ToolUpdateTicket':
              return (
                <ToolUpdateTicket
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'ConditionCheck':
              return (
                <ConditionCheck
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'MessageHotIssue':
              return (
                <MessageHotIssue
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'ToolSetCustomerTag':
              return (
                <ToolSetCustomerTag
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
            case 'AskQuestionCard':
              return (
                <AskQuestionCard
                  node={node}
                  key={key}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                />
              );
          }
        })()}
      </div>
    </div>
  );
};

export default CustomizePopup;
