import React, { useState, useRef, useEffect } from 'react';
import './style.less';
import { Dropdown, notification } from 'antd';
import Picker from '@emoji-mart/react';
import data from '@emoji-mart/data';
import { ConstIcon, EmojiIcon } from './icon.js';
import { getIntl, useDispatch } from 'umi';

const HighlighterInput = props => {
  const {
    setText, // 设置输入框内容
    text = '', // 输入框内容
    index = 0, // 当前所在index
    index2 = 0, // 当前所在index2
    setKey = '', // 设置的key
    noVar = false, // 是否不显示变量
    noEmoji = false, // false显示表情 true不显示表情
    backgroundColor = 'transparent', // 背景颜色穿参
    emojiTop = false,
    showEnd = '', // 展示结束符
    placeholder = '',
    mode = 'input', // input 输入 select 选择
    disabled = false, // 是否禁用
  } = props;
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef(null);
  const highlighterRef = useRef(null);
  // 展示表情包
  const [shownEmoji, setShownEmoji] = useState(false);
  const [items, setItems] = useState([]);
  const [loadingVar, setLoadingVar] = useState(false);
  const dispatch = useDispatch();
  const emojiTriggerRef = useRef(null); // 用于引用表情图标触发元素
  const emojiPickerRef = useRef(null); // 用于引用 Picker 组件

  useEffect(() => {
    // queryCurrentVar();
    setInputValue(text);
  }, []);

  // 禁止输入可以通过设置 input 的 disabled 属性为 true 实现
  // 这里是高亮函数，不涉及输入控制，输入控制应在 input 组件上设置 disabled
  // 下面是原高亮函数，无需更改
  const highlightText = text => {
    const escapedText = text
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\n$/g, '<br/>&nbsp;')
      .replace(/\n/g, '<br/>')
      .replace(/ /g, '&nbsp;');

    // 只匹配 {} 内部以 var_xxx. 开头的变量
    return escapedText.replace(
      /(\{(var_agent|var_session|var_global|var_system)\.[^}]*\})/g,
      (match, fullMatch, prefix) => {
        let className = '';
        if (prefix === 'var_agent') {
          className = 'highlightAgent';
        } else if (prefix === 'var_session') {
          className = 'highlightSession';
        } else if (prefix === 'var_global') {
          className = 'highlightGlobal';
        } else if (prefix === 'var_system') {
          className = 'highlightSystem';
        }
        return `<span class="${className}">${fullMatch}</span>`;
      },
    );
  };

  const handleInputChange = e => {
    setInputValue(e.target.value);
    // 节流处理：输入停止2秒后再调用 setText
    if (window.inputThrottleTimer) {
      clearTimeout(window.inputThrottleTimer);
    }
    const value = e && e.target ? e.target.value : '';
    window.inputThrottleTimer = setTimeout(() => {
      setText && setText(value, index, index2, setKey);
    }, 2000);
  };

  const handleScroll = () => {
    if (inputRef.current && highlighterRef.current) {
      highlighterRef.current.scrollLeft = inputRef.current.scrollLeft;
    }
  };

  const onShownEmoji = () => {
    setShownEmoji(!shownEmoji);
  };

  // 选择表情
  const handleEmojiSelect = emoji => {
    const input = inputRef.current;
    if (input) {
      const start = input.selectionStart;
      const end = input.selectionEnd;
      let currentValue = input.value;
      // 插入文本
      currentValue =
        currentValue.substring(0, start) +
        emoji.native +
        currentValue.substring(end);
      setInputValue(currentValue);
      setText(currentValue, index, index2, setKey);

      // 更新光标位置
      input.setSelectionRange(
        start + emoji.native.length,
        start + emoji.native.length,
      );
      // 重新聚焦
      input.focus();
    }

    setShownEmoji(!shownEmoji);
  };

  const handleClickValue = e => {
    if (e !== null && e !== undefined) {
      const input = inputRef.current;
      if (input) {
        if (mode == 'select') {
          setInputValue(e);
          setText(e, index, index2, setKey);
          input.setSelectionRange(e.length, e.length);
        } else {
          const start = input.selectionStart;
          const end = input.selectionEnd;
          let currentValue = input.value;
          // 插入文本
          currentValue =
            currentValue.substring(0, start) + e + currentValue.substring(end);
          setInputValue(currentValue);
          setText(currentValue, index, index2, setKey);
          // 更新光标位置
          input.setSelectionRange(start + e.length, start + e.length);
          // 重新聚焦
        }
        // input.focus();
      }
    }
  };

  // 查询变量
  const queryCurrentVar = () => {
    // console.log('highLightText>>>请求了');
    setLoadingVar(true);
    try {
      let aiAgentId = localStorage.getItem('aiAgentId');
      dispatch({
        // type: 'aiagent/queryGroupVariables',
        type: 'aiagent/queryCurrentVar',
        payload: {
          id: aiAgentId, // 智能体 ID
        },
        callback: response => {
          // console.log('highLightText>>>', response);
          setLoadingVar(false);
          let { code, data, msg } = response;
          if (200 === code) {
            let arr3 = [];
            let arr2 = [];
            let arr4 = [];
            let arr1 = [];
            // 变量类型（1.系统内置变量，2当前会话变量，3当前智能体变量，4全局变量）
            data?.forEach(item => {
              if (item.variableType === 3) {
                arr3.push(item);
              } else if (item.variableType === 2) {
                arr2.push(item);
              } else if (item.variableType === 4) {
                arr4.push(item);
              } else if (item.variableType === 1) {
                arr1.push(item);
              }
            });
            setItems([
              {
                key: '3',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.1',
                  defaultValue: '当前智能体变量',
                }),
                children: arr3.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleClickValue('{var_agent.' + item.variableName + '}'),
                  };
                }),
              },
              {
                key: '2',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.2',
                  defaultValue: '当前会话变量',
                }),
                children: arr2.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleClickValue(
                        '{var_session.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '4',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.3',
                  defaultValue: '全局变量',
                }),
                children: arr4.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleClickValue(
                        '{var_global.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '1',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.4',
                  defaultValue: '系统内置变量',
                }),
                children: arr1.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleClickValue(
                        '{var_system.' + item.variableName + '}',
                      ),
                  };
                }),
              },
            ]);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    } catch {
      setLoadingVar(false);
    }
  };

  // 点击外部关闭 Picker 组件
  const handleOutsideClick = e => {
    if (
      shownEmoji &&
      emojiTriggerRef.current &&
      !emojiTriggerRef.current.contains(e.target) &&
      emojiPickerRef.current &&
      !emojiPickerRef.current.contains(e.target)
    ) {
      setShownEmoji(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOutsideClick);
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [shownEmoji]);

  return (
    <div
      className="highLighterInput"
      style={{ backgroundColor: backgroundColor }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: 'row',
          flexWrap: 'nowrap',
        }}
      >
        <div className="inputArea" style={{ width: noEmoji ? '90%' : '80%' }}>
          <div
            className="highlighter"
            ref={highlighterRef}
            dangerouslySetInnerHTML={{
              __html: highlightText(inputValue),
            }}
          ></div>
          <input
            className="input"
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onScroll={handleScroll}
            type="text"
            placeholder={placeholder}
            disabled={disabled}
          />
        </div>
        <div className="toolDiv">
          <div
            style={{ zIndex: '1000', display: 'flex', alignItems: 'center' }}
          >
            {!noVar && (
              <Dropdown
                onClick={() => {
                  console.log('点击了');
                  queryCurrentVar();
                }}
                menu={{
                  items,
                }}
                trigger={['click']}
                dropdownRender={menu => (
                  <div
                    style={{
                      marginTop: 2,
                      maxHeight: '400px',
                      border: '1px solid #e6e6e6',
                      borderRadius: 4,
                      overflowY: 'auto',
                    }}
                  >
                    {menu}
                  </div>
                )}
              >
                <a>
                  <span style={{ marginRight: 10 }}>{ConstIcon()}</span>
                </a>
              </Dropdown>
            )}
            <span
              onClick={() => onShownEmoji()}
              style={{ display: noEmoji ? 'none' : 'block', cursor: 'pointer' }}
              ref={emojiTriggerRef}
            >
              {EmojiIcon()}
            </span>
            {showEnd !== '' && (
              <div
                style={{
                  width: 48,
                  maxWidth: 48,
                  textAlign: 'center',
                  background: '#3463FC',
                  transform: 'scale(0.8)',
                  color: '#fff',
                  marginTop: -4,
                  marginLeft: -4,
                  borderRadius: 4,
                }}
              >
                {showEnd}
              </div>
            )}
          </div>
        </div>
        <div
          className="emojiDetailContainer"
          style={{
            display: shownEmoji ? 'block' : 'none',
            top: emojiTop ? emojiTop + 'px' : index * 45 + 240 + 'px',
          }}
          ref={emojiPickerRef}
        >
          <Picker
            data={data}
            onEmojiSelect={e => handleEmojiSelect(e)}
            emojiButtonSize={24}
            navPosition={'none'}
            theme={'light'}
            skinTonePosition={'none'}
            searchPosition={'none'}
            previewPosition={'none'}
            emojiButtonRadius={'4px'}
            emojiSize={'16'}
            maxFrequentRows={0}
            perLine={10}
          />
        </div>
      </div>
    </div>
  );
};

export default HighlighterInput;
