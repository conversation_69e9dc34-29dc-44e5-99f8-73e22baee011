import {
  Handle,
  Position,
  useReactFlow,
  applyNodeChanges,
} from 'react-flow-renderer';
import { useDrag } from 'react-dnd';
import styles from './style.less';
import { NodeCopyIcon, NodeDeteleIcon } from './icon.js';
import NodeContent from './components/nodeContent/index';
import { useEffect, useState } from 'react';
import { useSelector, FormattedMessage } from 'umi';
import { v4 as uuidv4 } from 'uuid';

const ModalNodes = props => {
  // console.log('以下是节点的所有已有属性', props);
  const { currentTitle, currentVariables } = useSelector(({ aiagent }) => ({
    currentTitle: aiagent.currentTitle,
    currentVariables: aiagent.currentVariables,
  }));

  const { id, isConnectable, data, selected } = props;
  const {
    componentName,
    isCanvas = false,
    componentColor,
    nodeId,
    componentMiniIcon,
    isStart,
    handleList,
    nodeAlias,
    inHandleProgress,
    isHover,
    isSource,
    componentStyle,
    componentType,
    key,
    setNodesDraggable,
    nodesDraggable,
  } = data || {};
  const { setNodes, setEdges } = useReactFlow();
  const [titleHeight, setTitleHeight] = useState(30);

  //拿起操作，携带数据，拿起放下type=node
  const [_, drag] = useDrag(() => ({
    type: 'node',
    item: { ...data },
  }));

  //删除节点
  const deleteNode = () => {
    let nodeName = '';
    let nodeId = '';
    //以下逻辑用于限制一个句柄仅可以产出一条连接线
    setEdges(edges =>
      edges.filter(edge => {
        if (edge.target !== id) {
          return edge;
        } else {
          nodeId = edge.source;
          nodeName = edge.sourceHandle;
        }
      }),
    );

    setNodes(nds => {
      let newNds = nds.filter((item, i) => {
        if (item.id === nodeId) {
          item.data.handleList?.forEach(handle => {
            if (handle.id === nodeName || (!nodeName && !handle.id)) {
              handle.isConnectable = true;
            }
          });
          return item;
        } else if (item.id !== id) {
          return item;
        }
      });
      return newNds;
    });
  };
  //复制节点
  const copyNode = () => {
    setNodes(nds => {
      let newNds = nds.filter((item, i) => item.id === id)[0];
      let x = newNds.position.x;
      let y = newNds.position.y;
      let uuid = uuidv4();
      let newHandleList = newNds.data.handleList?.map(handle => {
        return { ...handle, isConnectable: true };
      });
      return [
        ...nds,
        {
          ...newNds,
          id: uuid,
          position: { x: x + 50, y: y + 50 },
          selected: false,
          data: {
            ...newNds.data,
            nodeId: uuid,
            customizForms: JSON.parse(
              JSON.stringify(newNds.data.customizForms),
            ),
            handleList: newHandleList,
          },
        },
      ];
    });
  };
  //编辑节点内容
  const handleEditNode = e => {
    if (componentType === 'ToolFailure' || componentType === 'ToolDelay') {
      return false;
    }
    e.stopPropagation();
    setNodes(nds => {
      let newNds = nds?.map(item => {
        let it = item;
        if (item.id === id) {
          it.data.isEdit = true;
        }
        return it;
      });
      return newNds;
    });
  };

  useEffect(() => {
    console.log(props, 'modalNodes组件');
    // 拿到modalNodesTitle的高度
    const modalNodesTitle = document.querySelector('.modalNodesTitle');
    let height = titleHeight;
    if (modalNodesTitle) {
      height = modalNodesTitle.getBoundingClientRect().height;
    }
    setTitleHeight(height);
  }, [nodeAlias, componentName]);
  return (
    <div
      ref={isCanvas ? null : drag}
      className={`${styles.modalNodes} modalNodes_${id}`}
      style={{ cursor: isHover ? 'pointer' : '', ...componentStyle }}
      key={key}
    >
      {/* 自定义删除复制操作*/}
      <div
        className={styles.modalNodesOperation}
        style={{
          visibility: (selected || isHover) && !isStart ? '' : 'hidden',
        }}
      >
        <span onClick={() => copyNode()}>{NodeCopyIcon()}</span>
        <span onClick={() => deleteNode()}> {NodeDeteleIcon()}</span>
      </div>
      {/* 自定义图标 */}
      {/* <span

        style={{ border: `1px solid ${componentColor}` }}
      > */}
      <img
        src={`https://${process.env.DOMAIN_NAME_OVER}/static-icon/tools/${componentMiniIcon}`}
        width={20}
        height={20}
        className={styles.modalNodesTitleIcon}
      />
      {/* </span> */}
      {/* 自定义标题*/}
      <div
        className={styles.modalNodesTitle}
        style={{ backgroundColor: componentColor }}
      >
        <div style={{ display: nodeAlias ? '' : 'none' }}>{nodeAlias}</div>
        <div style={{ display: nodeAlias ? 'none' : '' }}>{componentName}</div>
      </div>
      {/* 自定义内容*/}
      {localStorage.getItem('aiAgentType') == 2 && componentType === 'Start' ? (
        <div style={{ padding: 10, color: '#333', fontSize: 12 }}>
          <p style={{ fontWeight: 700 }}>
            <FormattedMessage
              id="ai.agent.nodes.start.type.2"
              defaultMessage="默认欢迎智能体"
            />
          </p>
          <div style={{ color: '#939393' }}>
            <FormattedMessage id="ai.agent.nodes.start.type.2.content" />
          </div>
        </div>
      ) : localStorage.getItem('aiAgentType') == 3 &&
        componentType === 'Start' ? (
        <div style={{ padding: 10, color: '#333', fontSize: 12 }}>
          <p style={{ fontWeight: 700 }}>
            <FormattedMessage
              id="ai.agent.nodes.start.type.3"
              defaultMessage="默认Fallback意图"
            />
          </p>
          <div style={{ color: '#939393' }}>
            <FormattedMessage id="ai.agent.nodes.start.type.3.content" />
          </div>
        </div>
      ) : (
        <div
          className={styles.modalNodesContent}
          onClick={e => handleEditNode(e)}
        >
          <NodeContent
            setNodesDraggable={setNodesDraggable}
            nodesDraggable={nodesDraggable}
            content={props}
            titleHeight={titleHeight}
            key={key}
          ></NodeContent>
        </div>
      )}
      {/* 左右句柄，可多个 */}
      {isCanvas && !isStart && !isSource && (
        <Handle
          type="target"
          position={Position.Left}
          style={{
            width: inHandleProgress ? '100%' : '10px',
            height: inHandleProgress ? '100%' : '10px',
          }}
        />
      )}
      {isCanvas && (
        <>
          {handleList?.map(item => {
            return (
              <Handle
                type="source"
                position={item.position}
                id={item.id}
                style={item.style}
                isConnectable={item.isConnectable}
              />
            );
          })}
        </>
      )}
    </div>
  );
};

export default ModalNodes;
