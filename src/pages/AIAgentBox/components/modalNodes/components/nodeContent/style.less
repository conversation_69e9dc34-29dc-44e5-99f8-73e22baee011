.nodeContent {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.hotIssuetype1 {
  > :nth-child(1) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    width: 100%;
    border-bottom: 1px solid #e6e6e6;
    transition: all 0.3s ease;
    border-radius: 4px;
  }
  > :nth-child(1):hover {
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  }
}
.hotIssuetype1_content {
  padding: 5px 10px;
  border-radius: 4px;
}
.hotIssuetype1_content_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0px;
  width: 100%;
  border-bottom: 1px solid #e6e6e6;
  transition: all 0.3s ease;
}
.hotIssuetype1_content_item:hover {
  color: #3463fc;
  transform: all 0.3s ease;
}
.hotIssuetype2 {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}
.hotIssuetype2:hover {
  color: #3463fc;
  transition: all 0.3s ease;
}
.start {
}
.MessageRichText {
  img {
    max-width: 100%;
    max-height: 100%;
  }
  video {
    max-width: 100%;
    max-height: 100%;
  }
}
.colorType1 {
  border-radius: 4px;
  border: 1px solid #3463fc;
  background: rgba(52, 99, 252, 0.1);

  span {
    color: #3463fc;
  }
}

.colorType2 {
  border-radius: 4px;
  border: 1px solid #00b900;
  background: rgba(0, 185, 0, 0.1);

  span {
    color: #00b900;
  }
}

.colorType3 {
  border-radius: 4px;
  border: 1px solid #ad30e5;
  background: rgba(173, 48, 229, 0.1);

  span {
    color: #ad30e5;
  }
}

.colorType4 {
  border-radius: 4px;
  border: 1px solid #2d6973;
  background: rgba(45, 105, 115, 0.1);

  span {
    color: #2d6973;
  }
}

.colorType5 {
  border-radius: 4px;
  border: 1px solid #f22417;
  background: rgba(242, 36, 23, 0.1);

  span {
    color: #f22417;
  }
}

.colorType6 {
  border-radius: 4px;
  border: 1px solid #d7ce1e;
  background: rgba(215, 206, 30, 0.1);

  span {
    color: #d7ce1e;
  }
}

.colorType7 {
  border-radius: 4px;
  border: 1px solid #86bf00;
  background: rgba(134, 191, 0, 0.1);

  span {
    color: #86bf00;
  }
}

.colorType8 {
  border-radius: 4px;
  border: 1px solid #fb8f83;
  background: rgba(251, 143, 131, 0.1);

  span {
    color: #fb8f83;
  }
}

.colorType9 {
  border-radius: 4px;
  border: 1px solid #64635e;
  background: rgba(100, 99, 94, 0.1);

  span {
    color: #64635e;
  }
}

.colorType10 {
  border-radius: 4px;
  border: 1px solid #9c9992;
  background: rgba(156, 153, 146, 0.1);

  span {
    color: #9c9992;
  }
}

.colorType12 {
  border-radius: 4px;
  border: 1px solid #ee6521;
  background: rgba(238, 101, 33, 0.1);

  span {
    color: #ee6521;
  }
}

.colorType13 {
  border-radius: 4px;
  border: 1px solid #fb83f5;
  background: rgba(251, 131, 245, 0.1);

  span {
    color: #fb83f5;
  }
}
#askQuestionButton {
  // overflow-x: auto;
  // overflow-y: hidden;
  // white-space: nowrap;
  // overflow: hidden;
  // text-overflow: ellipsis;
  max-width: 200px;
  min-width: 160px;
  border: 1px solid #3463fc;
  padding: 5px 10px;
  text-align: left;
  border-radius: 4px;
  background: white;
  color: #3463fc;
  min-height: 30px;
}
// /* 滚动条整体样式（适用于 WebKit 浏览器，如 Chrome、Safari） */
// #askQuestionButton::-webkit-scrollbar {
//   height: 5px;
//   /* 设置滚动条高度（窄滚动条） */
// }

// /* 滚动条轨道样式（适用于 WebKit 浏览器） */
// #askQuestionButton::-webkit-scrollbar-track {
//   background: #f1f1f1;
//   /* 滚动条轨道背景颜色 */
// }

// /* 滚动条滑块样式（适用于 WebKit 浏览器） */
// #askQuestionButton::-webkit-scrollbar-thumb {
//   background: #888;
//   /* 滚动条滑块背景颜色 */
//   border-radius: 5px;
//   /* 滚动条滑块圆角 */
// }

// /* 滚动条滑块悬停样式（适用于 WebKit 浏览器） */
// #askQuestionButton::-webkit-scrollbar-thumb:hover {
//   background: #555;
//   /* 滚动条滑块悬停时的背景颜色 */
// }
:global {
  .ant-tabs-tab {
    padding: 2px 10px !important;
  }
  .ant-tabs-nav-more {
    display: none;
  }
}
.MessageCardDiv {
  padding: 7px 17px;
  background: #f9f9f9;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: nowrap;
  border-radius: 10px;
  > div {
    width: 100%;
    margin-bottom: 5px;
  }
  .MessageCardDiv_img {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 150px;
      height: 150px;
      border-radius: 4px;
    }
  }
  .MessageCardDiv_title {
    color: #333;
    font-size: 12px;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 显示的行数 */
    overflow: hidden;
  }
  .MessageCardDiv_content {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;
    :nth-child(1) {
      color: #ff1e1e;
    }
    :nth-child(2) {
      color: #ffa601;
    }
  }
  .MessageCardDiv_button {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;
    .MessageCardDiv_button_icon {
      display: flex;
      > span {
        display: flex;
        cursor: pointer;
        background: #999;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        align-items: center;
        justify-content: center;
        color: #fff;
        transition: all 0.3s ease;
      }
      > span:hover {
        background: #3463fc;
        transition: all 0.3s ease;
      }
    }
  }
}
.MessageCardDiv2 {
  padding: 5px;
  background: #f9f9f9;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  border-radius: 4px;
  .MessageCardDiv2_img {
    margin-right: 5px;
    img {
      width: 50px;
      height: 50px;
    }
    border-radius: 4px;
  }
  .MessageCardDiv2_title {
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
  }
  .MessageCardDiv2_title_text {
    width: 65%;
    color: #333;
    font-size: 12px;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 显示的行数 */
    overflow: hidden;
  }
  .MessageCardDiv2_title_price {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    align-items: flex-end;
    flex-wrap: nowrap;
    :nth-child(1) {
      color: #ff1e1e;
    }
    :nth-child(2) {
      color: #999;
      transform: scale(0.8);
    }
  }
  .MessageCardDiv2_status {
    color: #ffa601;
    font-size: 12px;
    margin-top: 10px;
  }
}
