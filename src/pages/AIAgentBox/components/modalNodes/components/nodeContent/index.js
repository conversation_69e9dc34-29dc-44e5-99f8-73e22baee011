import styles from './style.less';
import { useEffect, useCallback, useLayoutEffect } from 'react';
import {
  Input,
  Button,
  Form,
  Tag,
  Select,
  Radio,
  DatePicker,
  Checkbox,
  Row,
  Col,
  Tabs,
  Collapse,
  Slider,
} from 'antd';
import { useReactFlow } from 'react-flow-renderer';
import ReactMarkdown from 'react-markdown';
import { ReactComponent as FileFrame } from '@/assets/file_frame.svg';
import { ReactComponent as YesIcon } from '@/assets/yes.svg';
import { getIntl, FormattedMessage, useSelector } from 'umi';
import './style.less';
import {
  ArrowRightOutlined,
  RightOutlined,
  LeftOutlined,
} from '@ant-design/icons';
const { TextArea } = Input;
const { RangePicker } = DatePicker;
import { UploadOutlined } from '@ant-design/icons';
import MessageCardImg from '@/assets/messageCardTemp1.png';
const NodeContent = props => {
  const { titleHeight, content, setNodesDraggable, nodesDraggable } = props;
  const { id, data } = content;
  const { customizForms, handleList, componentType } = data || {};
  const { setNodes } = useReactFlow();
  const { currentVariables } = useSelector(({ aiagent }) => ({
    currentVariables: aiagent.currentVariables,
  }));
  //处理变量蓝底
  const HighlightText = ({ text, blueText }) => {
    // 使用正则表达式查找大括号中的内容
    const regex = /(\{[^}]+\})/g;
    // 替换匹配到的内容并包裹上样式
    const parts = text?.split(regex);
    return (
      <span>
        {parts?.map(
          (part, index) =>
            index % 2 === 0 ? (
              blueText ? (
                <span style={{ color: '#3463FC', fontWeight: 'bold' }}>
                  {part}
                </span>
              ) : (
                part
              )
            ) : (
              <span
                key={index}
                style={{
                  color: part.replace(
                    /(\{(var_agent|var_session|var_global|var_system)\.[^}]*\})/g,
                    (match, fullMatch, prefix) => {
                      if (prefix === 'var_agent') {
                        return '#fff';
                      } else if (prefix === 'var_session') {
                        return '#fff';
                      } else if (prefix === 'var_global') {
                        return '#fff';
                      } else if (prefix === 'var_system') {
                        return '#fff';
                      } else {
                        return '#3463fc';
                      }
                    },
                  ),
                  backgroundColor: part.replace(
                    /(\{(var_agent|var_session|var_global|var_system)\.[^}]*\})/g,
                    (match, fullMatch, prefix) => {
                      if (prefix === 'var_agent') {
                        return '#21b1e1';
                      } else if (prefix === 'var_session') {
                        return '#97dc16';
                      } else if (prefix === 'var_global') {
                        return '#31c759';
                      } else if (prefix === 'var_system') {
                        return '#eb903b';
                      }
                    },
                  ),
                  padding: '1px 2px',
                  margin: '0px 1px',
                  borderRadius: '4px',
                  marginBottom: 5,
                  lineHeight: '23px',
                }}
              >
                {part}
              </span>
            ),
          // index % 2 === 0 ? (
          //   part
          // ) : (
          //   <span
          //     key={index}
          //     style={{
          //       color: '#fff',
          //       backgroundColor: '#3463FC',
          //       padding: '1px 2px',
          //       margin: '0px 1px',
          //       borderRadius: '4px',
          //       marginBottom: 5,
          //       lineHeight: '23px',
          //     }}
          //   >
          //     {part}
          //   </span>
          // ),
        )}
      </span>
    );
  };
  const HotIssusIcon = () => {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="12"
        height="13"
        viewBox="0 0 12 13"
        fill="none"
      >
        <g clip-path="url(#clip0_2504_14923)">
          <path
            d="M7.50892 11.3524C7.59138 11.3081 7.66787 11.2699 7.73718 11.2185C8.35982 10.8349 8.7542 10.2911 8.94064 9.67803C9.26212 8.62396 8.96335 7.46711 8.39448 6.55884C7.86984 5.72706 7.6105 4.75187 7.60333 3.77906C7.15876 4.31924 7.12649 5.06259 6.99264 5.72587C6.85281 6.43455 6.38314 6.81579 5.85611 6.92693C5.62068 6.96876 5.37927 6.96159 5.151 6.90542C4.92274 6.84806 4.70046 6.73094 4.51641 6.57199C4.09096 6.20032 3.84955 5.58723 4.09096 4.79967C4.40288 3.80296 4.24393 2.90067 3.55795 2.12147C3.68463 3.65597 3.16357 4.97654 2.33776 6.25768C1.98521 6.80384 1.74739 7.41333 1.63266 8.02044C1.41037 9.19283 1.6458 10.523 2.71302 11.2089C2.77636 11.2567 2.85284 11.295 2.92216 11.3332C2.82655 10.7644 2.91857 10.1608 3.15759 9.63023C3.36434 9.17251 3.68463 8.77215 4.09096 8.4925C4.40288 8.28934 4.75782 8.14951 5.14503 8.12441C5.39361 8.10171 5.60872 8.28934 5.62785 8.53433C5.63741 8.69328 5.56451 8.83908 5.44978 8.92751C5.38644 8.98129 5.33863 9.03866 5.30398 9.09602C5.22749 9.23943 5.19283 9.39719 5.20239 9.55255C5.24661 10.0772 5.61589 10.2003 6.06047 10.2445C6.74645 10.3162 7.44558 10.5003 7.50892 11.3524ZM8.21402 11.975C7.89613 12.1782 7.53999 12.3407 7.14083 12.4638C7.07032 12.4889 6.98786 12.4925 6.91257 12.4769C6.67714 12.4255 6.51819 12.1913 6.56599 11.9499C6.70582 11.2735 6.47397 11.1934 5.96845 11.1337C5.09484 11.044 4.38973 10.5875 4.31325 9.62784C4.31325 9.58003 4.30727 9.53223 4.30727 9.47845C4.17342 9.63142 4.05989 9.8083 3.97025 10.0019C3.72168 10.5516 3.6906 11.2567 4.03359 11.7778C4.10291 11.8794 4.13876 12.0001 4.11606 12.1339C4.07184 12.3754 3.8364 12.5343 3.595 12.4937C3.06079 12.3957 2.61024 12.208 2.22542 11.963C0.841506 11.0691 0.463857 9.38643 0.749484 7.85791C0.88931 7.13966 1.16896 6.41902 1.58844 5.77367C2.57917 4.22483 2.90662 2.86482 2.47759 1.04708C2.4489 0.922795 2.47759 0.786554 2.55407 0.681386C2.69987 0.481806 2.97952 0.443563 3.17671 0.583389C4.7136 1.70199 5.52746 3.16957 4.94186 5.06259C4.82474 5.45697 4.92274 5.73901 5.10081 5.89437C5.17969 5.96369 5.27529 6.01269 5.38046 6.03778C5.47607 6.07244 5.58124 6.07244 5.67923 6.04735C5.87642 6.01269 6.06047 5.85733 6.11783 5.55258C6.39748 4.14835 6.50504 3.20422 7.90927 2.41307C7.99771 2.35571 8.11244 2.33659 8.22717 2.35332C8.46499 2.39515 8.62752 2.63536 8.58331 2.87796C8.37297 3.97984 8.55104 5.12952 9.15217 6.08917C9.86086 7.22331 10.1979 8.62755 9.79632 9.93617C9.54535 10.7512 9.02071 11.4671 8.21402 11.975Z"
            fill="#3463FC"
          />
        </g>
        <defs>
          <clipPath id="clip0_2504_14923">
            <rect
              width="12"
              height="12"
              fill="white"
              transform="translate(0 0.5)"
            />
          </clipPath>
        </defs>
      </svg>
    );
  };
  const ExampleImg = (size = 150) => {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 150 150"
        fill="none"
      >
        <path
          d="M128.812 31.9687L128.775 32.4562L128.531 33.3749L128.044 34.2187L127.725 34.5937L127.35 34.9124L126.525 35.3812L125.587 35.6437L125.1 35.6812L124.612 35.6437L123.675 35.3812L122.85 34.9124L122.475 34.5937L122.156 34.2187L121.669 33.3749L121.425 32.4562L121.387 31.9687L121.425 31.4812L121.669 30.5437L122.156 29.7187L122.475 29.3437L122.85 29.0249L123.675 28.5374L124.612 28.2937L125.1 28.2562L125.587 28.2937L126.525 28.5374L127.35 29.0249L127.725 29.3437L128.044 29.7187L128.531 30.5437L128.775 31.4812L128.812 31.9687Z"
          fill="#FF73A9"
        />
        <path
          d="M22.2937 116.681L22.2562 117.169L22.0124 118.087L21.5249 118.931L21.2062 119.306L20.8312 119.625L20.0062 120.094L19.0687 120.356L18.5812 120.394L18.0937 120.356L17.1562 120.094L16.3312 119.625L15.9562 119.306L15.3749 118.519L14.9999 117.637L14.9062 117.169L14.8687 116.681L14.9999 115.706L15.3749 114.825L15.9562 114.056L16.3312 113.737L17.1562 113.25L18.0937 113.006L18.5812 112.969L19.0687 113.006L20.0062 113.25L20.8312 113.737L21.2062 114.056L21.5249 114.431L22.0124 115.256L22.2562 116.194L22.2937 116.681Z"
          fill="#FF73A9"
        />
        <path
          d="M41.2875 128.306L40.8375 128.25L40.4062 128.137L40.0312 127.95L39.375 127.406L38.9437 126.675L38.8312 126.244L38.775 125.794L38.8312 125.344L38.9437 124.912L39.375 124.181L40.0312 123.619L40.4062 123.431L40.8375 123.319L41.2875 123.281L41.7562 123.319L42.1687 123.431L42.9 123.881L43.4625 124.519L43.65 124.912L43.7625 125.344L43.8 125.794L43.7625 126.244L43.65 126.675L43.2 127.406L42.5625 127.95L42.1687 128.137L41.7562 128.25L41.2875 128.306ZM41.2875 124.481L40.95 124.537L40.6312 124.669L40.3687 124.875L40.1625 125.137L40.0312 125.437L39.9937 125.794L40.0312 126.131L40.1625 126.45L40.3687 126.712L40.6312 126.919L40.95 127.05L41.2875 127.087L41.6437 127.05L41.9437 126.919L42.2062 126.712L42.4125 126.45L42.5437 126.131L42.6 125.794L42.5437 125.437L42.4125 125.137L42.2062 124.875L41.9437 124.669L41.6437 124.537L41.2875 124.481Z"
          fill="#CAF6E1"
        />
        <path
          d="M112.256 20.8312H108.506V17.0999L108.45 16.7624L108.337 16.4624L108.131 16.2187L107.887 16.0124L107.587 15.8999L107.269 15.8437L106.931 15.8999L106.631 16.0124L106.387 16.2187L106.181 16.4624L106.069 16.7624L106.012 17.0999V20.8312H102.281L101.944 20.8874L101.644 20.9999L101.4 21.2062L101.194 21.4499L101.081 21.7499L101.025 22.0874L101.081 22.4249L101.194 22.7062L101.4 22.9499L101.644 23.1562L101.944 23.2687L102.281 23.3249H106.012V27.0749L106.069 27.4124L106.181 27.6937L106.387 27.9374L106.631 28.1437L106.931 28.2562L107.269 28.3124L107.587 28.2562L107.887 28.1437L108.131 27.9374L108.337 27.6937L108.45 27.4124L108.506 27.0749V23.3249H112.256L112.575 23.2687L112.875 23.1562L113.119 22.9499L113.325 22.7062L113.437 22.4249L113.494 22.0874L113.437 21.7499L113.325 21.4499L113.119 21.2062L112.875 20.9999L112.575 20.8874L112.256 20.8312Z"
          fill="#CAF6E1"
        />
        <path
          d="M123.037 129.037H120.262L120.225 125.962L120.112 125.737L119.756 125.362L119.231 125.212L118.706 125.362L118.331 125.737L118.237 125.962L118.2 129.037H115.406L114.9 129.169L114.525 129.544L114.375 130.069L114.412 130.35L114.525 130.594L114.9 130.95L115.406 131.1H118.2L118.237 134.156L118.5 134.606L118.95 134.869L119.231 134.906L119.756 134.775L120.112 134.4L120.225 134.156L120.262 131.1H123.037L123.562 130.95L123.937 130.594L124.069 130.069L123.937 129.544L123.562 129.169L123.037 129.037Z"
          fill="#CAF6E1"
        />
        <path
          d="M22.7249 23.7374L23.1562 24.1124L24.0937 24.6562L25.1437 24.9187H26.1937L27.2437 24.6562L28.1812 24.1124L28.6312 23.7374L29.3062 22.8374L29.5499 22.3499L29.8124 21.2999V20.2499L29.7187 19.7249L29.3062 18.7312L29.0062 18.2624L27.3749 16.5749L26.9437 16.1999L25.9874 15.6562L24.9562 15.3937H23.8874L22.8562 15.6562L21.8999 16.1999L21.4687 16.5749L20.7937 17.4749L20.5499 17.9624L20.2874 18.9937V20.0624L20.3812 20.5874L20.7937 21.5812L21.0937 22.0499L22.7249 23.7374Z"
          fill="#F3F3F3"
        />
        <path
          d="M22.7249 23.7374L23.1562 24.1124L24.0937 24.6562L25.1437 24.9187H26.1937L27.2437 24.6562L28.1812 24.1124L28.6312 23.7374L29.3062 22.8374L29.5499 22.3499L29.8124 21.2999V20.2499L29.7187 19.7249L29.3062 18.7312L29.0062 18.2624L27.3749 16.5749L26.9437 16.1999L25.9874 15.6562L24.9562 15.3937H23.8874L22.8562 15.6562L21.8999 16.1999L21.4687 16.5749L20.7937 17.4749L20.5499 17.9624L20.2874 18.9937V20.0624L20.3812 20.5874L20.7937 21.5812L21.0937 22.0499L22.7249 23.7374Z"
          fill="url(#paint0_linear_4933_4020)"
        />
        <path
          d="M44.2312 22.2187L44.7937 22.6124L45.4124 22.7812L46.0687 22.7249L46.3687 22.6124L46.9312 22.2187L47.3062 21.6749L47.4187 21.3562L47.4749 20.7187L47.3062 20.0812L46.9312 19.5187L45.0562 17.6437L44.4937 17.2687L43.8562 17.0999L43.2187 17.1562L42.6187 17.4187L42.3562 17.6437L42.1312 17.9249L41.8499 18.5062L41.7937 19.1624L41.9624 19.7812L42.3562 20.3437L44.2312 22.2187Z"
          fill="#F3F3F3"
        />
        <path
          d="M44.2312 22.2187L44.7937 22.6124L45.4124 22.7812L46.0687 22.7249L46.3687 22.6124L46.9312 22.2187L47.3062 21.6749L47.4187 21.3562L47.4749 20.7187L47.3062 20.0812L46.9312 19.5187L45.0562 17.6437L44.4937 17.2687L43.8562 17.0999L43.2187 17.1562L42.6187 17.4187L42.3562 17.6437L42.1312 17.9249L41.8499 18.5062L41.7937 19.1624L41.9624 19.7812L42.3562 20.3437L44.2312 22.2187Z"
          fill="url(#paint1_linear_4933_4020)"
        />
        <path
          d="M15.9 59.8499L16.4625 60.2437L17.0812 60.4124L17.7375 60.3562L18.0375 60.2437L18.6 59.8499L18.9937 59.3062L19.1437 58.6687L19.0875 58.0312L18.9937 57.7124L18.6 57.1687L16.725 55.2937L16.1625 54.8999L15.525 54.7312L14.8875 54.7874L14.2875 55.0687L14.025 55.2937L13.8 55.5562L13.5375 56.1562L13.4812 56.7937L13.6312 57.4312L14.025 57.9749L15.9 59.8499Z"
          fill="#F3F3F3"
        />
        <path
          d="M15.9 59.8499L16.4625 60.2437L17.0812 60.4124L17.7375 60.3562L18.0375 60.2437L18.6 59.8499L18.9937 59.3062L19.1437 58.6687L19.0875 58.0312L18.9937 57.7124L18.6 57.1687L16.725 55.2937L16.1625 54.8999L15.525 54.7312L14.8875 54.7874L14.2875 55.0687L14.025 55.2937L13.8 55.5562L13.5375 56.1562L13.4812 56.7937L13.6312 57.4312L14.025 57.9749L15.9 59.8499Z"
          fill="url(#paint2_linear_4933_4020)"
        />
        <path
          d="M114.9 117.506L114.337 117.112L113.7 116.962L113.062 117.019L112.762 117.112L112.2 117.506L111.806 118.069L111.656 118.687L111.712 119.344L111.806 119.644L112.2 120.206L114.075 122.081L114.637 122.475L115.256 122.625L115.912 122.569L116.512 122.306L116.775 122.081L117 121.819L117.262 121.219L117.319 120.581L117.169 119.944L116.775 119.381L114.9 117.506Z"
          fill="#F3F3F3"
        />
        <path
          d="M114.9 117.506L114.337 117.112L113.7 116.962L113.062 117.019L112.762 117.112L112.2 117.506L111.806 118.069L111.656 118.687L111.712 119.344L111.806 119.644L112.2 120.206L114.075 122.081L114.637 122.475L115.256 122.625L115.912 122.569L116.512 122.306L116.775 122.081L117 121.819L117.262 121.219L117.319 120.581L117.169 119.944L116.775 119.381L114.9 117.506Z"
          fill="url(#paint3_linear_4933_4020)"
        />
        <path
          d="M137.475 119.1L137.419 119.906L137.25 120.694L137.006 121.444L136.65 122.175L136.2 122.831L135.675 123.45L135.056 123.975L134.4 124.425L133.669 124.781L132.919 125.025L132.131 125.194L131.325 125.25L130.519 125.194L129.731 125.025L128.962 124.781L128.25 124.425L127.594 123.975L126.975 123.45L126.45 122.831L126 122.175L125.644 121.444L125.381 120.694L125.231 119.906L125.175 119.1L125.231 118.294L125.381 117.506L125.644 116.756L126 116.025L126.45 115.369L126.975 114.75L127.594 114.225L128.25 113.775L128.962 113.419L129.731 113.175L130.519 113.006L131.325 112.95L132.131 113.006L132.919 113.175L133.669 113.419L134.4 113.775L135.056 114.225L135.675 114.75L136.2 115.369L136.65 116.025L137.006 116.756L137.25 117.506L137.419 118.294L137.475 119.1Z"
          fill="#FF73A9"
        />
        <path
          d="M131.437 90.5999L125.531 84.6937L121.8 80.7187L121.294 79.9499L121.069 79.1062L121.125 78.2437L121.275 77.8312L121.762 77.0624L122.137 76.7249L122.55 76.4624L123 76.2937L123.469 76.2187L123.937 76.1999L124.406 76.2937L124.837 76.4437L125.269 76.6874L125.644 77.0249L128.156 79.5937L128.512 79.8937L128.906 80.1374L129.787 80.3812L130.669 80.3249L131.1 80.1937L131.494 79.9874L131.869 79.6874L132.187 79.3124L132.412 78.9187L132.562 78.5062L132.656 78.0562V77.6062L132.581 77.1749L132.431 76.7437L132.206 76.3499L131.906 75.9749L128.4 72.4687L127.8 71.7937L127.275 71.0624L126.825 70.2749L126.469 69.4499L126.206 68.6062L125.7 65.5312L125.269 63.3937L124.762 61.2937L124.162 59.2312L123.487 57.2249L122.719 55.2187L121.894 53.2687L120.975 51.3562L120 49.4999L118.931 47.6812L117.806 45.9187L116.606 44.1937L115.35 42.5249L114.019 40.9124L112.631 39.3562L111.169 37.8374L109.65 36.3937L108.094 35.0249L106.462 33.6937L104.794 32.4374L103.069 31.2562L101.287 30.1312L99.4687 29.0999L97.5937 28.1249L95.6624 27.2062L93.7124 26.3999L91.7249 25.6499L89.6999 24.9937L87.6187 24.3937L85.5187 23.9062L83.3812 23.4937L81.2249 23.1749L79.0499 22.9312L76.8187 22.7999L74.5499 22.7437L71.8687 22.8187L70.9312 22.7249L70.0499 22.4062L69.6562 22.1437L65.7187 18.2437L64.8374 17.5687L63.8437 17.1374L63.1499 17.0062L62.4562 16.9687L61.7437 17.0624L60.7499 17.4187L60.2812 17.6999L59.8499 18.0749L59.4374 18.5062L59.0999 18.9562L58.8562 19.4624L58.6687 19.9874L58.4999 21.0562L58.6312 22.1437L58.7999 22.6687L59.0437 23.1749L59.3437 23.6437L62.3062 26.7562L62.6624 27.2249L62.9249 27.7312L63.0749 28.2562L63.1312 28.7999L63.0749 29.3249L62.9249 29.8687L62.6812 30.3562L62.3624 30.8062L61.9124 31.1999L61.4812 31.4624L61.0687 31.6312L60.6187 31.7249H60.1687L59.7374 31.6687L58.8937 31.3124L58.1249 30.6937L50.8687 23.4749L50.4749 23.1749L50.0249 22.9687L49.5562 22.8937L49.0874 22.9124L48.7874 23.0062L48.2249 23.3437L47.7749 23.9062L47.5874 24.5624L47.6249 25.2187L47.7374 25.5374L48.1312 26.1187L52.5749 30.5624L52.8937 31.0124L53.2874 31.9687L53.4187 32.6437L53.4374 33.3187L53.3062 33.9937L52.9312 34.9499L52.2374 35.8312L51.8062 36.2062L51.3562 36.4874L50.3812 36.8812L49.6874 36.9937H48.9937L48.2999 36.8624L47.3249 36.4687L46.4437 35.7749L43.6874 32.9062L37.0687 26.2874L36.6374 25.9124L36.1687 25.5937L35.1749 25.1999L34.1062 25.0687L33.0562 25.1999L32.0624 25.5937L31.5937 25.9124L31.1624 26.2874L30.4687 27.1874L30.2437 27.6749L29.9624 28.7062V29.7749L30.0749 30.2999L30.4687 31.2937L31.1624 32.1937L35.9624 37.0124L37.6499 38.5874L38.3062 39.4499L38.7187 40.4249L38.8499 41.0999V41.7937L38.7374 42.4874L38.5874 42.9749L38.0812 43.9124L37.7249 44.3437L37.2749 44.7562L36.8062 45.0749L36.3187 45.3187L35.7749 45.5062L35.2312 45.5999L34.1249 45.5812L33.0749 45.2812L32.5687 45.0187L32.0999 44.6812L27.8624 40.3687L27.2437 39.8062L26.5687 39.3562L25.8562 38.9999L25.1062 38.7374L24.3374 38.5687L23.5687 38.5124L22.7812 38.5312L22.0124 38.6624L21.2624 38.8687L20.5312 39.2062L19.8562 39.6187L19.2187 40.1437L18.6187 40.7812L18.1499 41.4749L17.7749 42.1874L17.5124 42.9562L17.3437 43.7437L17.2874 44.5312L17.3249 45.3374L17.4749 46.1249L17.7187 46.8937L18.0749 47.6249L18.5249 48.2999L19.0874 48.9562L29.4187 59.2499L29.7937 59.6999L30.0749 60.1874L30.2437 60.7124L30.3374 61.2562L30.3187 61.7812L30.2249 62.3249L30.0187 62.8312L29.7374 63.2999L29.3437 63.7499L28.8937 64.0874L28.4249 64.3312L27.9374 64.4812L27.4124 64.5562L26.9062 64.5374L26.3812 64.4437L25.8937 64.2562L25.4437 63.9937L25.0312 63.6187L22.4999 61.0687L22.2374 60.8437L21.6374 60.5624L21.3187 60.5062L20.6812 60.5624L20.0812 60.8437L19.7999 61.0687L19.5187 61.3499L19.3312 61.6499L19.2374 61.9499L19.2187 62.6062L19.2937 62.9249L19.4999 63.3749L19.8187 63.7687L23.1562 67.1062L30.0937 73.8749L30.4499 74.2874L30.7124 74.7562L30.8812 75.2437L30.9749 75.7499V76.2562L30.8999 76.7624L30.7124 77.2499L30.4687 77.7187L30.1124 78.1312L29.6812 78.4874L29.2499 78.7312L28.7624 78.8999L28.2562 78.9937H27.7687L27.2624 78.8999L26.7749 78.7499L26.3437 78.4874L25.9124 78.1499L17.7374 70.0499L17.3624 69.7499L16.9687 69.5062L16.5562 69.3562L16.1062 69.2624L15.2249 69.3187L14.7937 69.4499L14.3999 69.6562L14.0249 69.9562L13.7062 70.3312L13.4624 70.7249L13.3124 71.1374L13.2374 71.5874V72.0374L13.2937 72.4687L13.4437 72.8999L13.6687 73.2937L13.9874 73.6687L22.2187 81.8999L22.6687 82.4437L23.0437 83.0249L23.3062 83.6624L23.9999 86.6812L24.5812 88.9687L25.2749 91.2187L26.0624 93.4124L26.9624 95.5499L27.9374 97.6499L29.0062 99.7124L30.1499 101.7L31.3874 103.631L32.7187 105.506L34.1249 107.325L35.6062 109.069L37.1437 110.756L38.7749 112.369L40.4624 113.906L42.2249 115.369L44.0437 116.756L45.9374 118.069L47.8874 119.287L49.8937 120.431L51.9374 121.481L54.0562 122.437L56.1937 123.319L58.4062 124.087L60.6749 124.762L62.9437 125.344L65.2687 125.812L67.6312 126.187L70.0312 126.45L73.1624 126.675L73.7999 126.825L74.4374 127.031L75.0374 127.331L75.5812 127.687L76.1062 128.137L79.9124 131.756L80.7937 132.431L81.7687 132.862L82.4812 132.994L83.1749 133.031L83.8687 132.937L84.8624 132.581L85.3312 132.3L85.7812 131.944L86.1749 131.494L86.5124 131.044L86.7562 130.537L86.9437 130.012L87.1124 128.944L86.9812 127.856L86.5874 126.825L86.2687 126.356L85.8937 125.925L83.6249 123.581L83.3062 123.169L83.0999 122.737L82.9499 122.287L82.9124 121.8L82.9312 121.331L83.0437 120.862L83.2312 120.431L83.4937 120.019L83.8499 119.662L84.2437 119.381L84.6749 119.175L85.1062 119.044L85.5562 119.006L86.4562 119.119L86.8687 119.306L87.2624 119.55L91.4249 123.844L95.5499 127.969L95.9999 128.362L96.4687 128.662L97.4999 129.075L98.0249 129.169L99.1124 129.15L100.181 128.85L100.669 128.587L101.137 128.269L101.569 127.875L101.925 127.425L102.206 126.956L102.562 125.962L102.656 125.25L102.637 124.556L102.487 123.862L102.319 123.356L101.756 122.419L98.3249 118.875L97.9312 118.425L97.6124 117.937L97.3687 117.412L97.1062 116.325L97.1437 115.2L97.2562 114.656L97.7062 113.606L98.0437 113.137L98.4562 112.687L98.9999 112.237L99.4687 111.937L100.481 111.544L101.194 111.431H101.906L102.619 111.562L103.631 111.956L104.1 112.256L104.55 112.631L108.244 116.25L108.637 116.569L109.087 116.756L109.556 116.85L110.044 116.812L110.625 116.587L111.15 116.119L111.337 115.837L111.544 115.181L111.506 114.506L111.225 113.887L110.025 112.65L109.537 112.256L108.431 111.206L105.375 108.094L103.912 106.575L103.444 105.9L103.275 105.112L103.35 104.344L103.5 103.969L103.987 103.294L104.681 102.844L105.056 102.731L105.844 102.675L106.594 102.9L106.931 103.106L107.25 103.387L113.081 109.537L115.725 112.2L116.362 112.744L117.019 113.194L117.731 113.55L118.481 113.812L119.25 113.981L120.037 114.056L120.806 114.019L121.575 113.906L122.344 113.681L123.056 113.362L123.731 112.931L124.387 112.406L124.969 111.769L125.437 111.094L125.812 110.362L126.075 109.594L126.244 108.825L126.3 108.019L126.262 107.231L126.112 106.444L125.869 105.675L125.531 104.944L125.062 104.25L124.5 103.594L120.281 99.3749L118.144 97.0312L117.825 96.6374L117.6 96.2062L117.45 95.7562L117.394 95.2687V94.7999L117.487 94.3312L117.656 93.8812L117.919 93.4687L118.237 93.0749L118.65 92.7562L119.1 92.5124L119.569 92.3624L120.056 92.2874L120.544 92.3062L121.031 92.3999L121.481 92.5874L121.912 92.8499L125.606 96.5812L126.037 96.9749L126.506 97.2749L127.012 97.5187L128.081 97.7812L129.169 97.7624L129.694 97.6499L130.725 97.1999L131.175 96.8812L131.625 96.4874L132.262 95.5687L132.619 94.5749L132.712 93.8812L132.694 93.1687L132.544 92.4749L132.131 91.4812L131.437 90.5999Z"
          fill="#D7B4FF"
        />
        <path
          d="M131.437 90.5999L125.531 84.6937L121.8 80.7187L121.294 79.9499L121.069 79.1062L121.125 78.2437L121.275 77.8312L121.762 77.0624L122.137 76.7249L122.55 76.4624L123 76.2937L123.469 76.2187L123.937 76.1999L124.406 76.2937L124.837 76.4437L125.269 76.6874L125.644 77.0249L128.156 79.5937L128.512 79.8937L128.906 80.1374L129.787 80.3812L130.669 80.3249L131.1 80.1937L131.494 79.9874L131.869 79.6874L132.187 79.3124L132.412 78.9187L132.562 78.5062L132.656 78.0562V77.6062L132.581 77.1749L132.431 76.7437L132.206 76.3499L131.906 75.9749L128.4 72.4687L127.8 71.7937L127.275 71.0624L126.825 70.2749L126.469 69.4499L126.206 68.6062L125.7 65.5312L125.269 63.3937L124.762 61.2937L124.162 59.2312L123.487 57.2249L122.719 55.2187L121.894 53.2687L120.975 51.3562L120 49.4999L118.931 47.6812L117.806 45.9187L116.606 44.1937L115.35 42.5249L114.019 40.9124L112.631 39.3562L111.169 37.8374L109.65 36.3937L108.094 35.0249L106.462 33.6937L104.794 32.4374L103.069 31.2562L101.287 30.1312L99.4687 29.0999L97.5937 28.1249L95.6624 27.2062L93.7124 26.3999L91.7249 25.6499L89.6999 24.9937L87.6187 24.3937L85.5187 23.9062L83.3812 23.4937L81.2249 23.1749L79.0499 22.9312L76.8187 22.7999L74.5499 22.7437L71.8687 22.8187L70.9312 22.7249L70.0499 22.4062L69.6562 22.1437L65.7187 18.2437L64.8374 17.5687L63.8437 17.1374L63.1499 17.0062L62.4562 16.9687L61.7437 17.0624L60.7499 17.4187L60.2812 17.6999L59.8499 18.0749L59.4374 18.5062L59.0999 18.9562L58.8562 19.4624L58.6687 19.9874L58.4999 21.0562L58.6312 22.1437L58.7999 22.6687L59.0437 23.1749L59.3437 23.6437L62.3062 26.7562L62.6624 27.2249L62.9249 27.7312L63.0749 28.2562L63.1312 28.7999L63.0749 29.3249L62.9249 29.8687L62.6812 30.3562L62.3624 30.8062L61.9124 31.1999L61.4812 31.4624L61.0687 31.6312L60.6187 31.7249H60.1687L59.7374 31.6687L58.8937 31.3124L58.1249 30.6937L50.8687 23.4749L50.4749 23.1749L50.0249 22.9687L49.5562 22.8937L49.0874 22.9124L48.7874 23.0062L48.2249 23.3437L47.7749 23.9062L47.5874 24.5624L47.6249 25.2187L47.7374 25.5374L48.1312 26.1187L52.5749 30.5624L52.8937 31.0124L53.2874 31.9687L53.4187 32.6437L53.4374 33.3187L53.3062 33.9937L52.9312 34.9499L52.2374 35.8312L51.8062 36.2062L51.3562 36.4874L50.3812 36.8812L49.6874 36.9937H48.9937L48.2999 36.8624L47.3249 36.4687L46.4437 35.7749L43.6874 32.9062L37.0687 26.2874L36.6374 25.9124L36.1687 25.5937L35.1749 25.1999L34.1062 25.0687L33.0562 25.1999L32.0624 25.5937L31.5937 25.9124L31.1624 26.2874L30.4687 27.1874L30.2437 27.6749L29.9624 28.7062V29.7749L30.0749 30.2999L30.4687 31.2937L31.1624 32.1937L35.9624 37.0124L37.6499 38.5874L38.3062 39.4499L38.7187 40.4249L38.8499 41.0999V41.7937L38.7374 42.4874L38.5874 42.9749L38.0812 43.9124L37.7249 44.3437L37.2749 44.7562L36.8062 45.0749L36.3187 45.3187L35.7749 45.5062L35.2312 45.5999L34.1249 45.5812L33.0749 45.2812L32.5687 45.0187L32.0999 44.6812L27.8624 40.3687L27.2437 39.8062L26.5687 39.3562L25.8562 38.9999L25.1062 38.7374L24.3374 38.5687L23.5687 38.5124L22.7812 38.5312L22.0124 38.6624L21.2624 38.8687L20.5312 39.2062L19.8562 39.6187L19.2187 40.1437L18.6187 40.7812L18.1499 41.4749L17.7749 42.1874L17.5124 42.9562L17.3437 43.7437L17.2874 44.5312L17.3249 45.3374L17.4749 46.1249L17.7187 46.8937L18.0749 47.6249L18.5249 48.2999L19.0874 48.9562L29.4187 59.2499L29.7937 59.6999L30.0749 60.1874L30.2437 60.7124L30.3374 61.2562L30.3187 61.7812L30.2249 62.3249L30.0187 62.8312L29.7374 63.2999L29.3437 63.7499L28.8937 64.0874L28.4249 64.3312L27.9374 64.4812L27.4124 64.5562L26.9062 64.5374L26.3812 64.4437L25.8937 64.2562L25.4437 63.9937L25.0312 63.6187L22.4999 61.0687L22.2374 60.8437L21.6374 60.5624L21.3187 60.5062L20.6812 60.5624L20.0812 60.8437L19.7999 61.0687L19.5187 61.3499L19.3312 61.6499L19.2374 61.9499L19.2187 62.6062L19.2937 62.9249L19.4999 63.3749L19.8187 63.7687L23.1562 67.1062L30.0937 73.8749L30.4499 74.2874L30.7124 74.7562L30.8812 75.2437L30.9749 75.7499V76.2562L30.8999 76.7624L30.7124 77.2499L30.4687 77.7187L30.1124 78.1312L29.6812 78.4874L29.2499 78.7312L28.7624 78.8999L28.2562 78.9937H27.7687L27.2624 78.8999L26.7749 78.7499L26.3437 78.4874L25.9124 78.1499L17.7374 70.0499L17.3624 69.7499L16.9687 69.5062L16.5562 69.3562L16.1062 69.2624L15.2249 69.3187L14.7937 69.4499L14.3999 69.6562L14.0249 69.9562L13.7062 70.3312L13.4624 70.7249L13.3124 71.1374L13.2374 71.5874V72.0374L13.2937 72.4687L13.4437 72.8999L13.6687 73.2937L13.9874 73.6687L22.2187 81.8999L22.6687 82.4437L23.0437 83.0249L23.3062 83.6624L23.9999 86.6812L24.5812 88.9687L25.2749 91.2187L26.0624 93.4124L26.9624 95.5499L27.9374 97.6499L29.0062 99.7124L30.1499 101.7L31.3874 103.631L32.7187 105.506L34.1249 107.325L35.6062 109.069L37.1437 110.756L38.7749 112.369L40.4624 113.906L42.2249 115.369L44.0437 116.756L45.9374 118.069L47.8874 119.287L49.8937 120.431L51.9374 121.481L54.0562 122.437L56.1937 123.319L58.4062 124.087L60.6749 124.762L62.9437 125.344L65.2687 125.812L67.6312 126.187L70.0312 126.45L73.1624 126.675L73.7999 126.825L74.4374 127.031L75.0374 127.331L75.5812 127.687L76.1062 128.137L79.9124 131.756L80.7937 132.431L81.7687 132.862L82.4812 132.994L83.1749 133.031L83.8687 132.937L84.8624 132.581L85.3312 132.3L85.7812 131.944L86.1749 131.494L86.5124 131.044L86.7562 130.537L86.9437 130.012L87.1124 128.944L86.9812 127.856L86.5874 126.825L86.2687 126.356L85.8937 125.925L83.6249 123.581L83.3062 123.169L83.0999 122.737L82.9499 122.287L82.9124 121.8L82.9312 121.331L83.0437 120.862L83.2312 120.431L83.4937 120.019L83.8499 119.662L84.2437 119.381L84.6749 119.175L85.1062 119.044L85.5562 119.006L86.4562 119.119L86.8687 119.306L87.2624 119.55L91.4249 123.844L95.5499 127.969L95.9999 128.362L96.4687 128.662L97.4999 129.075L98.0249 129.169L99.1124 129.15L100.181 128.85L100.669 128.587L101.137 128.269L101.569 127.875L101.925 127.425L102.206 126.956L102.562 125.962L102.656 125.25L102.637 124.556L102.487 123.862L102.319 123.356L101.756 122.419L98.3249 118.875L97.9312 118.425L97.6124 117.937L97.3687 117.412L97.1062 116.325L97.1437 115.2L97.2562 114.656L97.7062 113.606L98.0437 113.137L98.4562 112.687L98.9999 112.237L99.4687 111.937L100.481 111.544L101.194 111.431H101.906L102.619 111.562L103.631 111.956L104.1 112.256L104.55 112.631L108.244 116.25L108.637 116.569L109.087 116.756L109.556 116.85L110.044 116.812L110.625 116.587L111.15 116.119L111.337 115.837L111.544 115.181L111.506 114.506L111.225 113.887L110.025 112.65L109.537 112.256L108.431 111.206L105.375 108.094L103.912 106.575L103.444 105.9L103.275 105.112L103.35 104.344L103.5 103.969L103.987 103.294L104.681 102.844L105.056 102.731L105.844 102.675L106.594 102.9L106.931 103.106L107.25 103.387L113.081 109.537L115.725 112.2L116.362 112.744L117.019 113.194L117.731 113.55L118.481 113.812L119.25 113.981L120.037 114.056L120.806 114.019L121.575 113.906L122.344 113.681L123.056 113.362L123.731 112.931L124.387 112.406L124.969 111.769L125.437 111.094L125.812 110.362L126.075 109.594L126.244 108.825L126.3 108.019L126.262 107.231L126.112 106.444L125.869 105.675L125.531 104.944L125.062 104.25L124.5 103.594L120.281 99.3749L118.144 97.0312L117.825 96.6374L117.6 96.2062L117.45 95.7562L117.394 95.2687V94.7999L117.487 94.3312L117.656 93.8812L117.919 93.4687L118.237 93.0749L118.65 92.7562L119.1 92.5124L119.569 92.3624L120.056 92.2874L120.544 92.3062L121.031 92.3999L121.481 92.5874L121.912 92.8499L125.606 96.5812L126.037 96.9749L126.506 97.2749L127.012 97.5187L128.081 97.7812L129.169 97.7624L129.694 97.6499L130.725 97.1999L131.175 96.8812L131.625 96.4874L132.262 95.5687L132.619 94.5749L132.712 93.8812L132.694 93.1687L132.544 92.4749L132.131 91.4812L131.437 90.5999Z"
          fill="url(#paint4_linear_4933_4020)"
        />
        <path
          d="M80.3437 37.9312H77.5687L77.5312 34.8562L77.4374 34.6125L77.0624 34.2562L76.5374 34.1062L76.0124 34.2562L75.6374 34.6125L75.5437 34.8562L75.5062 37.9312H72.7124L72.2062 38.0625L71.8312 38.4375L71.6812 38.9625L71.7187 39.2437L71.8312 39.4687L72.2062 39.8437L72.7124 39.9937H75.5062L75.5437 43.05L75.8062 43.5L76.2562 43.7625L76.5374 43.8L77.0624 43.6687L77.4374 43.2937L77.5312 43.05L77.5687 39.9937H80.3437L80.8687 39.8437L81.2437 39.4687L81.3937 38.9625L81.2437 38.4375L80.8687 38.0625L80.3437 37.9312Z"
          fill="white"
        />
        <path
          d="M109.556 78.8999L109.106 78.8624L108.675 78.7499L107.944 78.2999L107.381 77.6624L107.194 77.2687L107.081 76.8562L107.044 76.3874L107.081 75.9374L107.194 75.5062L107.644 74.7749L108.281 74.2312L108.675 74.0437L109.106 73.9312L109.556 73.8749L110.006 73.9312L110.437 74.0437L111.169 74.4749L111.712 75.1312L111.9 75.5062L112.012 75.9374L112.069 76.3874L112.012 76.8562L111.9 77.2687L111.469 77.9999L110.812 78.5624L110.437 78.7499L110.006 78.8624L109.556 78.8999ZM109.556 75.0937L109.2 75.1312L108.9 75.2624L108.637 75.4687L108.431 75.7312L108.3 76.0499L108.244 76.3874L108.3 76.7437L108.431 77.0437L108.637 77.3062L108.9 77.5124L109.2 77.6437L109.556 77.6999L109.894 77.6437L110.212 77.5124L110.475 77.3062L110.681 77.0437L110.812 76.7437L110.85 76.3874L110.812 76.0499L110.681 75.7312L110.475 75.4687L110.212 75.2624L109.912 75.1312L109.556 75.0937Z"
          fill="white"
        />
        <path
          d="M41.55 98.025L41.1 97.9875L40.6875 97.875L40.2937 97.6875L39.6375 97.1437L39.2062 96.3937L39.0937 95.9812L39.0562 95.5312L39.0937 95.0625L39.2062 94.65L39.6375 93.9L40.2937 93.3562L40.6875 93.1687L41.1 93.0562L41.55 93.0187L42.0187 93.0562L42.4312 93.1687L42.825 93.3562L43.4812 93.9L43.9125 94.65L44.025 95.0625L44.0625 95.5312L44.025 95.9812L43.9125 96.3937L43.4812 97.1437L42.825 97.6875L42.4312 97.875L42.0187 97.9875L41.55 98.025ZM41.55 94.2187L41.2125 94.275L40.8937 94.4062L40.6312 94.5937L40.425 94.875L40.2937 95.175L40.2562 95.5312L40.2937 95.8687L40.425 96.1875L40.6312 96.45L40.8937 96.6562L41.2125 96.7875L41.55 96.825L41.9062 96.7875L42.2062 96.6562L42.4875 96.45L42.675 96.1875L42.8062 95.8687L42.8625 95.5312L42.8062 95.175L42.675 94.875L42.4875 94.5937L42.225 94.4062L41.9062 94.275L41.55 94.2187Z"
          fill="white"
        />
        <path
          d="M107.344 97.3501H52.7624L51.9562 97.2938L51.2062 97.1438L50.4937 96.8813L49.8187 96.5438L49.1999 96.1313L48.6374 95.6438L48.1499 95.0813L47.7187 94.4626L47.3812 93.7876L47.1374 93.0563L46.9874 92.3063L46.9312 91.5001V49.1063L46.9874 48.3001L47.1374 47.5501L47.3812 46.8376L47.7187 46.1626L48.1499 45.5438L48.6374 44.9813L49.1999 44.4751L49.8187 44.0626L50.4937 43.7251L51.2062 43.4813L51.9562 43.3313L52.7624 43.2751H107.344L108.131 43.3313L108.9 43.4813L109.612 43.7251L110.287 44.0626L110.906 44.4751L111.469 44.9813L111.956 45.5438L112.369 46.1626L112.725 46.8376L112.969 47.5501L113.119 48.3001L113.175 49.1063V91.5001L113.119 92.3063L112.969 93.0563L112.725 93.7876L112.369 94.4626L111.956 95.0813L111.469 95.6438L110.906 96.1313L110.287 96.5438L109.612 96.8813L108.9 97.1438L108.131 97.2938L107.344 97.3501Z"
          fill="white"
        />
        <path
          d="M107.344 97.9126H52.7624L51.8812 97.8563L51.0562 97.6876L50.2687 97.4063L49.5374 97.0313L48.8624 96.5813L48.2437 96.0376L47.6999 95.4188L47.2499 94.7438L46.8749 93.9938L46.5937 93.2063L46.4249 92.3813L46.3687 91.5001V49.1063L46.4249 48.2251L46.5937 47.4001L46.8749 46.6126L47.2499 45.8813L47.6999 45.2063L48.2437 44.5876L48.8624 44.0438L49.5374 43.5751L50.2687 43.2001L51.0562 42.9376L51.8812 42.7688L52.7624 42.7126H107.344L108.206 42.7688L109.05 42.9376L109.819 43.2001L110.569 43.5751L111.244 44.0438L111.862 44.5876L112.406 45.2063L112.856 45.8813L113.231 46.6126L113.512 47.4001L113.681 48.2251L113.737 49.1063V91.5001L113.681 92.3813L113.512 93.2063L113.231 93.9938L112.856 94.7438L112.406 95.4188L111.862 96.0376L111.244 96.5813L110.569 97.0313L109.819 97.4063L109.05 97.6876L108.206 97.8563L107.344 97.9126ZM52.7624 43.8376L52.0312 43.8751L51.3562 44.0251L50.7187 44.2501L50.0999 44.5501L49.5374 44.9251L49.0312 45.3751L48.5812 45.8813L48.2062 46.4438L47.9062 47.0626L47.6812 47.7001L47.5312 48.3751L47.4937 49.1063V91.5001L47.5312 92.2313L47.6812 92.9063L47.9062 93.5626L48.2062 94.1626L48.5812 94.7251L49.0312 95.2313L49.5374 95.6813L50.0999 96.0563L50.7187 96.3751L51.3562 96.6001L52.0312 96.7313L52.7624 96.7876H107.344L108.056 96.7313L108.75 96.6001L109.387 96.3751L110.006 96.0563L110.55 95.6813L111.075 95.2313L111.506 94.7251L111.881 94.1813L112.2 93.5626L112.425 92.9063L112.556 92.2313L112.612 91.5001V49.1063L112.556 48.3751L112.425 47.7001L112.2 47.0626L111.881 46.4438L111.506 45.8813L111.075 45.3751L110.55 44.9251L110.006 44.5501L109.387 44.2501L108.75 44.0251L108.056 43.8751L107.344 43.8376H52.7624Z"
          fill="#999999"
        />
        <path
          d="M98.5125 106.313H43.9499L43.1437 106.256L42.3937 106.106L41.6812 105.844L40.9874 105.506L40.3874 105.094L39.8249 104.606L39.3187 104.044L38.9062 103.425L38.5687 102.75L38.3249 102.038L38.1562 101.269L38.0999 100.481V58.0688L38.1562 57.2626L38.3249 56.5126L38.5687 55.8001L38.9062 55.1251L39.3187 54.5063L39.8249 53.9438L40.3874 53.4563L40.9874 53.0251L41.6812 52.6876L42.3937 52.4438L43.1437 52.2938L43.9499 52.2376H98.5125L99.3187 52.2938L100.069 52.4438L100.8 52.6876L101.475 53.0251L102.094 53.4563L102.656 53.9438L103.144 54.5063L103.556 55.1251L103.894 55.8001L104.156 56.5126L104.306 57.2626L104.362 58.0688V100.481L104.306 101.269L104.156 102.038L103.894 102.75L103.556 103.425L103.144 104.044L102.656 104.606L102.094 105.094L101.475 105.506L100.8 105.844L100.069 106.106L99.3187 106.256L98.5125 106.313Z"
          fill="white"
        />
        <path
          d="M98.5125 106.875H43.9499L43.0687 106.819L42.2437 106.65L41.4562 106.369L40.7062 105.994L40.0312 105.544L39.4124 105L38.8874 104.381L38.4187 103.706L38.0437 102.956L37.7812 102.169L37.6124 101.344L37.5374 100.481V58.0688L37.6124 57.1876L37.7812 56.3626L38.0437 55.5751L38.4187 54.8438L38.8874 54.1688L39.4312 53.5501L40.0312 53.0063L40.7062 52.5563L41.4562 52.1813L42.2437 51.9001L43.0687 51.7313L43.9499 51.6751H98.5125L99.3937 51.7313L100.219 51.9001L101.006 52.1813L101.756 52.5563L102.431 53.0063L103.05 53.5501L103.594 54.1688L104.044 54.8438L104.419 55.5751L104.681 56.3626L104.85 57.1876L104.925 58.0688V100.481L104.85 101.344L104.681 102.169L104.419 102.956L104.044 103.706L103.594 104.381L103.05 105L102.431 105.544L101.756 105.994L101.006 106.369L100.219 106.65L99.3937 106.819L98.5125 106.875ZM43.9499 52.8001L43.2187 52.8376L42.5437 52.9876L41.8874 53.2126L41.2874 53.5126L40.7249 53.8876L40.2187 54.3376L39.7687 54.8438L39.3937 55.4063L39.0937 56.0251L38.8499 56.6626L38.7187 57.3376L38.6624 58.0688V100.481L38.7187 101.194L38.8499 101.869L39.0937 102.525L39.3937 103.144L39.7687 103.688L40.2187 104.194L40.7249 104.644L41.2874 105.019L41.8874 105.338L42.5437 105.563L43.2187 105.694L43.9499 105.75H98.5125L99.2437 105.694L99.9187 105.563L100.575 105.338L101.175 105.019L101.737 104.644L102.244 104.194L102.694 103.688L103.069 103.144L103.387 102.525L103.612 101.869L103.744 101.194L103.8 100.481V58.0688L103.744 57.3376L103.612 56.6626L103.387 56.0251L103.069 55.4063L102.694 54.8438L102.244 54.3376L101.737 53.8876L101.175 53.5126L100.575 53.2126L99.9187 52.9876L99.2437 52.8376L98.5125 52.8001H43.9499Z"
          fill="#999999"
        />
        <path
          d="M99.2249 106.875H43.2374L42.5249 106.819L41.8312 106.706L41.1937 106.481L40.5562 106.2L39.9749 105.844L39.4499 105.431L38.9812 104.944L38.5499 104.419L38.1937 103.838L37.9124 103.2L37.7062 102.544L37.5749 101.831L37.5937 101.625L37.6874 101.438L57.5812 73.6313L57.9374 73.2376L58.3499 72.9376L58.8374 72.7313L59.3624 72.6376L59.8874 72.6563L60.3937 72.7688L60.8624 73.0126L61.2749 73.3313L76.3312 88.4813L76.8562 88.8001L77.4374 88.8563L77.9999 88.6126L86.4937 82.5188L86.8687 82.3688L87.6374 82.2563L88.3874 82.4063L89.0812 82.8001L104.887 100.856L104.925 101.175L104.869 101.963L104.719 102.694L104.475 103.388L104.137 104.044L103.725 104.663L103.256 105.206L102.694 105.694L102.094 106.088L101.437 106.425L100.744 106.669L99.9937 106.819L99.2249 106.875ZM38.7374 101.906L38.9062 102.638L39.1874 103.294L39.5624 103.894L40.0124 104.419L40.5562 104.869L41.1562 105.244L41.8124 105.525L42.5062 105.694L43.2374 105.75H99.2249L99.8437 105.713L100.425 105.581L100.987 105.394L101.981 104.813L102.806 104.006L103.406 103.013L103.612 102.469L103.744 101.888L103.8 101.269L88.5374 83.8313L88.2749 83.6063L88.0124 83.4751L87.6937 83.4001H87.3937L87.0937 83.4751L86.7937 83.6438L78.3187 89.7376L77.6062 89.9626L77.2312 90.0001L76.4999 89.8688L75.8249 89.5313L75.5249 89.2688L60.4687 74.1376L60.2437 73.9501L59.7187 73.7626L59.1562 73.8001L58.6874 74.0813L58.4999 74.2876L38.7374 101.906Z"
          fill="#999999"
        />
        <path
          d="M82.0124 77.3626L81.1124 77.2876L80.2687 77.1188L79.4624 76.8376L78.6937 76.4626L77.9999 75.9938L77.3624 75.4313L76.7999 74.7938L76.3312 74.1001L75.9562 73.3313L75.6749 72.5251L75.4874 71.6813L75.4312 70.7813L75.4874 69.8626L75.6749 69.0188L75.9562 68.2126L76.3312 67.4438L76.7999 66.7501L77.3624 66.1126L77.9999 65.5501L78.6937 65.0813L79.4624 64.7063L80.2687 64.4251L81.1124 64.2563L82.0124 64.1813L82.9312 64.2563L83.7749 64.4251L84.5812 64.7063L85.3499 65.0813L86.0437 65.5501L86.6812 66.1126L87.2249 66.7501L87.7124 67.4438L88.0874 68.2126L88.3687 69.0188L88.5374 69.8626L88.6124 70.7813L88.5374 71.6813L88.3687 72.5251L88.0874 73.3313L87.7124 74.1001L87.2249 74.7938L86.6812 75.4313L86.0437 75.9938L85.3499 76.4626L84.5812 76.8376L83.7749 77.1188L82.9312 77.2876L82.0124 77.3626ZM82.0124 65.3063L81.2624 65.3626L80.5687 65.5126L79.8937 65.7376L79.2562 66.0563L78.6937 66.4501L78.1687 66.9188L77.6999 67.4438L77.3062 68.0063L76.9874 68.6438L76.7624 69.3188L76.6124 70.0126L76.5562 70.7626L76.6124 71.5126L76.7624 72.2251L76.9874 72.9001L77.3062 73.5188L77.6999 74.1001L78.1687 74.6251L78.6937 75.0938L79.2562 75.4876L79.8937 75.8063L80.5687 76.0313L81.2624 76.1813L82.0124 76.2376L82.7624 76.1813L83.4749 76.0313L84.1499 75.8063L84.7687 75.4876L85.3499 75.0938L85.8749 74.6251L86.3437 74.1001L86.7374 73.5188L87.0562 72.9001L87.2812 72.2251L87.4312 71.5126L87.4874 70.7626L87.4312 70.0126L87.2812 69.3188L87.0562 68.6438L86.7374 68.0063L86.3437 67.4438L85.8749 66.9188L85.3499 66.4501L84.7687 66.0563L84.1499 65.7376L83.4749 65.5126L82.7624 65.3626L82.0124 65.3063Z"
          fill="#999999"
        />
        <defs>
          <linearGradient
            id="paint0_linear_4933_4020"
            x1="20.6684"
            y1="24.8609"
            x2="31.7271"
            y2="21.2382"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#CBDCFF" />
            <stop offset="1" stop-color="#F8E2FF" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_4933_4020"
            x1="42.0209"
            y1="22.7467"
            x2="48.6169"
            y2="20.5859"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#CBDCFF" />
            <stop offset="1" stop-color="#F8E2FF" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_4933_4020"
            x1="13.7077"
            y1="60.378"
            x2="20.2861"
            y2="58.2301"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#CBDCFF" />
            <stop offset="1" stop-color="#F8E2FF" />
          </linearGradient>
          <linearGradient
            id="paint3_linear_4933_4020"
            x1="111.883"
            y1="122.591"
            x2="118.457"
            y2="120.437"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#CBDCFF" />
            <stop offset="1" stop-color="#F8E2FF" />
          </linearGradient>
          <linearGradient
            id="paint4_linear_4933_4020"
            x1="18.0164"
            y1="132.328"
            x2="155.931"
            y2="85.8197"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#CBDCFF" />
            <stop offset="1" stop-color="#F8E2FF" />
          </linearGradient>
        </defs>
      </svg>
    );
  };
  const getDomToHtml = dom => {
    return dom?.map(item => {
      switch (item.id) {
        case 'title':
          return (
            <strong
              style={{
                wordBreak: 'keep-all',
              }}
            >
              {item.label}
            </strong>
          );
        case 'list':
          return (
            <ul>
              <strong style={{ lineHeight: '30px', marginLeft: '-30px' }}>
                {item.label}
              </strong>
              {getDomToHtml(item?.children?.filter((item, index) => index < 5))}
              {item?.children?.length > 5 && <span>...</span>}
            </ul>
          );
        case 'li':
          let liDom = (
            <li
              id="variables_aiAgent"
              style={{
                lineHeight: '25px',
                marginLeft: '-8px',
                wordBreak: 'keep-all',
              }}
            >
              <HighlightText text={item.label} />
            </li>
          );
          return liDom;
        case 'MessageText':
          return (
            <span style={{ maxWidth: 200 }}>
              <HighlightText text={item.label} />
            </span>
          );
        case 'MessageImage':
          return item.showUrl ? (
            <img
              className="dynamicButton_aiAgent"
              src={item.showUrl}
              alt={item.label}
              style={{ width: 160, height: 90 }}
            />
          ) : (
            <div className="dynamicButton_aiAgent"></div>
          );
        case 'MessageRichText':
          return (
            <div
              className="MessageRichText"
              style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}
            >
              <ReactMarkdown>{item.label}</ReactMarkdown>
            </div>
          );
        case 'MessageMedia':
          return item.showUrl ? (
            <video
              className="dynamicButton_aiAgent"
              src={item.showUrl}
              alt={item.label}
              style={{ width: 160, height: 90 }}
            />
          ) : (
            <div className="dynamicButton_aiAgent"></div>
          );
        case 'MessageDoc':
          return item.url ? (
            <div>
              <span style={{ marginBottom: '5px' }}>{item.label}</span>
              <div
                className="dynamicButton_aiAgent"
                style={{
                  width: 169,
                  height: 50,
                  borderRadius: 4,
                  border: '1px solid #E6E6E6',
                  background: 'rgba(255, 255, 255, 0.50)',
                  padding: 10,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <FileFrame />
                <span
                  style={{
                    width: '80%',
                    overflow: 'hidden',
                    fontSize: 12,
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                  }}
                  title={item.fileName}
                >
                  {item.fileName}
                </span>
              </div>
            </div>
          ) : (
            <div className="dynamicButton_aiAgent"></div>
          );
        case 'ToolToAgent':
          return (
            <span className="dynamicButton_aiAgent" style={{ maxWidth: 200 }}>
              <HighlightText text={item.label} />
            </span>
          );
        case 'ToolLLM':
          return (
            <span className="dynamicButton_aiAgent" style={{ maxWidth: 200 }}>
              <HighlightText text={item.prompt} />
            </span>
          );
        case 'ToolAPI':
          return (
            <span className="dynamicButton_aiAgent" style={{ maxWidth: 200 }}>
              {item.apiName}
            </span>
          );
        case 'ToolFailure':
          return (
            <span style={{ maxWidth: 200 }}>
              {getIntl().formatMessage({
                id: 'ai.agent.nodes.ToolFailure.text',
              })}
            </span>
          );
        case 'handle':
          if (
            item.handle == 'AskQuestionFormButton1' ||
            item.handle == 'AskQuestionFormButton2'
          ) {
            return (
              <Button className="dynamicButton_aiAgent">
                <HighlightText text={item.label} />
              </Button>
            );
          } else {
            return (
              <div
                className="dynamicButton_aiAgent"
                id={styles.askQuestionButton}
              >
                <HighlightText text={item.label} />
                {/* {JSON.stringify(item)} */}
              </div>
            );
          }
        case 'Fallback':
          return (
            <span
              style={{ textAlign: 'end' }}
              className="dynamicButton_aiAgent"
            >
              {item.label}
            </span>
          );
        case 'Warning':
          return (
            <span
              style={{ textAlign: 'end' }}
              className="dynamicButton_aiAgent"
            >
              {item.label}
            </span>
          );

        case 'Form':
          return (
            <Form
              labelCol={{
                span: 24,
              }}
              wrapperCol={{
                span: 24,
              }}
              style={{
                width: '100%',
                marginBottom: '-10px',
              }}
              layout="vertical"
            >
              <Form.Item
                label={item.formName}
                name={item.attributeName}
                rules={[
                  {
                    required: item.required == 1 ? true : false,
                  },
                  {
                    pattern: item.regexPattern,
                    message: '',
                  },
                ]}
              >
                {item.attributeType == 1 ? (
                  <Input
                    disabled
                    style={{ width: '100%' }}
                    placeholder={item.inputTip}
                  ></Input>
                ) : item.attributeType == 2 ? (
                  <TextArea
                    disabled
                    style={{ width: '100%' }}
                    placeholder={item.inputTip}
                  ></TextArea>
                ) : item.attributeType == 3 ? (
                  <Select
                    disabled
                    style={{ width: '100%' }}
                    placeholder={item.inputTip}
                  ></Select>
                ) : item.attributeType == 4 ? (
                  <Select
                    disabled
                    style={{ width: '100%' }}
                    placeholder={item.inputTip}
                  ></Select>
                ) : item.attributeType == 5 ? (
                  <Radio.Group
                    disabled
                    style={{ width: '100%' }}
                    placeholder={item.inputTip}
                  >
                    {item.attributeValues.map(ele => {
                      return <Radio value={ele.value}>{ele.name}</Radio>;
                    })}
                  </Radio.Group>
                ) : item.attributeType == 6 ? (
                  <Checkbox.Group
                    disabled
                    style={{ width: '100%' }}
                    placeholder={item.inputTip}
                  >
                    {item.attributeValues.map(ele => {
                      return (
                        <Row>
                          <Col span={24}>
                            <Checkbox value={ele.value}>
                              <HighlightText text={ele.name} />
                            </Checkbox>
                          </Col>
                        </Row>
                      );
                    })}
                  </Checkbox.Group>
                ) : item.attributeType == 7 ? (
                  <DatePicker
                    disabled
                    style={{ width: '100%' }}
                    placeholder={item.inputTip}
                  ></DatePicker>
                ) : item.attributeType == 8 ? (
                  <DatePicker
                    disabled
                    style={{ width: '100%' }}
                    showTime
                    placeholder={item.inputTip}
                  ></DatePicker>
                ) : item.attributeType == 9 ? (
                  <RangePicker disabled style={{ width: '100%' }}></RangePicker>
                ) : item.attributeType == 10 ? (
                  <RangePicker disabled style={{ width: '100%' }}></RangePicker>
                ) : item.attributeType == 11 ? (
                  <div style={{ fontSize: 12, color: '#999' }}>
                    <FormattedMessage
                      id="ai.agent.nodes.AskQuestionForm.show.upload.tips"
                      defaultValue="上传文件，最多可上传5个，单个文件最大20MB"
                    ></FormattedMessage>
                    <div style={{ color: '#3463FC', marginTop: 5 }}>
                      <UploadOutlined
                        style={{ marginRight: 5, color: '#3463FC' }}
                      />
                      <FormattedMessage
                        id="ai.agent.nodes.AskQuestionForm.show.upload"
                        defaultValue="点击上传"
                      ></FormattedMessage>
                    </div>
                  </div>
                ) : (
                  ''
                )}
              </Form.Item>
            </Form>
          );
        case 'AskQuestionText':
          return (
            <span className="dynamicButton_aiAgent" style={{ maxWidth: 200 }}>
              <HighlightText text={item.content} />
            </span>
          );
        case 'ToolVariableSetting':
          return (
            <div className="dynamicButton_aiAgent">
              <div
                style={{ color: '#333', fontSize: '12px', fontWeight: 'bold' }}
              >
                <FormattedMessage
                  id="ai.agent.nodes.ToolVariableSetting.node.title"
                  defaultValue="更新变量"
                ></FormattedMessage>
              </div>
              <HighlightText text={'{' + item.variableName + '}'} />
            </div>
          );
        case 'ToolUpdateCustomer':
          if (item.index == 1) {
            return (
              <div
                className="dynamicButton_aiAgent"
                style={{
                  border: '1px solid #E6E6E6',
                  padding: '7px 10px',
                  borderRadius: '4px',
                }}
              >
                <div>
                  <span>
                    <FormattedMessage
                      id="ai.agent.nodes.ToolUpdateTicket.title1"
                      defaultValue="客户属性"
                    ></FormattedMessage>
                    ：
                  </span>
                  <spann style={{ color: '#3463FC', fontWeight: 'bold' }}>
                    {item.attributeNameText}
                  </spann>
                </div>
                <div>
                  <span>
                    <FormattedMessage
                      id="ai.agent.nodes.ToolUpdateTicket.title2"
                      defaultValue="客户属性值"
                    ></FormattedMessage>
                    ：
                  </span>
                  <HighlightText blueText={true} text={item.attributeValue} />
                </div>
              </div>
            );
          }
        case 'ToolUpdateC':
          return (
            <div
              style={{
                border: '1px solid #E6E6E6',
                padding: '7px 10px',
                borderRadius: '4px',
              }}
            >
              <div>
                <span>
                  <FormattedMessage
                    id="ai.agent.nodes.ToolUpdateTicket.title1"
                    defaultValue="客户属性"
                  ></FormattedMessage>
                  ：
                </span>
                <span style={{ color: '#3463FC', fontWeight: 'bold' }}>
                  {item.attributeNameText}
                </span>
              </div>
              <div>
                <span>
                  <FormattedMessage
                    id="ai.agent.nodes.ToolUpdateTicket.title4"
                    defaultValue="属性值"
                  ></FormattedMessage>
                  ：
                </span>
                <HighlightText blueText={true} text={item.attributeValue} />
              </div>
            </div>
          );
        case 'ToolUpdateTicket':
          if (item.index == 1) {
            return (
              <div
                className="dynamicButton_aiAgent"
                style={{
                  border: '1px solid #E6E6E6',
                  padding: '7px 10px',
                  borderRadius: '4px',
                }}
              >
                <div>
                  <span>
                    <FormattedMessage
                      id="ai.agent.nodes.ToolVariableSetting.title17"
                      defaultValue="工单属性"
                    ></FormattedMessage>
                    ：
                  </span>
                  <spann style={{ color: '#3463FC', fontWeight: 'bold' }}>
                    {item.attributeNameText}
                  </spann>
                </div>
                <div>
                  <span>
                    <FormattedMessage
                      id="ai.agent.nodes.ToolUpdateTicket.title4"
                      defaultValue="属性值"
                    ></FormattedMessage>
                    ：
                  </span>
                  <HighlightText blueText={true} text={item.attributeValue} />
                </div>
              </div>
            );
          }
        case 'ToolUpdateT':
          return (
            <div
              style={{
                border: '1px solid #E6E6E6',
                padding: '7px 10px',
                borderRadius: '4px',
              }}
            >
              <div>
                <FormattedMessage
                  id="ai.agent.nodes.ToolVariableSetting.title17"
                  defaultValue="工单属性"
                ></FormattedMessage>
                ：
                <span style={{ color: '#3463FC', fontWeight: 'bold' }}>
                  {item.attributeNameText}
                </span>
              </div>
              <div>
                <FormattedMessage
                  id="ai.agent.nodes.ToolUpdateTicket.title4"
                  defaultValue="属性值"
                ></FormattedMessage>
                ：
                <HighlightText blueText={true} text={item.attributeValue} />
              </div>
            </div>
          );
        case 'ConditionCheck':
          return (
            <div
              className="dynamicButton_aiAgent"
              style={{
                background: '#fff',
                padding: '10px',
                borderRadius: '4px',
              }}
            >
              <div>
                <FormattedMessage
                  id="ai.agent.nodes.ConditionCheck.title4"
                  defaultValue="如果"
                ></FormattedMessage>
              </div>
              <div style={{ color: '#3463FC', fontWeight: 'bold' }}>
                {item.inputType ? item.inputType : ''}
              </div>
              <div>
                {item.conditionType == 0
                  ? getIntl().formatMessage({
                      id: 'ai.agent.nodes.ConditionCheck.title5',
                      defaultValue: '等于',
                    })
                  : item.conditionType == 1
                  ? getIntl().formatMessage({
                      id: 'ai.agent.nodes.ConditionCheck.title6',
                      defaultValue: '包含',
                    })
                  : item.conditionType == 2
                  ? getIntl().formatMessage({
                      id: 'ai.agent.nodes.ConditionCheck.title7',
                      defaultValue: '为空',
                    })
                  : item.conditionType == 3
                  ? getIntl().formatMessage({
                      id: 'ai.agent.nodes.ConditionCheck.title8',
                      defaultValue: '不为空',
                    })
                  : item.conditionType == 4
                  ? getIntl().formatMessage({
                      id: 'ai.agent.nodes.ConditionCheck.title11',
                      defaultValue: '大于',
                    })
                  : getIntl().formatMessage({
                      id: 'ai.agent.nodes.ConditionCheck.title12',
                      defaultValue: '小于',
                    })}
              </div>
              <div style={{ color: '#3463FC', fontWeight: 'bold' }}>
                {item.inputValue}
              </div>
            </div>
          );
        case 'ConditionCheckElse':
          return (
            <div
              className="dynamicButton_aiAgent"
              style={{
                background: '#fff',
                padding: '10px',
                borderRadius: '4px',
              }}
            >
              <div>
                <FormattedMessage
                  id="ai.agent.nodes.ConditionCheck.title2"
                  defaultValue="否则"
                ></FormattedMessage>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <FormattedMessage
                  id="ai.agent.nodes.ConditionCheck.title3"
                  defaultValue="失败"
                ></FormattedMessage>
              </div>
            </div>
          );
        case 'ToolSetCustomerTag':
          return (
            <div className="dynamicButton_aiAgent">
              {item.tagName.map(ele => {
                return (
                  <Tag
                    className={ele.tagColorCode}
                    style={{
                      marginRight: 2,
                      marginTop: 2,
                      marginLeft: 5,
                      marginBottom: 2,
                      // borderColor: ele.tagColor,
                    }}
                  >
                    {ele.categoryContent + '/' + ele.tagContent}
                  </Tag>
                );
              })}
            </div>
          );
        // case 'MessageHotIssue':
        //   return(

        //   );
        case 'MessageHotIssue':
          return (
            <div className="dynamicButton_aiAgent">
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: '900',
                  marginBottom: 10,
                }}
              >
                <HotIssusIcon />
                <FormattedMessage
                  id="ai.agent.nodes.MessageHotIssue.title8"
                  defaultValue="热点问题"
                ></FormattedMessage>
              </div>
              {item.configType == 1 ? (
                // 手动
                <div>
                  {item.showType == 2 ? (
                    <div>
                      {item.issues[0]?.issueClassifys?.map(ele => {
                        return (
                          <div
                            className={styles.hotIssuetype1}
                            onClick={() => {
                              ele.open = !ele.open;
                            }}
                          >
                            <span>
                              {ele.issueName}
                              <RightOutlined
                                style={{
                                  transform: ele.open
                                    ? 'rotate(90deg)'
                                    : 'rotate(0deg)',
                                  transition: 'all 0.3s ease',
                                }}
                              />
                            </span>
                            {ele.open && (
                              <div className={styles.hotIssuetype1_content}>
                                {ele.issueContents?.map(el => {
                                  return (
                                    <div
                                      className={
                                        styles.hotIssuetype1_content_item
                                      }
                                    >
                                      <div>{el}</div>
                                      <ArrowRightOutlined />
                                    </div>
                                  );
                                })}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div>
                      <Tabs
                        tabBarGutter={10}
                        defaultActiveKey={item.originLanguage}
                        items={item.issues[0]?.issueClassifys?.map(ele => {
                          return {
                            label: ele.issueName,
                            key: ele.issueName,
                            children: ele.issueContents?.map((ele, index) => {
                              return (
                                <div className={styles.hotIssuetype2}>
                                  <div
                                    style={{
                                      display: 'flex',
                                      justifyContent: 'space-between',
                                      width: '100%',
                                      borderBottom: '1px solid #e6e6e6',
                                      padding: '5px 0px',
                                    }}
                                  >
                                    <div>
                                      {index + 1}.{ele}
                                    </div>
                                    <div>
                                      <ArrowRightOutlined />
                                    </div>
                                  </div>
                                </div>
                              );
                            }),
                          };
                        })}
                      >
                        {/* {item.issues[0]?.issueClassifys?.length &&
                        item.issues[0]?.issueClassifys?.map(ele => {
                          return (
                            <Tabs.TabPane
                              tab={ele.issueName}
                              key={ele.issueName}
                            >
                              {ele.issueConents?.map(ele => {
                                return <div>{ele}</div>;
                              })}
                            </Tabs.TabPane>
                          );
                        })} */}
                      </Tabs>
                    </div>
                  )}
                </div>
              ) : (
                // 自动
                <div>
                  <FormattedMessage
                    id="ai.agent.nodes.MessageHotIssue.title9"
                    defaultValue="自动推荐"
                  ></FormattedMessage>
                </div>
              )}
            </div>
          );
        case 'AskQuestionCard':
          return (
            <div className="dynamicButton_aiAgent">
              <div
                style={{
                  color: '#333',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  marginBottom: 10,
                }}
              >
                {item.botMessage}
              </div>
              <div
                style={{
                  color: '#333',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  marginBottom: 10,
                }}
              >
                <FormattedMessage
                  id="ai.agent.nodes.AskQuestionCard.title2"
                  defaultValue="卡片布局"
                ></FormattedMessage>
                ：
                <span style={{ color: '#3463FC', fontWeight: '400' }}>
                  {item.cardLayout == 1
                    ? getIntl().formatMessage({
                        id: 'ai.agent.nodes.AskQuestionCard.title6',
                        defaultValue: '轮播',
                      })
                    : getIntl().formatMessage({
                        id: 'ai.agent.nodes.AskQuestionCard.title7',
                        defaultValue: '列表',
                      })}
                </span>
              </div>

              {item.cardLayout == 1 ? (
                <div className={styles.MessageCardDiv}>
                  <div className={styles.MessageCardDiv_img}>
                    {ExampleImg(150)}
                  </div>
                  <div className={styles.MessageCardDiv_title}>
                    <FormattedMessage
                      id="ai.agent.nodes.AskQuestionCard.title3"
                      defaultValue="这是一个产品标题示例。你可以描述该产品的主要特点。"
                    ></FormattedMessage>
                  </div>
                  <div className={styles.MessageCardDiv_content}>
                    <div style={{ display: 'flex', alignItems: 'flex-end' }}>
                      <FormattedMessage
                        id="ai.agent.nodes.AskQuestionCard.title4"
                        defaultValue="¥0.00"
                      ></FormattedMessage>
                      <div style={{ color: '#999', fontSize: '10px' }}>
                        &nbsp;&nbsp;x1
                      </div>
                    </div>
                    <div>
                      <FormattedMessage
                        id="ai.agent.nodes.AskQuestionCard.title5"
                        defaultValue="已签收"
                      ></FormattedMessage>
                    </div>
                  </div>
                  <div className={styles.MessageCardDiv_button}>
                    <div style={{ color: '#999' }}>
                      <span
                        style={{ fontSize: '14px', transform: 'scale(1.1)' }}
                      >
                        1
                      </span>
                      /5
                    </div>
                    <div className={styles.MessageCardDiv_button_icon}>
                      <span style={{ marginRight: 5 }}>
                        <LeftOutlined />
                      </span>
                      <span>
                        <RightOutlined />
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className={styles.MessageCardDiv2}>
                  <div className={styles.MessageCardDiv2_img}>
                    {ExampleImg(50)}
                  </div>
                  <div>
                    <div className={styles.MessageCardDiv2_title}>
                      <div className={styles.MessageCardDiv2_title_text}>
                        <FormattedMessage
                          id="ai.agent.nodes.AskQuestionCard.title3"
                          defaultValue="这是一个产品标题示例。你可以描述该产品的主要特点。"
                        ></FormattedMessage>
                      </div>
                      <div className={styles.MessageCardDiv2_title_price}>
                        <div>
                          <FormattedMessage
                            id="ai.agent.nodes.AskQuestionCard.title4"
                            defaultValue="¥628.90"
                          ></FormattedMessage>
                        </div>
                        <div>x1</div>
                      </div>
                    </div>
                    <div className={styles.MessageCardDiv2_status}>
                      <FormattedMessage
                        id="ai.agent.nodes.AskQuestionCard.title5"
                        defaultValue="已签收"
                      ></FormattedMessage>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        case 'Knowlege':
          return (
            <div className="dynamicButton_aiAgent">
              <div
                style={{ color: '#333', fontSize: '12px', fontWeight: 'bold' }}
              >
                <FormattedMessage
                  id="ai.agent.nodes.Rag.knowledgeType"
                  defaultValue="知识库类型"
                ></FormattedMessage>
              </div>
              {item.useFaq == 1 ? (
                <div>
                  <YesIcon></YesIcon>
                  <FormattedMessage
                    id="ai.agent.nodes.Rag.faqKnoewledge"
                    defaultValue="FAQ知识库"
                  ></FormattedMessage>
                </div>
              ) : (
                ''
              )}
              {item.useQa == 1 ? (
                <div>
                  <YesIcon></YesIcon>
                  <FormattedMessage
                    id="ai.agent.nodes.Rag.ragKnoewledge"
                    defaultValue="RAG知识库"
                  ></FormattedMessage>
                </div>
              ) : (
                ''
              )}
              <div
                style={{ color: '#333', fontSize: '12px', fontWeight: 'bold' }}
              >
                <FormattedMessage
                  id="ai.agent.nodes.Rag.ragName"
                  defaultValue="Rag知识库名称"
                ></FormattedMessage>
              </div>
              <div style={{ color: '#3463FC', fontSize: '12px' }}>
                {item.knowledgeName || ''}
              </div>
            </div>
          );
        case 'ToolDelay':
          return (
            <div
              className="dynamicButton_aiAgent"
              onClick={e => {
                e.stopPropagation();
              }}
              // key={item.delayTime}
            >
              <div>
                {getIntl()
                  .formatMessage({
                    id: 'ai.agent.nodes.ToolDelay.title1',
                    defaultValue: '延时x秒',
                  })
                  .replace('x', item.delayTime || '0')}
              </div>
              <div
                onMouseEnter={e => {
                  e.stopPropagation();
                  setNodesDraggable(false);
                  // setTimeout(() => {
                  //   setNodesDraggable(true);
                  // }, 1000);
                }}
                onMouseLeave={e => {
                  setNodesDraggable(true);
                }}
              >
                <Slider
                  min={0}
                  max={60}
                  defaultValue={item.delayTime}
                  onChange={e => {
                    setNodes(nds => {
                      return nds.map(item => {
                        let obj = { ...item };
                        if (item.id === id) {
                          obj.data.customizForms[0].delayTime = e;
                        }
                        return obj;
                      });
                    });
                  }}
                />
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>0</span>
                <span>60</span>
              </div>
            </div>
          );
      }
    });
  };
  //去重
  const removeDuplicatesByProperty = data => {
    let result = [];
    let map = new Map();
    data.forEach(item => {
      map.set(item.id, item); // 如果id重复，更新为最新的项
    });
    result = [...map.values()]; // 取出map的值，得到去重后的结果
    return result;
  };
  //去除无用的点
  const filterUselessHandle = list => {
    // 创建哈希表存储列表id与对象的映射关系
    const idMap = new Map();
    for (const item of list) {
      idMap.set(item.id, item); // 使用id作为键，列表对象作为值
    }

    // 结果数组，用于存储与表单匹配的列表对象
    const result = [];

    // 遍历表单数组
    for (const form of customizForms) {
      // 检查表单的handle是否在哈希表中存在
      if (idMap.has(form.handle)) {
        // 如果存在，则将对应的列表对象添加到结果数组中
        result.push(idMap.get(form.handle));
      }
    }
    // 单独处理AskQuestionButton和ConditionIntent的fallback节点,解决需要在fallBack前插入蓝点导致，连线失效问题
    if (componentType === 'ConditionIntent') {
      result.push({
        type: 'source',
        position: 'right',
        id: 'Fallback',
        style: {
          top: '100%',
          backgroundColor: '#F22417',
        },
      });
    } else if (componentType === 'AskQuestionButton') {
      result.push({
        type: 'source',
        position: 'right',
        id: 'AskQuestionTextFallBack',
        style: {
          top: '100%',
          backgroundColor: '#F22417',
        },
      });
    }
    // 返回结果数组
    return result;
  };

  useEffect(() => {
    const aElements = document.querySelectorAll(`.modalNodes_${id}`);
    const buttons = [];
    // 遍历所有类名为  'modalNodes_${id}'  的元素
    aElements.forEach(aElement => {
      // 选择所有 class 为 'dynamicButton_aiAgent' 的 DOM 元素
      const bElementsInA = aElement.querySelectorAll('.dynamicButton_aiAgent');
      // 将找到的元素添加到 bElements 数组中
      buttons.push(...bElementsInA);
    });
    let handleListNew = handleList ? handleList : [];
    // let handleListNew = [];

    console.log(
      buttons,
      customizForms,
      componentType,
      id,
      handleListNew,
      handleList,
      aElements,
      'buttonsllm集合',
    );
    //需要判断自己已经初始化连接点的节点，比如MessageText，不需要走循环里
    if (customizForms && customizForms?.[customizForms?.length - 1]?.handle) {
      setNodes(nds => {
        console.log(nds, 'setNodesllm集合');

        //以上拿到按钮后的节点位置，set到对应的Json中
        let newNds = nds?.map((item, i) => {
          console.log(item, 'llm集合');
          if (item.id === id) {
            item?.data?.customizForms?.forEach(param => {
              buttons.forEach((button, index) => {
                // console.log(
                //   param.index,
                //   index + 1,
                //   handleListNew,
                //   buttons,
                //   param,
                //   'param.index',
                // );
                // 获取每个按钮的距离父元素顶部的距离
                const rect = button.offsetTop;
                const rectHeight = Number(button.offsetHeight) / 2;
                if (rect) {
                  const distanceFromTop =
                    rectHeight !== 0
                      ? Number(rect) + Number(rectHeight)
                      : Number(rect) + 25;
                  console.log('distanceFromTop', distanceFromTop);
                  if (
                    [
                      'handle',
                      'ToolToAgent',
                      'MessageImage',
                      'MessageMedia',
                      'MessageDoc',
                      'ToolLLM',
                      'AskQuestionText',
                      'ToolAPI',
                      'ToolAttrSetting',
                      'ToolVariableSetting',
                      'ToolUpdateCustomer',
                      'ToolUpdateTicket',
                      'ConditionCheck',
                      'ConditionCheckElse',
                      'ToolSetCustomerTag',
                      'MessageHotIssue',
                      'AskQuestionCard',
                      'ToolDelay',
                    ].includes(param.id) &&
                    param.handle &&
                    param.index == index + 1
                  ) {
                    console.log(
                      '这是距离',
                      distanceFromTop,
                      rect,
                      button,
                      param.handle,
                    );
                    handleListNew.push({
                      type: 'source',
                      position: 'right',
                      id: param.handle,
                      style: { top: distanceFromTop },
                    });
                  } else if (
                    param.id === 'Fallback' &&
                    param.handle &&
                    param.index == index + 1
                  ) {
                    handleListNew.push({
                      type: 'source',
                      position: 'right',
                      id: param.handle,
                      style: {
                        top: '100%',
                        backgroundColor: '#F22417',
                      },
                    });
                  } else if (
                    param.id === 'Warning' &&
                    param.handle &&
                    param.index == index + 1
                  ) {
                    handleListNew.push({
                      type: 'source',
                      position: 'right',
                      id: param.handle,
                      style: {
                        top: 'calc(100% - 20px)',
                        backgroundColor: '#FCB830',
                      },
                    });
                  } else if (
                    param.id === 'Knowlege' &&
                    param.handle &&
                    param.index == index + 1
                  ) {
                    handleListNew.push({
                      type: 'source',
                      position: 'right',
                      id: param.handle,
                      style: {
                        top: { top: distanceFromTop },
                      },
                    });
                  }
                }
              });
            });
            console.log(`这里是去重前的集合`, handleListNew);
            //两道工艺
            //去重
            let result = removeDuplicatesByProperty(handleListNew);
            console.log(`这里是去重后的集合`, result);
            //去无用的点
            let resultFin = filterUselessHandle(result);
            console.log(`这里是去删除点的集合`, resultFin);
            console.log({ ...item.data, handleList: resultFin }, 'itemllm集合');
            // dispatch({
            //   type: 'aiagent/setNewNodes',
            //   payload: [
            //     ...newNodes,
            //     {
            //       ...item,
            //       data: { ...item.data, handleList: resultFin },
            //     },
            //   ],
            // }); // tab值存入全局
            return {
              ...item,
              data: { ...item.data, handleList: resultFin },
            };
          } else {
            return { ...item };
          }
        });
        console.log(newNds, 'ndsllm集合');
        let changeNodes = removeDuplicateIds(newNds);
        return changeNodes;
      });
    }

    // let status = localStorage.getItem('aiAgentStatus');
    //修改和复制不需要重新计算节点位置
    // if (status === 'create') {
    // 获取所有类名为 'modalNodes_${id}' 的元素,目的是隔离每个node连接点的计算
  }, [customizForms, setNodes]);
  const removeDuplicateIds = dataArray => {
    const processedDataArray = [];

    dataArray.forEach(item => {
      const uniqueIds = [];
      const { handleList, ...rest } = item.data; // 提取 handleList 外的其他属性
      console.log(handleList, 'handleList');
      const uniqueHandleList = handleList?.filter(handle => {
        const { id } = handle; // 获取每个 handle 的 id 属性
        if (!uniqueIds.includes(id)) {
          // 如果 id 不在 uniqueIds 中，则添加进去并保留 handle
          uniqueIds.push(id);
          return true;
        }
        // 如果 id 已经存在，则过滤掉这个 handle
        return false;
      });
      // 将处理过的 handleList 和其他属性合并成一个新的对象，并添加到 processedDataArray 中
      processedDataArray.push({
        ...item,
        data: { ...rest, handleList: uniqueHandleList },
      });
    });
    console.log(processedDataArray, 'onNodesChange===');
    return processedDataArray;
  };
  return (
    <div className={`${styles.nodeContent} ${styles[componentType]} `}>
      {getDomToHtml(customizForms)}
    </div>
  );
};

export default NodeContent;
