import {
  // getSmoothStepPath,
  getEdgeCenter,
  getMarkerEnd,
  useReactFlow,
} from 'react-flow-renderer';
import styles from './style.less';
import { EdgeDeteleIcon } from './icon.js';
import { getSmoothStepPath } from './smoothstep-edge';

const ModalEdge = props => {
  const {
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    markerEnd,
    style,
    data,
    labelStyle,
    label,
    labelShowBg,
    labelBgStyle,
    labelBgPadding,
    labelBgBorderRadius,
    animated,
    markerStart,
    pathOptions,
    interactionWidth = 20,
  } = props;
  const foreignObjectSize = 40;
  const { setEdges, setNodes } = useReactFlow();

  const onEdgeClick = (evt, id) => {
    let nodeId = '';
    let nodeName = '';
    setEdges(edges =>
      edges.filter(edge => {
        if (edge.id !== id) {
          return edge;
        } else {
          nodeId = edge.source;
          nodeName = edge.sourceHandle;
        }
      }),
    );
    //以下逻辑用于限制一个句柄仅可以产出一条连接线
    setNodes(nds => {
      let newNds = nds?.map((item, i) => {
        let it = item;
        if (it.id === nodeId) {
          it.data.handleList?.forEach(handle => {
            console.log(handle.id, nodeName, 'onConnect1');
            if (handle.id === nodeName || (!nodeName && !handle.id)) {
              handle.isConnectable = true;
            }
          });
        }
        return it;
      });
      return newNds;
    });
  };
  console.log(
    id,
    markerEnd,
    style,
    data,
    labelStyle,
    label,
    animated,
    '倒计时',
  );
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: pathOptions?.borderRadius,
    offset: pathOptions?.offset,
  });
  const [edgeCenterX, edgeCenterY] = getEdgeCenter({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });
  return (
    <>
      <path
        {...props}
        d={edgePath}
        fill="none"
        className="react-flow__edge-path"
        markerEnd={markerEnd}
        style={{ ...style, strokeWidth: 2 }}
      />
      {/* {interactionWidth && (
        <path
          d={edgePath}
          fill="none"
          strokeOpacity={0}
          strokeWidth={interactionWidth}
          className="react-flow__edge-interaction"
        />
      )} */}
      <foreignObject
        width={foreignObjectSize}
        height={foreignObjectSize}
        x={edgeCenterX - foreignObjectSize / 2}
        y={edgeCenterY - foreignObjectSize / 2}
        className={styles.edgebuttonForeignobject}
      >
        <body>
          <button
            className={styles.edgebutton}
            onClick={event => onEdgeClick(event, id)}
          >
            {EdgeDeteleIcon()}
          </button>
        </body>
      </foreignObject>
      {/* {label  ? (
        <EdgeText
          x={labelX}
          y={labelY}
          label={label}
          labelStyle={labelStyle}
          labelShowBg={labelShowBg}
          labelBgStyle={labelBgStyle}
          labelBgPadding={labelBgPadding}
          labelBgBorderRadius={labelBgBorderRadius}
        />
      ) : null} */}

      {/* <path
        id={id}
        d={edgePath}
        markerEnd={markerEnd}
        style={{ ...style, strokeWidth: 2 }}
        className="react-flow__edge-path"
      />
      <foreignObject
        width={foreignObjectSize}
        height={foreignObjectSize}
        x={edgeCenterX - foreignObjectSize / 2}
        y={edgeCenterY - foreignObjectSize / 2}
        className={styles.edgebuttonForeignobject}
      >
        <body>
          <button
            className={styles.edgebutton}
            onClick={event => onEdgeClick(event, id)}
          >
            {EdgeDeteleIcon()}
          </button>
        </body>
      </foreignObject> */}
    </>
  );
};
export default ModalEdge;
