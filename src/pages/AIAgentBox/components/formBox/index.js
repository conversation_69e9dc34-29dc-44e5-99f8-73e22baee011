import NodeBox from './components/nodeBox/index';
import { QuestionFontIcon, QuestionBtnIcon, FormCloseIcon } from './icon.js';
import { ReactComponent as Search } from '@/assets/Search.svg';
import { FormattedMessage, getIntl, getLocale, useDispatch } from 'umi';
import styles from './style.less';
import { Input, Spin, notification } from 'antd';
import { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

const FormBox = props => {
  const [openFlag, setOpenFlag] = useState(false);
  const [componentListAll, setComponentListAll] = useState([]);
  const [componentList, setComponentList] = useState([]);
  const [componentLoading, setComponentLoading] = useState(false);
  const [componentName, setComponentName] = useState('');
  const { setComponentsList } = props;
  const dispatch = useDispatch();

  const closeFormBox = () => {
    setOpenFlag(false);
  };
  const openFormBox = () => {
    setOpenFlag(true);
  };
  useEffect(() => {
    getAiAgentComList();
  }, []);
  //所有组件库列表
  const getAiAgentComList = () => {
    setComponentLoading(true);
    dispatch({
      type: 'aiagent/getAiAgentComList',
      payload: {
        lang: getLocale(),
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          let newData = { ...data };
          console.log(newData, '组件库列表');
          setComponentListAll(newData);
          setComponentList(newData);
          // setComponentsList([
          //   ...newData.message,
          //   ...newData.condition,
          //   ...newData.tools,
          //   ...newData.askQuestion,
          // ]);
        } else {
          notification.error({
            message: msg,
          });
        }
        setComponentLoading(false);
      },
    });
  };
  //搜索组件
  const onSearchChange = e => {
    let searchName = e.target.value;
    let newComponentList = {};
    setComponentName(searchName);
    // 遍历对象的每个属性
    for (const key in componentListAll) {
      const foundItem = componentListAll[key]
        ?.map(item => {
          if (
            item.componentName.toLowerCase().includes(searchName.toLowerCase())
          ) {
            return item;
          }
        })
        .filter(item => item);
      if (foundItem?.length > 0) {
        newComponentList[key] = foundItem;
        console.log(
          foundItem,
          newComponentList,
          'Object.entries(componentList)',
        );
      }
    }
    setComponentList(newComponentList);
  };
  return (
    <div
      className={openFlag ? styles.formBox : styles.formBoxSmall}
      onClick={() => (openFlag ? {} : openFormBox())}
    >
      {openFlag ? (
        <div className={styles.formBox_div}>
          <div className={styles.formBox_title}>
            <FormattedMessage
              id="ai.agent.nodes.form.name"
              defaultValue="组件库"
            ></FormattedMessage>
            <span
              className={styles.formBox_title_close}
              onClick={() => closeFormBox()}
            >
              {FormCloseIcon()}
            </span>
          </div>
          <div className={styles.formBox_search}>
            <Input
              placeholder={getIntl().formatMessage({
                id: 'ai.agent.nodes.form.search',
              })}
              value={componentName}
              prefix={<Search />}
              style={{ width: '100%', height: 32 }}
              onChange={e => onSearchChange(e)}
            />
          </div>
          <Spin spinning={componentLoading}>
            <div className={styles.formBox_components}>
              {componentList.message ? (
                <>
                  <div className={styles.formBox_components_title}>
                    <FormattedMessage
                      id="ai.agent.nodes.form.message"
                      defaultValue="机器人消息"
                    ></FormattedMessage>
                  </div>
                  <div
                    className={styles.formBox_components_content}
                    style={{
                      marginBottom:
                        componentList?.message.length > 6 ? '15px' : 0,
                    }}
                  >
                    {componentList?.message?.map(n => {
                      let key = uuidv4();
                      return (
                        <NodeBox
                          key={key}
                          id={n.componentType}
                          data={{
                            key: key,
                            componentId: n.componentId,
                            componentName: n.componentName,
                            componentColor: n.color,
                            componentToolsIcon: n.toolsIcon,
                            nodeAlias: '',
                            componentMiniIcon: n.miniIcon,
                            blockNode: n.blockNode,
                            componentStyle: { maxWidth: 220 },
                            customizForms: n.customizForms,
                            handleList: n.handleList,
                          }}
                        />
                      );
                    })}
                  </div>
                </>
              ) : null}
              {componentList.askQuestion ? (
                <>
                  <div className={styles.formBox_components_title}>
                    <FormattedMessage
                      id="ai.agent.nodes.form.question"
                      defaultValue="提问问题"
                    ></FormattedMessage>
                  </div>
                  <div
                    className={styles.formBox_components_content}
                    style={{
                      marginBottom:
                        componentList?.askQuestion.length > 6 ? '15px' : 0,
                    }}
                  >
                    {componentList?.askQuestion?.map(n => {
                      let key = uuidv4();
                      return (
                        <NodeBox
                          key={key}
                          id={n.componentType}
                          data={{
                            key: key,
                            componentId: n.componentId,
                            componentName: n.componentName,
                            componentColor: n.color,
                            componentToolsIcon: n.toolsIcon,
                            nodeAlias: '',
                            componentMiniIcon: n.miniIcon,
                            blockNode: n.blockNode,
                            componentStyle: { maxWidth: 220 },
                            customizForms: n.customizForms,
                            handleList: n.handleList,
                          }}
                        />
                      );
                    })}
                  </div>
                </>
              ) : null}
              {componentList.tools ? (
                <>
                  <div className={styles.formBox_components_title}>
                    <FormattedMessage
                      id="ai.agent.nodes.form.tools"
                      defaultValue="工具"
                    ></FormattedMessage>
                  </div>
                  <div
                    className={styles.formBox_components_content}
                    style={{
                      marginBottom:
                        componentList?.tools.length > 6 ? '15px' : 0,
                    }}
                  >
                    {componentList?.tools?.map(n => {
                      let key = uuidv4();
                      return (
                        <NodeBox
                          key={key}
                          id={n.componentType}
                          data={{
                            key: key,
                            componentId: n.componentId,
                            componentName: n.componentName,
                            componentColor: n.color,
                            componentToolsIcon: n.toolsIcon,
                            nodeAlias: '',
                            componentMiniIcon: n.miniIcon,
                            blockNode: n.blockNode,
                            componentStyle: { maxWidth: 220 },
                            customizForms: n.customizForms,
                            handleList: n.handleList,
                          }}
                        />
                      );
                    })}
                  </div>
                </>
              ) : null}
              {componentList.condition ? (
                <>
                  <div className={styles.formBox_components_title}>
                    <FormattedMessage
                      id="ai.agent.nodes.form.condition"
                      defaultValue="条件"
                    ></FormattedMessage>
                  </div>
                  <div
                    className={styles.formBox_components_content}
                    style={{
                      marginBottom:
                        componentList?.condition.length > 6 ? '15px' : 0,
                    }}
                  >
                    {componentList?.condition?.map(n => {
                      let key = uuidv4();
                      return (
                        <NodeBox
                          key={key}
                          id={n.componentType}
                          data={{
                            key: key,
                            componentId: n.componentId,
                            componentName: n.componentName,
                            componentColor: n.color,
                            componentToolsIcon: n.toolsIcon,
                            nodeAlias: '',
                            componentMiniIcon: n.miniIcon,
                            blockNode: n.blockNode,
                            componentStyle: { maxWidth: 220 },
                            customizForms: n.customizForms,
                            handleList: n.handleList,
                          }}
                        />
                      );
                    })}
                  </div>
                </>
              ) : null}
            </div>
          </Spin>
        </div>
      ) : (
        <>
          <div
            className={styles.bg}
            style={{ letterSpacing: getLocale() == 'zh-CN' ? 6 : '' }}
          >
            <FormattedMessage
              id="ai.agent.nodes.form.name.small"
              defaultValue="新建组件"
            ></FormattedMessage>
          </div>
          <div className={styles.blob}></div>
        </>
      )}
    </div>
  );
};

export default FormBox;
