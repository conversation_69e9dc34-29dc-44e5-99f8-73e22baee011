import CanvasFlow from '../components/canvasFlow';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { history } from 'umi';
import { useState, useEffect } from 'react';
import styles from './style.less';

const FlowLayout = props => {
  const [routeInfo, setRouteInfo] = useState({});
  /**
   * status：‘create’创建
   * status：‘copy’复制
   * status：‘editor’修改
   */
  useEffect(() => {
    setRouteInfo(history.location.state);
    console.log(history.location.state, 'history');
  }, []);
  return (
    <div id={'#layoutBox'} className={styles.layoutBox} style={{ padding: 20 }}>
      <DndProvider backend={HTML5Backend}>
        <CanvasFlow routeInfo={history.location.state} />
      </DndProvider>
    </div>
  );
};

export default FlowLayout;
