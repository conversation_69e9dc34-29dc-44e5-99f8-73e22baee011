import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import styles from './index.less';
import {
  Col,
  Row,
  Progress,
  Table,
  Select,
  Spin,
  notification,
  Tooltip,
  Empty,
  message,
} from 'antd';
const Carousel = forwardRef(
  (
    {
      children,
      index,
      length,
      topTitle,
      topTips,
      icon,
      iconW,
      content,
      hover,
      handleNextNow,
      // handlePrevNow,
    },
    ref,
  ) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [currentIndexNext, setCurrentIndexNext] = useState(1);
    const [currentIndexEffectNext, setCurrentIndexEffectNext] = useState(2);
    const [currentIndexPrev, setCurrentIndexPrev] = useState(length - 1);
    const [currentIndexEffect, setCurrentIndexEffect] = useState(length - 2);
    const [direction, setDirection] = useState('left'); //记录方向
    const [timer, setTimer] = useState(null);
    // useEffect(() => {
    //   const timer = setInterval(() => {
    //     setCurrentIndexNext(prevIndex => (prevIndex + 1) % length);
    //     setCurrentIndex(prevIndex => (prevIndex + 1) % length);
    //     setCurrentIndexPrev(prevIndex => (prevIndex + 1) % length);
    //     setCurrentIndexEffect(prevIndex => (prevIndex + 1) % length);
    //   }, 4000);
    //   return () => clearInterval(timer);
    // }, []);
    useEffect(() => {
      // setDirection('left');
      if (hover) {
        clearInterval(timer);
      } else {
        const newTimer = setInterval(() => {
          setCurrentIndexNext(prevIndex => (prevIndex + 1) % length);
          setCurrentIndex(prevIndex => (prevIndex + 1) % length);
          setCurrentIndexPrev(prevIndex => (prevIndex + 1) % length);
          setCurrentIndexEffect(prevIndex => (prevIndex + 1) % length);
          setCurrentIndexEffectNext(prevIndex => (prevIndex + 1) % length);
        }, 15000);
        setTimer(newTimer);
      }
    }, [hover]);
    /**
     * 回显
     */
    // 在子组件中暴露方法给父组件调用
    useImperativeHandle(ref, () => ({
      handleNext: handleNext,
      // handlePrev: handlePrev,
    }));
    const handleNext = () => {
      console.log('调子组件Next');
      setDirection('left');

      setCurrentIndexNext(prevIndex => (prevIndex + 1) % length);
      setCurrentIndex(prevIndex => (prevIndex + 1) % length);
      setCurrentIndexPrev(prevIndex => (prevIndex + 1) % length);
      setCurrentIndexEffectNext(prevIndex => (prevIndex + 1) % length);
      setCurrentIndexEffect(prevIndex => (prevIndex + 1) % length);
    };
    // const handlePrev = () => {
    //   console.log('调子组件Prev');
    //   setDirection('right');

    //   setCurrentIndexNext(prevIndex => (length - 1 + prevIndex) % length);
    //   setCurrentIndex(prevIndex => (length - 1 + prevIndex) % length);
    //   setCurrentIndexPrev(prevIndex => (length - 1 + prevIndex) % length);
    //   setCurrentIndexEffect(prevIndex => (length - 1 + prevIndex) % length);
    //   setCurrentIndexEffectNext(prevIndex => (length - 1 + prevIndex) % length);
    // };
    return (
      <div
        className={`${styles.carousel}  ${
          direction === 'left'
            ? currentIndex === index
              ? styles.carousel_box1
              : currentIndexNext === index
              ? styles.carousel_box2
              : currentIndexPrev === index
              ? styles.carousel_box3
              : currentIndexEffect === index
              ? styles.carousel_box5
              : styles.carousel_box4
            : currentIndex === index
            ? styles.carousel_box11
            : currentIndexNext === index
            ? styles.carousel_box12
            : currentIndexPrev === index
            ? styles.carousel_box13
            : currentIndexEffect === index
            ? styles.carousel_box15
            : styles.carousel_box14
        } ${
          sessionStorage.getItem('menu_collapsed_state') === 'true'
            ? currentIndex === index
              ? styles.carousel_box1_collapsed
              : currentIndexNext === index
              ? styles.carousel_box2_collapsed
              : currentIndexPrev === index
              ? styles.carousel_box3_collapsed
              : currentIndexEffect === index
              ? styles.carousel_box5_collapsed
              : ''
            : ''
        }`}
        // style={
        //   currentIndex !== index &&
        //   currentIndexNext !== index &&
        //   currentIndexPrev !== index &&
        //   currentIndexEffect !== index
        //     ? {left: `${index*2}+vw`,
        //       transform: scale(index)}
        //     : {}
        // }
        onClick={
          [currentIndexNext, currentIndexPrev, currentIndex].includes(index)
            ? () => handleNextNow()
            : // : currentIndexPrev === index
              // ? () => handlePrevNow()
              null
        }
      >
        <ProgressCard
          topTitle={topTitle}
          topTips={topTips}
          icon={currentIndex === index ? iconW : icon}
          content={content}
          // key={item.channelTypeCode}
        ></ProgressCard>
      </div>
    );
  },
);

const ProgressCard = props => {
  return (
    <div className={styles.progressCardContent}>
      <Row justify="space-between" style={{ marginBottom: 15 }}>
        <div className={styles.progressCardContentLeft}>
          <span className={styles.progressCardContentLeftTop}>
            {props.topTitle}
          </span>
          <span className={styles.progressCardContentLeftBtm}>
            {props.topTips}
          </span>
        </div>
        <div className={styles.progressCardContentRight}>
          <img src={props.icon} />
        </div>
      </Row>
      <Row>
        {props.content?.length > 0 &&
          props.content.map((item, index) => {
            return ['处理中', 'Processing'].includes(item.resolveStatusEn) ? (
              <Progress
                key={index}
                percent={item.eachStatusPercent}
                status="active"
                format={value => {
                  return (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: 140,
                      }}
                    >
                      <div
                        style={{
                          display: 'inline-block',
                          marginRight:
                            item.resolveStatus === '处理中' ? 10 : 10,
                          marginLeft: 0,
                          flex: 1,
                        }}
                      >
                        {item.resolveStatus}
                      </div>
                      <div
                        style={{
                          display: 'inline-block',
                          flex: 1,
                        }}
                      >
                        {value + '%'}
                      </div>
                    </div>
                  );
                }}
              />
            ) : ['已解决', 'Resolved'].includes(item.resolveStatusEn) ? (
              <Progress
                key={index}
                percent={item.eachStatusPercent}
                className={styles.progress2}
                format={value => {
                  return (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: 140,
                      }}
                    >
                      <div
                        style={{
                          display: 'inline-block',
                          marginRight: 10,
                          marginLeft: 0,
                          flex: 1,
                        }}
                      >
                        {item.resolveStatus}
                      </div>
                      <div
                        style={{
                          display: 'inline-block',
                          flex: 1,
                        }}
                      >
                        {value + '%'}
                      </div>
                    </div>
                  );
                }}
              />
            ) : ['已超时', 'Timeout'].includes(item.resolveStatusEn) ? (
              <Progress
                key={index}
                percent={item.eachStatusPercent}
                className={styles.progress3}
                format={value => {
                  return (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: 140,
                      }}
                    >
                      <div
                        style={{
                          display: 'inline-block',
                          marginRight: 10,
                          marginLeft: 0,
                          flex: 1,
                        }}
                      >
                        {item.resolveStatus}
                      </div>
                      <div
                        style={{
                          display: 'inline-block',
                          flex: 1,
                        }}
                      >
                        {value + '%'}
                      </div>
                    </div>
                  );
                }}
              />
            ) : ['待分配', 'Pending'].includes(item.resolveStatusEn) ? (
              <Progress
                key={index}
                percent={item.eachStatusPercent}
                className={styles.progress4}
                format={value => {
                  return (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: 140,
                      }}
                    >
                      <div
                        style={{
                          display: 'inline-block',
                          marginRight: 10,
                          marginLeft: 0,
                          flex: 1,
                        }}
                      >
                        {item.resolveStatus}
                      </div>
                      <div
                        style={{
                          display: 'inline-block',
                          flex: 1,
                        }}
                      >
                        {value + '%'}
                      </div>
                    </div>
                  );
                }}
              />
            ) : (
              ''
            );
          })}
      </Row>
    </div>
  );
};
export default Carousel;
