import customerInformation from './ja/customerInformation';
import selfConfiguration from './ja/selfConfiguration';
import orderAmazon from './ja/orderAmazon';
import customerDataGroupManagement from './ja/customerDataGroupManagement';
import auth from './ja/auth';
import piecemealWhatsApp from './ja/piecemealWhatsApp';
import sendMsg from '@/locales/ja/sendMsg';
import awsAccountSetting from './ja/awsAccountSetting';
import customerList from './ja/customerList';
import crmWorkRecord from './ja/crmWorkRecord';
import channel from './ja/channel';
import worktable from './ja/worktable';
import userManagement from '@/locales/ja/userManagement';
import agentManagement from '@/locales/ja/agentManagment';
import knowledgeQA from '@/locales/ja/knowledgeQA';
import statistics from '@/locales/ja/statistics';
import personal from '@/locales/ja/personal';
import home from '@/locales/ja/home';
import emailChannelConfiguration from './ja/emailChannelConfiguration';
import agentIMChat from './ja/agentIMChat';
import feedbackPerformance from './ja/feedbackPerformance';
import faceBookConfiguration from './ja/faceBookConfiguration';
import weChatConfiguration from './ja/weChatConfiguration';
import instagramConfiguration from './ja/instagramConfiguration';
import chatVoiceChannelConfiguration from './ja/chatVoiceChannelConfiguration';
// import homePage from '@/locales/ja/homePage';
import customerExtensionInformation from './ja/customerExtensionInformation';
import workOrderManagement from './ja/workOrderManagement';
import cloudContactCenter from './ja/cloudContactCenter';
import aigc from './ja/AIGC';
import homePage from './ja/homePage';
import contactCustomers from './ja/contactCustomers';
import documentKnowledgeBase from './ja/documentKnowledgeBase';
import definitionSynonyms from './ja/definitionSynonyms';
import chatChannelConfiguration from './ja/chatChannelConfiguration';
import whatsAppChannelConfiguration from './ja/whatsAppChannelConfiguration';
import beginnerGuidance from './ja/beginnerGuidance';
import marketingActivities from './ja/marketingActivities';
import marketingResults from './ja/marketingResults';
import emailMarketing from './ja/emailMarketing';
import agentWorkloadReport from './ja/agentWorkloadReport';
import selfAssessmentDetails from './ja/selfAssessmentDetails';
import amazonRegionConfiguration from './ja/amazonRegionConfiguration';
import googlePlayConfiguration from './ja/googlePlayConfiguration';
import tagManagement from './ja/tagManagement';
import alloctionRule from './ja/alloctionRule';
import site from './ja/site';
import meteringBilling from './ja/meteringBilling';

import AIAgent from './ja/AIAgent';
import intentionManagement from './ja/intentionManagement';
import hotlineKeyIndicators from './ja/hotlineKeyIndicators';
import hotlineKeyIndicatorsConfig from './ja/hotlineKeyIndicatorsConfig';

import aigcCustomerService from './ja/aigcCustomerService';
import footer from './ja/footer';
import header from './ja/header';
import homePageNew from './ja/homePageNew';
import productChannel from './ja/productChannel';
import finance from './ja/finance';
import retail from './ja/retail';
import manufacture from './ja/manufacture';
import electronics from './ja/electronics';
import newEnergy from './ja/newEnergy';
import partner from './ja/partner';
import resources from './ja/resources';
import callCenter from './ja/callCenter';
import productAiAgent from './ja/productAiAgent';
import productAssistant from './ja/productAssistant';
import dataReport from './ja/dataReport';
import marketing from './ja/marketing';
import smartWorkOrder from './ja/smartWorkOrder';
import videoCustomerService from './ja/videoCustomerService';
import voiceRobot from './ja/voiceRobot';
import settingTicket from './ja/settingTicket';
import privacyPolicy from './ja/privacyPolicy';
import apiManage from './ja/apiManage';
import aiAgentLibrary from './ja/aiAgentLibrary';
import cookiePolicy from './ja/cookiePolicy';
import inactiveMessageReminder from './ja/inactiveMessageReminder';
import productComplianceGuide from './ja/productComplianceGuide';
import intelligentFormFilling from './ja/intelligentFormFilling';
import joinUs from './ja/joinUs';
import cookie from './ja/cookie';
import userTerms from './ja/userTerms';
import euAiActCompliance from './ja/euAiActCompliance';
import gdprCompliance from './ja/gdprCompliance';
import workerOffers from './ja/workerOffers';
import smartQualityInspection from './ja/smartQualityInspection';
import homePageMobile from './ja/homePageMobile';
import helpDocument from './ja/helpDocument';
import shareComponents from './ja/shareComponents';
export default {
  ...workerOffers,
  ...piecemealWhatsApp,
  ...meteringBilling,
  ...orderAmazon,
  ...AIAgent,
  ...weChatConfiguration,
  ...chatVoiceChannelConfiguration,
  ...googlePlayConfiguration,
  ...instagramConfiguration,
  ...customerInformation,
  ...customerDataGroupManagement,
  ...selfConfiguration,
  ...amazonRegionConfiguration,
  ...faceBookConfiguration,
  ...auth,
  ...feedbackPerformance,
  ...sendMsg,
  ...home,
  ...statistics,
  ...aigc,
  ...emailMarketing,
  ...knowledgeQA,
  ...chatChannelConfiguration,
  ...whatsAppChannelConfiguration,
  ...agentWorkloadReport,
  // ...homePage,
  ...awsAccountSetting,
  ...cloudContactCenter,
  ...customerList,
  ...crmWorkRecord,
  ...channel,
  ...agentManagement,
  ...worktable,
  ...userManagement,
  ...personal,
  ...customerExtensionInformation,
  ...workOrderManagement,
  ...homePage,
  ...contactCustomers,
  ...documentKnowledgeBase,
  ...definitionSynonyms,
  ...emailChannelConfiguration,
  ...beginnerGuidance,
  ...marketingActivities,
  ...marketingResults,
  ...agentIMChat,
  ...selfAssessmentDetails,
  ...tagManagement,
  ...alloctionRule,
  ...site,
  ...intentionManagement,
  ...hotlineKeyIndicators,
  ...hotlineKeyIndicatorsConfig,
  ...footer,
  ...header,
  ...aigcCustomerService,
  ...homePageNew,
  ...productChannel,
  ...finance,
  ...retail,
  ...manufacture,
  ...electronics,
  ...newEnergy,
  ...partner,
  ...resources,
  ...callCenter,
  ...productAiAgent,
  ...productAssistant,
  ...dataReport,
  ...marketing,
  ...smartWorkOrder,
  ...videoCustomerService,
  ...voiceRobot,
  ...apiManage,
  ...settingTicket,
  ...aiAgentLibrary,
  ...privacyPolicy,
  ...cookiePolicy,
  ...inactiveMessageReminder,
  ...productComplianceGuide,
  ...joinUs,
  ...intelligentFormFilling,
  ...cookie,
  ...userTerms,
  ...euAiActCompliance,
  ...gdprCompliance,
  ...workerOffers,
  ...smartQualityInspection,
  ...homePageMobile,
  ...helpDocument,
  ...shareComponents,
};
