export default {
  // 营销结果
  'marketing.results.activity.name.placeholder': '请选择活动名称',
  'marketing.results.marketing.channel.type': '营销渠道类型：',
  'marketing.results.marketing.channel.type.placeholder': '请选择营销渠道类型',
  'marketing.results.marketing.event.name': '营销事件名称：',
  'marketing.results.marketing.event.name.placeholder': '请选择营销事件名称',

  'marketing.results.table.activity.name': '活动名称',
  'marketing.results.table.marketing.channel.type': '营销渠道类型',
  'marketing.results.table.send.channel': '发送渠道',
  'marketing.results.table.marketing.events': '营销事件',
  'marketing.results.table.marketing.methods': '营销方式',
  'marketing.results.table.test.type': 'A/B测试类型',
  'marketing.results.table.marketing.event.batches': '营销事件批次',
  'marketing.results.table.number.customers': '客户总数',
  'marketing.results.table.delivery.rate': '送达率',
  'marketing.results.table.read.rate': '已读率',
  'marketing.results.table.click.rate': '点击率',
  'marketing.results.table.subscription.rate': '订阅率',
  'marketing.results.table.unsubscribe.rate': '退订率',
  'marketing.results.table.complaint.rate': '投诉率',
  'marketing.results.table.complaint.rate1': '投诉率',
  'marketing.results.table.failure.rate': '失败率',

  'marketing.results.table.bounce.rate': '退信率',
  'marketing.results.table.delivery.delay.rate': '延迟送达率',
  'marketing.results.table.reject.rate': '拒绝率',
  'marketing.results.table.rendering.failure.rate': '呈现失败率',

  'marketing.results.table.operation': '操作',
  'marketing.results.table.export.customer': '导出客户清单',
  'marketing.results.table.customer.list': '客户清单',
  'marketing.results.table.marketing.event.sending.time': '营销事件发送时间',

  // 营销详情
  'marketing.results.marketing.event.batches': '营销事件批次：',
  'marketing.results.marketing.event.batches.placeholder': '请选择营销事件批次',
  'marketing.results.marketing.methods': '营销方式：',
  'marketing.results.marketing.methods.1': '标准测试',
  'marketing.results.marketing.methods.2': 'A/B测试',
  'marketing.results.marketing.methods.3': '计划A',
  'marketing.results.marketing.methods.4': '计划B',
  'marketing.results.marketing.methods.placeholder': '请选择营销方式',
  'marketing.results.contact.information': '客户联系方式：',
  'marketing.results.contact.information.placeholder': '请输入客户联系方式',
  'marketing.results.table.customer.name': '客户名称',
  'marketing.results.table.customer.contact.information': '客户联系方式',
  'marketing.results.table.status': '状态',
  'marketing.results.table.failure.reason': '失败原因',
  'marketing.results.table.detail': '详情',
  'event.notification.status.service': '已送达',
  'event.notification.status.read': '已读',
  'event.notification.status.click': '已点击',
  'event.notification.status.subscribe': '订阅',
  'event.notification.status.unsubscribe': '退订',
  'event.notification.status.fail': '呈现失败',
  'event.notification.status.complaint': '投诉',
  'event.notification.status.have.send': '已发送',
  'event.notification.status.bounce': '退信',
  'event.notification.status.reject': '拒绝',
  'event.notification.status.delivery.delay': '延迟送达',

  'marketing.details.customer.information.title': '客户基本信息',
  'marketing.details.activity.information.title': '活动基本信息',
  'marketing.details.customer.information.customer.name': '客户名称：',
  'marketing.details.customer.information.customer.phone': '客户手机：',
  'marketing.details.customer.information.customer.whats.app': 'WhatsApp号码：',
  'marketing.details.customer.information.customer.email': '客户邮箱：',
  'marketing.details.customer.information.marketing.result': '本次营销结果：',
  'marketing.details.history.title': '历史记录',

  'marketing.channel.type.email': '邮件',
  'marketing.channel.type.all': '所有渠道',
  'marketing.channel.type.all.small': '所有渠道',
  'marketing.channel.type.phone': '电话',
  'marketing.channel.type.whats.app': 'WhatsApp',
  'marketing.channel.type.info': '短信',
  'marketing.channel.type.chat': 'Web在线聊天',
  'marketing.channel.type.app.chat': 'App在线聊天',
  'marketing.channel.type.web.video': 'Web在线视频',
  'marketing.channel.type.app.video': 'App在线视频',
  'marketing.channel.type.amazon.message': 'Amazon Message',
  'marketing.channel.type.facebook': 'Facebook Messenger',
  'marketing.channel.type.instagram': 'Instagram',
  'marketing.channel.type.line': 'Line',
  'marketing.channel.type.weCom': '微信客服',
  'marketing.channel.type.weChat.official.account': '微信公众号',
  'marketing.channel.type.web.online.video': 'WEB在线语音',
  'marketing.channel.type.app.online.video': 'APP在线语音',
  'marketing.channel.type.twitter': 'Twitter',
  'marketing.channel.type.telegram': 'Telegram',
  'marketing.channel.type.weChat.mini.program': '微信小程序',
  'marketing.channel.type.shopify': 'Shopify',
  'marketing.channel.type.google.play': 'Google Play',
  'marketing.channel.type.discord': 'Discord',

  // A/B测试分析页面
  'test.analysis.result.title': '分析结果',
  'test.analysis.result.comparison.dimension': '对比维度：',
  'test.analysis.result.comparison.dimension.1': '客户群体',
  'test.analysis.result.comparison.dimension.2': '营销内容',
  'test.analysis.result.comparison.dimension.3': '营销时间',
  'test.analysis.result.overall.result': '整体结果：',
  'marketing.results.table.delivery.rate.1': '送达率：',
  'marketing.results.table.read.rate.1': '已读率：',
  'marketing.results.table.click.rate.1': '点击率：',
  'marketing.results.table.subscription.rate.1': '订阅率：',
  'marketing.results.table.unsubscribe.rate.1': '退订率：',
  'marketing.results.table.complaint.rate.1': '投诉率：',
  'marketing.results.table.failure.rate.1': '失败率：',

  'marketing.results.table.bounce.rate.1': '退信率：',
  'marketing.results.table.delivery.delay.rate.1': '延迟送达率：',
  'marketing.results.table.reject.rate.1': '拒绝率：',
  'marketing.results.table.rendering.failure.rate.1': '呈现失败率：',

  'test.analysis.semicolon': '；',
  'test.analysis.period': '。',

  'marketing.channel.type.discord': 'Discord',
};
