export default {
  'help.document.title': '帮助文档',
  'help.document.whats.app.title': '如何注册WhatsApp Business Account',
  'help.document.line.title': '如何注册Line渠道',
  'help.document.we.chat.official.title': '如何配置微信公众号渠道信息',

  // line帮助文档
  'help.document.line.left.menu': '创建Line官方账号',
  'help.document.line.left.menu.1': '获取Line渠道配置信息',
  'help.document.line.left.menu.2': '将Line集成到ConnectNow',
  'help.document.line.step.1.title': '步骤一：',
  'help.document.line.step.2.title': '步骤二：',
  'help.document.line.step.3.title': '步骤三：',
  'help.document.line.step.4.title': '步骤四：',
  'help.document.line.step.5.title': '步骤五：',
  'help.document.line.step.6.title': '步骤六：',
  'help.document.line.step.7.title': '步骤七：',
  'help.document.line.step.9.title': '步骤八：',
  'help.document.line.step.10.title': '步骤九：',
  'help.document.line.step.1.text.1':
    '打开链接： <a>https://tw.linebiz.com/account/</a>，点击「免费开设帐号」',
  'help.document.line.step.1.text.2': '选择使用line账号 或 商用账号 登录',
  'help.document.line.step.1.text.3':
    '若不确定要使用哪一项登入，可先详阅LINE官网上的说明：<a>「使用LINE帐号登入」与「使用商用帐号登入」有何差别？」</a>',
  'help.document.line.step.1.text.4':
    '登入后，填上 LINE官方帐号的资料，查看「LINE官方帐号服务条款」后按下「确定」',
  'help.document.line.step.1.text.5': '确认输入内容无误后，按下「Submit」',
  'help.document.line.step.1.text.6':
    '查看到以下画面，即表示 LINE 官方帐号申请成功',
  'help.document.line.step.2.text':
    '已有Line官方账号，点击以下 <a>LINE Official Account Manager</a>来登入',
  'help.document.line.step.2.text.1':
    '登入 “LINE Official Account Manager” 后，先选择要进行整合的 LINE 官方帐号：',
  'help.document.line.step.2.text.2': '点击「设定」',
  'help.document.line.step.2.text.3':
    '在左侧点选「Messaging API」，接着在 Messaging API 这页，点击「启用 Messaging API」',
  'help.document.line.step.2.text.4':
    '点击「启用 Messaging API」之后，首先需要选择已经建立的服务提供者，或是建立新的服务提供者（Provider），选择后请先详阅「LINE 官方帐号 API 服务条款」，再按下同意。',
  'help.document.line.step.2.text.5':
    '接着，填写「隐私权政策及服务条款的网址（选填）。若无填写亦可按下「确定」来进入下一个步骤',
  'help.document.line.step.2.text.6':
    '请确认「帐号名称」和「服务提供者名称」无误后，按下「确定」即正式启用Messaging API',
  'help.document.line.step.2.text.7':
    '按下「确定」之后就会出现以下画面，并可看到状态是「使用中」',
  'help.document.line.step.2.text.8':
    '接下来，您可以点击「LINE Developers 」来取得更多整合需要用到的资料！',
  'help.document.line.step.2.text.9':
    '为了整合 LINE 至 ConnectNow 后台，会需要复制以下 5 样资料：',
  'help.document.line.step.2.text.10': '- App Name',
  'help.document.line.step.2.text.11': '- Channel ID',
  'help.document.line.step.2.text.12': '- Channel secret',
  'help.document.line.step.2.text.13': '- Channel access token (long lived)',
  'help.document.line.step.2.text.14': '- LINE 官方帐号 ID',
  'help.document.line.step.2.text.15':
    '请在 Messaging API 页面，点击进入 LINE Developers',
  'help.document.line.step.2.text.16':
    '提示：Channel ID 和 Channel secret 会在 Messaging API 页面显示，亦可在此页复制这两项资讯',
  'help.document.line.step.2.text.17':
    '进入 LINE Developers 后， 先点击右上方的头像，选择 LINE 帐号',
  'help.document.line.step.2.text.18':
    '从左侧的 Admin 选择 Provider（服务提供者）',
  'help.document.line.step.2.text.19':
    '接着点选 Channel（官方帐号），进入设定页面',
  'help.document.line.step.2.text.20':
    '进入设定页面后，分别在「Basic Settings」和「Messaging API 」两个分页里，取得串接需要的资料',
  'help.document.line.step.2.text.21':
    '第一项～第三项可在「Basic Settings」里取得',
  'help.document.line.step.2.text.22': '切换到 Messaging API 分页',
  'help.document.line.step.2.text.23':
    '往下滑动页面，点击issue按钮获得 Channel access token (long lived)',
  'help.document.line.step.2.text.24':
    '点击后可以看到Channel access token (long-lived)信息',
  'help.document.line.step.2.text.25': '如何取得 LINE 官方帐号 ID',
  'help.document.line.step.2.text.26':
    '进入 LINE 官方帐号后台: <a>https://manager.line.biz</a>',
  'help.document.line.step.2.text.27':
    '复制下图红色标识处的文字即为 LINE 官方帐号的 LINE ID (不包含 @)',
  'help.document.line.step.3.text':
    '使用管理员账号登录ConnectNow平台，点击 渠道配置->line->添加',
  'help.document.line.step.3.text.1': '点击添加渠道',
  'help.document.line.step.3.text.2':
    '输入渠道名称后 点击下一步，会出现以下页面，请将刚刚拿到的5个资料粘贴到相应的数据框。',
  'help.document.line.step.3.text.3':
    '输入完成后，点击下一步进行机器人的相关设置',
  'help.document.line.step.3.text.4': '获取webhook地址',
  'help.document.line.step.3.text.5':
    '返回到 LINE Developers 的 Message API 分頁',
  'help.document.line.step.3.text.6':
    '找到 Webhook settings ，并在 Webhook URL 这里点击 "Edit" ',
  'help.document.line.step.3.text.7':
    '粘贴上刚刚在ConnectNow 后台复制的 LINE Webhook 网址，点击 "Update"',
  'help.document.line.step.3.text.8': '按 "Verify"，确保你看见 "Success"，',
  'help.document.line.step.3.text.9':
    '注意：请确保在ConnectNow平台点击完成后再进行"Verify"，否则会导致失败。',
  'help.document.line.step.3.text.10': '打开 Use Webhook 开关',
  'help.document.line.step.3.text.11':
    '如果无法在 LINE Developers 开启 Use Webhook，可以到 LINE OA 后台的「回应设定」里调整，在 Webhook 的地方勾选「启用」',
  'help.document.line.step.3.text.12':
    '若您不会在LINE OA原生后台，只会在ConnectNow后台回复讯息，请在LINE OA后台做以下设定 ：',
  'help.document.line.step.3.text.13': '- 关闭「聊天开关」；启用 Webhook',
  'help.document.line.step.3.text.14':
    '若您有需要在 LINE OA 后台回复客人讯息，请务必在 LINE OA「回应设定」做以下设定：',
  'help.document.line.step.3.text.15': '-  开启「聊天开关」；',
  'help.document.line.step.3.text.16': '- 启用Webhook',
  'help.document.line.step.3.text.17':
    '- 关闭「加入好友的欢迎讯息（避免同时触发 LINE OA 与 ConnectNow 两边的欢迎讯息功能）',
  'help.document.line.step.3.text.18':
    '- 关闭「回应时间」（避免同时触发 LINE OA 与 ConnectNow 两边的离线即时讯息功能）',
  'help.document.line.step.3.text.19':
    '- 「聊天的回应方式」请务必选择「手动」（避免同时触发 LINE OA 与 ConnectNow 两边的机器人回应、关键字自动回复、欢迎讯息）',
  'help.document.line.step.3.text.20':
    '完成！你的 LINE 已成功集成到ConnectNow了',

  // 微信公众号帮助文档
  'help.document.we.chat.official.left.menu': '登录微信公众平台',
  'help.document.we.chat.official.left.menu.1': '获取微信公众号ID',
  'help.document.we.chat.official.left.menu.2': 'ConnectNow渠道配置',
  'help.document.we.chat.official.step.1.text':
    '登录微信公众平台 <a>去往公众平台</a>',
  'help.document.we.chat.official.step.1.text.1':
    '打开手机端的微信，点击右上角+，选择扫一扫,扫描二维码后选择要登录的组织，公众平台会自动跳转',
  'help.document.we.chat.official.step.2.text':
    '获取公众号id ，选择左侧菜单中的设置与开发->公众号设置，向下滑动找到原始id',
  'help.document.we.chat.official.step.2.text.1':
    '获取APP ID，App Secret，选择左侧菜单中的设置与开发->基本配置',
  'help.document.we.chat.official.step.3.text':
    '通过ConnectNow渠道配置，获取参数',
  'help.document.we.chat.official.step.3.text.1':
    '将获取到的配置填写到公众平台，点击修改配置',
  'help.document.we.chat.official.step.3.text.2':
    '将ConnectNow渠道配置中第4步取到的参数配置到对应参数中',
  'help.document.we.chat.official.step.3.text.3': '',

  // WhatsApp帮助文档
  'help.document.whats.app.left.menu': '开始前需要准备的资料',
  'help.document.whats.app.left.menu.1': '创建新账号',
  'help.document.whats.app.left.menu.18': '点击创建入口',
  'help.document.whats.app.left.menu.2': '登录Facebook',
  'help.document.whats.app.left.menu.3': '确认协议',
  'help.document.whats.app.left.menu.4': '创建BM账号',
  'help.document.whats.app.left.menu.5': '创建WABA',
  'help.document.whats.app.left.menu.6': '设置WABA和号码信息',
  'help.document.whats.app.left.menu.7': '绑定手机号码',
  'help.document.whats.app.left.menu.8': '验证码校验',
  'help.document.whats.app.left.menu.9': '二次确认',
  'help.document.whats.app.left.menu.10': '创建成功',
  'help.document.whats.app.left.menu.11': '绑定成功',
  'help.document.whats.app.left.menu.12': '完成渠道添加',
  'help.document.whats.app.left.menu.13': '企业认证',
  'help.document.whats.app.left.menu.14': '业务验证准备',
  'help.document.whats.app.left.menu.15': '上传证明文件',
  'help.document.whats.app.left.menu.16': '选择联系方式',
  'help.document.whats.app.left.menu.17': '等待验证结果',
  'help.document.whats.app.step.1.table.title': '准备材料',
  'help.document.whats.app.step.1.table.title.1': '示例',
  'help.document.whats.app.step.1.table.title.2': '具体要求',
  'help.document.whats.app.step.1.table.body': 'Facebook个人账号',
  'help.document.whats.app.step.1.table.body.1': '-',
  'help.document.whats.app.step.1.table.body.2':
    '注册满一个月的老账号（在开始之前可点击链接检查下账号是否正常），用于创建Meta business manager（简称BM）',
  'help.document.whats.app.step.1.table.body.3': '手机号码',
  'help.document.whats.app.step.1.table.body.4': '+1 ***********',
  'help.document.whats.app.step.1.table.body.5':
    '需要能接收验证码短信，该号码之前没有注册过WhatsApp App或者商业账户（如该号码此前注册过个人WhatsApp，需注销个人WhatsApp账户后再使用），用于创建WhatsApp企业账号。',
  'help.document.whats.app.step.1.table.body.5.1': '备注：中国大陆手机号也可以',
  'help.document.whats.app.step.1.table.body.6':
    'WhatsApp business display name',
  'help.document.whats.app.step.1.table.body.7': 'ConnectNow',
  'help.document.whats.app.step.1.table.body.8':
    '需要与品牌官网有相关性，用于为WhatsApp 企业账号命名',
  'help.document.whats.app.step.1.table.body.9': '企业名称',
  'help.document.whats.app.step.1.table.body.10': 'ConnectNow',
  'help.document.whats.app.step.1.table.body.11':
    '需和营业执照或者登记证书上的公司名称完全一致',
  'help.document.whats.app.step.1.table.body.12': '企业地址',
  'help.document.whats.app.step.1.table.body.13':
    '7500A BEACH ROAD #04-307 THE PLAZA SINGAPORE 19959',
  'help.document.whats.app.step.1.table.body.14':
    '需和营业执照或者登记证书上的地址完全一致',
  'help.document.whats.app.step.1.table.body.15': '营业执照或者登记证书',
  'help.document.whats.app.step.1.table.body.16': 'Business_Pofile.pdf',
  'help.document.whats.app.step.1.table.body.17':
    '企业对应的营业执照或注册文件',
  'help.document.whats.app.step.1.table.body.18': '品牌网址',
  'help.document.whats.app.step.1.table.body.19': 'www.connectnowai.com',
  'help.document.whats.app.step.1.table.body.20': '1.网址需要https加密；',
  'help.document.whats.app.step.1.table.body.20.1':
    '2.网址内容需明确表达公司业务；',
  'help.document.whats.app.step.1.table.body.20.2':
    '3.网址底部需要带有企业名称和地址，例如：COPYRIGHT @ XXXX (当年年份) +公司名称 All Rights Reserved',
  'help.document.whats.app.step.1.table.body.21': '企业邮箱地址',
  'help.document.whats.app.step.1.table.body.22': '<EMAIL>',
  'help.document.whats.app.step.1.table.body.23':
    '企业邮箱后缀需要和品牌网址域名保持一致 如：www.connectnowai.com和************************则符合要求，将用于企业认证时接收一次验证码邮件',
  'help.document.whats.app.step.1.text':
    '注：如果您已经有BM账号 (Facebook business manager)，希望在已有的BM账号下创建WhatsApp API 账号，可在以下流程的第2步骤中登录 该BM账号管理员的Facebook账号，在第4步骤中选择已有的BM账号。',
  'help.document.whats.app.step.2.text':
    '准备好以上材料后，您就可以通过管理员登录ConnectNow，点击左侧菜单的渠道配置->WhatsApp->添加->绑定WhatsApp，进行嵌入式注册流程了',
  'help.document.whats.app.step.2.text.1':
    '点击“渠道配置”，向下滑动找到WhatsApp,点击“开始配置”',
  'help.document.whats.app.step.2.text.2':
    '进入“WhatsApp渠道配置”的界面，点击“添加渠道”',
  'help.document.whats.app.step.2.text.3':
    '进入“添加WhatsApp渠道”的页面，点击绑定WhatsApp账户，进入Facebook登录页面',
  'help.document.whats.app.step.2.text.4':
    '点击绑定“WhatsApp账号” 后会出现Facebook 授权弹窗，请登录您准备好的Facebook账号。若账号不正确，可点击Log in another account 进行切换。确认账号无误后点击 继续 进行下一步骤。',
  'help.document.whats.app.step.2.text.5': '确认无误后点击 开始  继续创建。',
  'help.document.whats.app.step.2.text.6': '输入您公司的资料：',
  'help.document.whats.app.step.2.text.7':
    '*公司名称：公司名称，使用跟营业执照上的名称完全一致，勿使用简称、品牌名称。',
  'help.document.whats.app.step.2.text.8':
    '*公司邮箱：建议使用跟网站域名相同的email地址。',
  'help.document.whats.app.step.2.text.9':
    '*公司网站或业务主页：公司网址。注意，公司的网址必须是https协议的。',
  'help.document.whats.app.step.2.text.10':
    '*国家/地区：公司的运营国家。请务必选择您打算认证的公司的归属国家。例如您做企业认证时，是提交印尼的营业执照，则选择国家为印尼。',
  'help.document.whats.app.step.2.text.11':
    '注：若已有BM账号，可在Business portfolio选项中选择。',
  'help.document.whats.app.step.2.text.12':
    '选择/创建新的WABA。已有WABA的情况下可以下拉框选择已有的WABA，否则选择 创建。',
  'help.document.whats.app.step.2.text.13':
    '*WhatsApp business业务账号名称：WABA的名称。内部区分业务使用，您的受众不会在您WhatsApp 账号资料上看到此信息。',
  'help.document.whats.app.step.2.text.14':
    '*WhatsApp显示名称：号码的名称。客户最终看到的名称，需要跟企业的名称相关，或者跟品牌名称相关。点击查看显示名称准则。',
  'help.document.whats.app.step.2.text.15': '*类别：行业',
  'help.document.whats.app.step.2.text.16':
    '输入注册WhatsApp API 账号的手机号码，选择获取验证码的方式：',
  'help.document.whats.app.step.2.text.17': '1. 短信',
  'help.document.whats.app.step.2.text.18': '2. 语音电话',
  'help.document.whats.app.step.2.text.19': '输入完成后点击下一步。',
  'help.document.whats.app.step.2.text.20':
    'Tip：中国大陆地区（+86）建议使用语音电话来接收验证码。',
  'help.document.whats.app.step.2.text.21': '输入收到的验证码并点击Next',
  'help.document.whats.app.step.2.text.22': '确认您的创建内容，无误后点击继续',
  'help.document.whats.app.step.2.text.23':
    '提示创建成功，请务必点击弹窗下方的 完成 按钮。',
  'help.document.whats.app.step.2.text.24':
    '当弹窗关闭，ConnectNow将进行绑定。',
  'help.document.whats.app.step.2.text.25': '选择号码',
  'help.document.whats.app.step.2.text.26': '设置机器人',
  'help.document.whats.app.step.2.text.27':
    '输入渠道名称点击完成后，就完成了WhatsApp Business Account的注册',
  'help.document.whats.app.step.3.text':
    '做好准备并验证您的 BM帐户，以便每天在 WhatsApp 上发送无限量的消息。',
  'help.document.whats.app.step.3.text.1':
    'BM企业认证是一个旨在验证 BM帐户是否属于真实组织的过程。',
  'help.document.whats.app.step.3.text.2':
    '如果您尚未完成 BM企业认证，您将在使用 WhatsApp Business API 时受到限制，包括：',
  'help.document.whats.app.step.3.text.3':
    '每个电话号码在 24 小时内滚动向 250 位唯一客户发送业务发起的对话。',
  'help.document.whats.app.step.3.text.4': '最多注册 2 个电话号码。',
  'help.document.whats.app.step.3.text.5':
    '完成企业验证和显示名称审核后，您的企业有机会可以快速打开限制：',
  'help.document.whats.app.step.3.text.6':
    '将业务发起的对话扩展到更多客户：从 24 小时滚动期间的 1,000 个唯一客户开始，逐渐增加到每个电话号码 10,000、100,000 或无限制。',
  'help.document.whats.app.step.3.text.7': '响应无限的客户发起的对话。',
  'help.document.whats.app.step.3.text.8': '请求成为官方企业帐户 (OBA)。',
  'help.document.whats.app.step.3.text.9':
    '注册额外的电话号码（每个 BM 最多 20 个）。',
  'help.document.whats.app.step.3.text.10':
    '自 2024 年 4 月起，在满足消息质量标准并完成显示名称审核后，企业可以在聊天中显示其名称，从而提高客户对其的信任度，而无需进行企业验证。请参考Meta文档：',
  'help.document.whats.app.step.3.text.11':
    'https://developers.facebook.com/docs/whatsapp/messaging-limits#open-1k-conversations-in-30-days',
  'help.document.whats.app.step.3.text.12':
    '为了增加公司通过 Meta 验证的机会，您需要提前确认好以下信息：',
  'help.document.whats.app.step.3.text.13':
    '公司的官方网站地址：且HTTPS加密，并包含公司的名称、地址或电话号码',
  'help.document.whats.app.step.3.text.14':
    '公司网址同一域名的邮箱地址：它将被用来接收一次验证邮件（通过域名认证或手机号认证的方式不需要，但一般情况下，我们建议通过邮箱认证）',
  'help.document.whats.app.step.3.text.15':
    '包含企业法定名称的官方文件：例如营业执照、公司章程或企业税务登记证明',
  'help.document.whats.app.step.3.text.16':
    '您需要确保文件中包含的公司名称与公司官方网站有相关性，例如，在页脚处输入“所属于公司 ABC”',
  'help.document.whats.app.step.3.text.17': '验证流程',
  'help.document.whats.app.step.3.text.17.1': '1. 验证流程',
  'help.document.whats.app.step.3.text.18':
    '如果您是 Meta Business Manager 帐户的管理员（按照第2 章节执行完成后，您将自动成为 Meta Business Manager 帐户的管理员），您可以按照以下步骤开始验证您的业务：',
  'help.document.whats.app.step.3.text.19':
    '转至商务管理平台的<a>“安全中心”</a>部分。',
  'help.document.whats.app.step.3.text.20':
    '如果您没有看到“验证”按钮，请访问 ConnectNow平台并完成嵌入式注册流程（即章节2）。',
  'help.document.whats.app.step.3.text.21': '2. 提交组织基础信息',
  'help.document.whats.app.step.3.text.22':
    '提供您的组织名称、地址、电话号码和网站',
  'help.document.whats.app.step.3.text.23': '最佳实践',
  'help.document.whats.app.step.3.text.24':
    '您填写的公司名称/地址必须与证明文件上的名称/地址一致。',
  'help.document.whats.app.step.3.text.25':
    '电话号码可以是个人手机号码（但后续环节中不可以采用手机号码验证公司）',
  'help.document.whats.app.step.3.text.26':
    '提交的网站必须有证明域名所有权的文字内容，例如，在页脚处输入“所属于公司 ABC”，该公司名称与您填写的公司名称必须一致。',
  'help.document.whats.app.step.3.text.27':
    '上传完成后安装页面信息进行提交验证',
  'help.document.whats.app.step.3.text.28': '最佳实践',
  'help.document.whats.app.step.3.text.29':
    '首选电子邮件验证，但您的电子邮件地址后缀必须与提交的域名匹配（www.mypage.com >> <EMAIL>）。',
  'help.document.whats.app.step.3.text.30':
    '域验证是验证您的业务的下一个选项。',
  'help.document.whats.app.step.3.text.31':
    '输入验证码后点击下一步直至最后一步提交验证',
  'help.document.whats.app.step.3.text.32':
    '提交验证后，最快 10 分钟、最长 14 个工作日即可做出决定。审核完成后，您会收到通知。如果您收到已通过验证的确认信息，则无需执行任何其他操作。',
};
