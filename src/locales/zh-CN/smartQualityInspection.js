export default {
  'smart.quality.evaluation.table.title': '智能质检评估表',
  'smart.quality.evaluation.table.add': '添加评估表',
  'smart.quality.evaluation.table.name': '评估表名称',
  'smart.quality.evaluation.rule.category': '规则分类',
  'smart.quality.evaluation.assessment.name': '评估表名称',
  'smart.quality.evaluation.table.channel': '渠道',
  'smart.quality.evaluation.table.channel.all': '所有渠道',
  'smart.quality.evaluation.table.ticket.type': '工单类型',
  'smart.quality.evaluation.table.status': '发布状态',
  'smart.quality.evaluation.table.status.published': '已发布',
  'smart.quality.evaluation.table.status.disabled': '未发布',
  'smart.quality.evaluation.table.rule.count': '评分规则数量',
  'smart.quality.evaluation.table.total.score': '总分',
  'smart.quality.evaluation.table.operation': '操作',
  'smart.quality.evaluation.table.enable': '启用',
  'smart.quality.evaluation.table.enable.success': '启用成功',
  'smart.quality.evaluation.table.disable': '禁用',
  'smart.quality.evaluation.table.disable.success': '禁用成功',
  'smart.quality.evaluation.table.edit': '修改',
  'smart.quality.evaluation.table.edit.rule': '修改规则',
  'smart.quality.evaluation.table.history': '评估历史记录',
  'smart.quality.evaluation.table.delete': '删除',
  'smart.quality.evaluation.table.delete.confirm': '是否删除该评估表？',
  'smart.quality.evaluation.table.delete.ok': '是',
  'smart.quality.evaluation.table.delete.cancel': '否',
  'smart.quality.evaluation.table.delete.success': '删除成功',
  'smart.quality.evaluation.list.page.total.num': '共 {total} 条',
  // ====== 添加评估表页面相关 ======
  'smart.quality.evaluation.add': '添加评估表',
  'smart.quality.evaluation.add.baseinfo': '基本信息',
  'smart.quality.evaluation.add.rule': '评分规则',
  'smart.quality.evaluation.add.permission': '权限设置',
  'smart.quality.evaluation.add.name': '评估表名称',
  'smart.quality.evaluation.add.name.placeholder': '请输入评估表名称',
  'smart.quality.evaluation.add.name.required': '请输入评估表名称',
  'smart.quality.evaluation.add.name.max': '长度不能超过80个字符',
  'smart.quality.evaluation.add.channel': '适用渠道',
  'smart.quality.evaluation.add.channel.placeholder': '请选择适用渠道',
  'smart.quality.evaluation.add.channel.required': '请选择适用渠道',
  'smart.quality.evaluation.add.ticket.type': '适用工单类型',
  'smart.quality.evaluation.add.ticket.type.placeholder': '请选择适用工单类型',
  'smart.quality.evaluation.add.ticket.type.required': '请选择适用工单类型',
  'smart.quality.evaluation.add.total.score': '满分',
  'smart.quality.evaluation.add.total.score.placeholder': '请输入满分',
  'smart.quality.evaluation.add.total.score.required': '请输入满分',
  'smart.quality.evaluation.add.score.mechanism': '评分机制',
  'smart.quality.evaluation.add.score.mechanism.add': '加分机制',
  'smart.quality.evaluation.add.score.mechanism.subtract': '减分机制',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.title': '加分机制',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.1':
    '绩效增值评估 (Performance Value-Add Assessment)',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.2':
    '从基础分起始，优秀表现加分',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.3':
    '强调超过标准的积极行为和额外价值',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.title':
    '减分机制',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.1':
    '合规基线评估 (Compliance Baseline Assessment)',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.2':
    '从满分起始，违规项目扣分',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.3':
    '强调维持基本服务标准和合规性',
  'smart.quality.evaluation.add.score.mechanism.required': '请选择评分机制',
  'smart.quality.evaluation.add.permission.scorer': '评分人',
  'smart.quality.evaluation.add.permission.scorer.tooltip':
    '此处定义谁有权限基于当前评估表评分',
  'smart.quality.evaluation.add.permission.scorer.required': '请选择评分人',
  'smart.quality.evaluation.add.permission.scorer.placeholder': '请选择评分人',
  'smart.quality.evaluation.add.cancel.confirm': '取消将清空表单，确定取消吗？',
  'smart.quality.common.cancel': '取消',
  'smart.quality.common.next': '下一步',
  'smart.quality.common.yes': '是',
  'smart.quality.common.no': '否',
  'smart.quality.evaluation.add.channel.all': '所有',
  'smart.quality.evaluation.add.ticket.type.all': '所有',
  // ====== END 添加评估表页面相关 ======

  // ====== 评估历史记录页面相关 ======
  'smart.quality.evaluation.history.title': '评估历史记录',
  'smart.quality.evaluation.history.name.placeholder': '请输入评估表名称',
  'smart.quality.evaluation.history.channel.placeholder': '请选择渠道',
  'smart.quality.evaluation.history.ticket.type.placeholder': '请选择工单类型',
  'smart.quality.evaluation.history.agent.name': '座席名称',
  'smart.quality.evaluation.history.agent.name.placeholder': '请选择坐席名称',
  'smart.quality.evaluation.history.ticket.id': '工单编号',
  'smart.quality.evaluation.history.ticket.id.placeholder': '请输入工单编号',
  'smart.quality.evaluation.history.score': '得分',
  'smart.quality.evaluation.history.evaluator': '评估人',
  'smart.quality.evaluation.history.evaluator.placeholder': '请选择评估人',
  'smart.quality.evaluation.history.score.range': '得分区间',
  'smart.quality.evaluation.history.score.min': '最小值',
  'smart.quality.evaluation.history.score.max': '最大值',
  'smart.quality.evaluation.history.score.min.error': '最小值不能大于最大值',
  'smart.quality.evaluation.history.score.max.error': '最大值不能小于最小值',
  'smart.quality.evaluation.history.score.range.error': '最大值不能小于最小值',
  'smart.quality.evaluation.history.score.both.required':
    '最大值和最小值必须同时填写',
  'smart.quality.evaluation.history.score.range.warning':
    '最小值不应大于最大值',
  'smart.quality.evaluation.history.score.format.error':
    '请输入有效的数值，最多支持2位小数',
  'smart.quality.evaluation.history.search': '搜索',
  'smart.quality.evaluation.history.details': '详情',
  'smart.quality.evaluation.history.export.pdf': '导出PDF',
  'smart.quality.evaluation.history.page.total.num': '共 {total} 条',
  'smart.quality.evaluation.history.evaluation.time': '评估时间',
  'smart.quality.evaluation.history.return.list': '返回列表',
  // ====== END 评估历史记录页面相关 ======

  'smart.quality.evaluation.rule.version.current': '最新版本',
  'smart.quality.evaluation.rule.version.current.tip': '请选择最新版本',
  'smart.quality.evaluation.rule.delete.category.tip.title': '提示',
  'smart.quality.evaluation.rule.delete.category.tip':
    '当前分类下已有规则，继续删除将同时删除该分类下所有规则',

  // start 规则列表
  'smart.quality.evaluation.rule.category': '规则分类',
  'smart.quality.evaluation.rule.title': '添加评估表',
  'smart.quality.evaluation.rule.edit.title': '编辑评估表',
  'smart.quality.evaluation.rule.deploy.status': '部署状态',
  'smart.quality.evaluation.rule.deploy.status.unpublished': '未发布',
  'smart.quality.evaluation.rule.deploy.status.published': '已发布',
  'smart.quality.evaluation.rule.button.cancel': '取消',
  'smart.quality.evaluation.rule.button.save': '保存',
  'smart.quality.evaluation.rule.button.save.and.publish': '保存并发布',
  'smart.quality.evaluation.rule.search.placeholder':
    '输入规则名称或规则简要说明，回车查询',
  'smart.quality.evaluation.rule.add': '添加规则',
  'smart.quality.evaluation.rule.table.category': '规则分类',
  'smart.quality.evaluation.rule.table.name': '规则名称',
  'smart.quality.evaluation.rule.table.description': '规则简要说明',
  'smart.quality.evaluation.rule.table.score': '得分规则',
  'smart.quality.evaluation.rule.table.evaluation.method': '评分方式',
  'smart.quality.evaluation.rule.table.evaluation.method.manual': '人工评分',
  'smart.quality.evaluation.rule.table.evaluation.method.ai': 'AIGC评分',
  'smart.quality.evaluation.rule.table.operation': '操作',
  'smart.quality.evaluation.rule.table.operation.edit': '修改',
  'smart.quality.evaluation.rule.table.delete.confirm': '是否删除该规则？',
  'smart.quality.evaluation.rule.table.delete.ok': '是',
  'smart.quality.evaluation.rule.table.delete.cancel': '否',
  'smart.quality.evaluation.rule.table.operation.delete': '删除',
  'smart.quality.evaluation.rule.table.operation.detail': '详情',
  'smart.quality.evaluation.rule.new.top.level': '新建类目',
  'smart.quality.evaluation.rule.new.category': '新建类目',
  'smart.quality.evaluation.rule.page.total.num': '共 {total} 条',
  'smart.quality.evaluation.rule.add.score.add': '加',
  'smart.quality.evaluation.rule.add.score.subtract': '减',
  // end 规则列表

  // start 添加规则页面
  'smart.quality.evaluation.rule.add.title': '添加规则',
  'smart.quality.evaluation.rule.edit.title': '修改规则',
  'smart.quality.evaluation.rule.detail.title': '规则详情',
  'smart.quality.evaluation.rule.add.baseinfo': '基本信息',
  'smart.quality.evaluation.rule.add.baseinfo.name': '规则名称',
  'smart.quality.evaluation.rule.add.baseinfo.name.placeholder':
    '请输入规则名称',
  'smart.quality.evaluation.rule.add.baseinfo.name.required': '请输入规则名称',
  'smart.quality.evaluation.rule.name': '规则名称',
  'smart.quality.evaluation.rule.category.required': '请选择规则分类',
  'smart.quality.evaluation.rule.name.placeholder': '请输入规则名称',
  'smart.quality.evaluation.rule.name.required': '请输入规则名称',
  'smart.quality.evaluation.rule.name.max': '长度不能超过80个字符',
  'smart.quality.evaluation.rule.description': '规则简要说明',
  'smart.quality.evaluation.rule.description.placeholder': '请输入规则简要说明',
  'smart.quality.evaluation.rule.description.required': '请输入规则简要说明',
  'smart.quality.evaluation.rule.description.max': '长度不能超过200个字符',
  'smart.quality.evaluation.rule.category.placeholder': '请选择规则分类',
  'smart.quality.evaluation.rule.category.required': '请选择规则分类',
  'smart.quality.evaluation.rule.evaluation.method': '评分方式',
  'smart.quality.evaluation.rule.evaluation.method.ai': 'AIGC评分',
  'smart.quality.evaluation.rule.evaluation.method.manual': '人工评分',
  'smart.quality.evaluation.rule.ai.settings': 'AIGC评分规则',
  'smart.quality.evaluation.rule.manual.settings': '人工评分规则',
  'smart.quality.evaluation.rule.total.score': '规则总分',
  'smart.quality.evaluation.rule.total.score.placeholder': '请输入规则总分',
  'smart.quality.evaluation.rule.total.score.required': '请输入规则总分',
  'smart.quality.evaluation.rule.total.score.exceed.error':
    '规则总分不能大于评估总分',
  'smart.quality.evaluation.rule.total.score.exceed.error.add':
    '规则总加分不能大于评估总分',
  'smart.quality.evaluation.rule.total.score.exceed.error.subtract':
    '规则总扣分不能大于评估总分',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error':
    'AIGC得分规则得分不能大于规则总分',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.add':
    'AIGC得分规则加分不能大于规则总分',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.subtract':
    'AIGC得分规则扣分不能大于规则总分',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error':
    'AIGC得分规则得分不能大于规则总分',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.add':
    'AIGC得分规则加分不能大于规则总分',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.subtract':
    'AIGC得分规则扣分不能大于规则总分',
  'smart.quality.evaluation.rule.manual.option.required':
    '请至少添加一条评分规则',
  'smart.quality.evaluation.rule.manual.option.save.first':
    '请先保存正在编辑的规则',
  'smart.quality.evaluation.rule.check.points': '质检点',
  'smart.quality.evaluation.rule.check.point.placeholder':
    '请输入详细的质检点描述',
  'smart.quality.evaluation.rule.check.point.required': '请输入质检点',
  'smart.quality.evaluation.rule.add.check.point': '添加质检点',
  'smart.quality.evaluation.rule.check.points.max': '最多可添加15个质检点',
  'smart.quality.evaluation.add.rule.button.return': '返回',
  'smart.quality.evaluation.add.rule.button.save': '保存',
  // end 添加规则页面

  // start 人工评分规则相关
  'smart.quality.evaluation.add.rule.manual.option.name': '选项名称',
  'smart.quality.evaluation.add.rule.manual.option.name.required':
    '请输入选项名称',
  'smart.quality.evaluation.add.rule.manual.option.name.placeholder':
    '请输入选项名称',
  'smart.quality.evaluation.add.rule.manual.option.name.duplicate':
    '选项名称不能重复',
  'smart.quality.evaluation.add.rule.manual.option.score': '得分',
  'smart.quality.evaluation.add.rule.manual.option.score.required':
    '请输入得分',
  'smart.quality.evaluation.add.rule.manual.option.operation': '操作',
  'smart.quality.evaluation.add.rule.manual.rules': '人工评分规则',
  'smart.quality.evaluation.add.rule.manual.add.option': '添加选项',
  'smart.quality.evaluation.add.rule.manual.standard': '评估标准参考',
  'smart.quality.evaluation.add.rule.manual.standard.required':
    '请输入评估标准参考',
  'smart.quality.evaluation.add.rule.manual.standard.placeholder':
    '请输入评估标准参考',
  // end 人工评分规则相关

  // start AIGC评分规则相关
  'smart.quality.evaluation.add.rule.aigc.rules': 'AIGC得分规则',
  'smart.quality.evaluation.add.rule.aigc.rules.required': '请选择AIGC得分规则',
  'smart.quality.evaluation.add.rule.aigc.rule1.title': '出现任意一个质检点',
  'smart.quality.evaluation.add.rule.aigc.rule1.prefix': '，每出现一次',

  'smart.quality.evaluation.add.rule.aigc.rule1.score.required': '请输入得分',
  'smart.quality.evaluation.add.rule.aigc.rule1.middle': '分，累计最多',
  'smart.quality.evaluation.add.rule.aigc.rule1.max.score.required':
    '请输入累计最多得分',
  'smart.quality.evaluation.add.rule.aigc.rule1.suffix': '分',
  'smart.quality.evaluation.add.rule.aigc.rule2.title': '出现多个质检点',
  'smart.quality.evaluation.add.rule.aigc.rule2.prefix': '，当出现次数',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.required':
    '请选择出现次数',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.gt': '大于',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.eq': '等于',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.lt': '小于',
  'smart.quality.evaluation.add.rule.aigc.rule2.threshold.required':
    '请输入次数',
  'smart.quality.evaluation.add.rule.aigc.rule2.middle': '次，',
  'smart.quality.evaluation.add.rule.aigc.rule2.score.required': '请输入得分',
  'smart.quality.evaluation.add.rule.aigc.rule2.suffix': '分',
  // end AIGC评分规则相关
};
