export default {
  'help.document.title': 'ヘルプドキュメント',
  'help.document.whats.app.title': 'Line business accountの登録方法',
  'help.document.line.title': 'Lineチャネルの登録方法',
  'help.document.we.chat.official.title':
    'Wechat公式アカウントチャネル情報の構成方法',

  // line帮助文档
  'help.document.line.left.menu': 'Line公式アカウントを作成する',
  'help.document.line.left.menu.1': 'Lineチャネル構成情報を取得する',
  'help.document.line.left.menu.2': 'Lineをconnectnowに統合する',
  'help.document.line.step.1.title': 'ステップ1：',
  'help.document.line.step.2.title': 'ステップ2：',
  'help.document.line.step.3.title': 'ステップ3：',
  'help.document.line.step.4.title': 'ステップ4：',
  'help.document.line.step.5.title': 'ステップ5：',
  'help.document.line.step.6.title': 'ステップ6：',
  'help.document.line.step.7.title': 'ステップ7：',
  'help.document.line.step.9.title': 'ステップ8：',
  'help.document.line.step.10.title': 'ステップ9：',
  'help.document.line.step.1.text.1':
    'リンクを開き：<a>https://tw.linebiz.com/account/</a>、「無料アカウントを開設」をクリックします',
  'help.document.line.step.1.text.2':
    'Lineアカウントまたはビジネスアカウントでログインを選択します',
  'help.document.line.step.1.text.3':
    'どちらを使用するか不明な場合は、line公式サイトの説明を参照してください：<a>「lineアカウントでログイン」と「ビジネスアカウントでログイン」の違いは何ですか？</a>',
  'help.document.line.step.1.text.4':
    'ログイン後、line公式アカウントの情報を入力し、「line公式アカウントサービス利用規約」を確認後、「ok」をクリックします',
  'help.document.line.step.1.text.5':
    '入力内容に間違いがないことを確認したら、「submit」をクリックします',
  'help.document.line.step.1.text.6':
    '以下のような画面が表示された場合、line公式アカウントの申請が成功したことを意味します',
  'help.document.line.step.2.text':
    'すでにline公式アカウントをお持ちの場合は、以下の<a>line official account manager</a>をクリックしてログインしてください',
  'help.document.line.step.2.text.1':
    '「line official account manager」にログイン後、まず統合するline公式アカウントを選択してください：',
  'help.document.line.step.2.text.2': '「設定」をクリックします',
  'help.document.line.step.2.text.3':
    '左側で「messaging API」を選択し、次にmessaging APIページで「messaging APIを有効にする」をクリックします',
  'help.document.line.step.2.text.4':
    '「messaging APIを有効にする」をクリックした後、まず既存のサービスプロバイダーを選択するか、新しいサービスプロバイダー（provider）を作成する必要があります。選択後、「line公式アカウントAPIサービス利用規約」をよく読み、同意してください。',
  'help.document.line.step.2.text.5':
    '次に、「プライバシーポリシーおよびサービス利用規約のURL（オプション）」を記入します。記入しなくても「ok」をクリックして次のステップに進むことができます。',
  'help.document.line.step.2.text.6':
    '「アカウント名」と「サービスプロバイダー名」に間違いがないことを確認した後、「ok」をクリックするとmessaging APIが正式に有効になります',
  'help.document.line.step.2.text.7':
    '「ok」をクリックすると以下の画面が表示され、ステータスが「使用中」になります',
  'help.document.line.step.2.text.8':
    '接下来、line developersをクリックして、統合に必要なその他の情報を取得できます！',
  'help.document.line.step.2.text.9':
    'LineをConnectNowのバックエンドに統合するためには、以下の5つの情報をコピーする必要があります：',
  'help.document.line.step.2.text.10': '- App name',
  'help.document.line.step.2.text.11': '- Channel id',
  'help.document.line.step.2.text.12': '- Channel secret',
  'help.document.line.step.2.text.13': '- Channel access token (long lived)',
  'help.document.line.step.2.text.14': '- Line公式アカウントid',
  'help.document.line.step.2.text.15':
    'Messaging APIページで、line developersをクリックしてください',
  'help.document.line.step.2.text.16':
    'ヒント：channel idとchannel secretはmessaging APIページに表示され、このページからこれら2つの情報をコピーすることもできます',
  'help.document.line.step.2.text.17':
    'Line developersに入ったら、まず右上のアバターをクリックしてlineアカウントを選択します',
  'help.document.line.step.2.text.18':
    '左側のadminからprovider（サービスプロバイダー）を選択します',
  'help.document.line.step.2.text.19':
    '次にchannel（公式アカウント）をクリックして設定ページに入ります',
  'help.document.line.step.2.text.20':
    '設定ページに入ったら、「basic settings」と「messaging API」の2つのタブで、接続に必要なデータをそれぞれ取得します',
  'help.document.line.step.2.text.21':
    '最初の3つの項目は「basic settings」で取得できます',
  'help.document.line.step.2.text.22': 'Messaging APIタブに切り替えます',
  'help.document.line.step.2.text.23':
    'ページを下にスクロールし、「issue」ボタンをクリックしてchannel access token (long lived)を取得します',
  'help.document.line.step.2.text.24':
    'クリックするとchannel access token (long-lived)情報が表示されます',
  'help.document.line.step.2.text.25': 'Line公式アカウントidの取得方法',
  'help.document.line.step.2.text.26':
    'Line公式アカウント管理画面にアクセスします: <a>https://manager.line.biz</a>',
  'help.document.line.step.2.text.27':
    '以下の図の赤で示されたテキストをコピーすると、line公式アカウントのline idになります（@を含まない）',
  'help.document.line.step.3.text':
    '管理者アカウントでconnectnowプラットフォームにログインし、「チャネル設定」->「line」->「追加」をクリックします',
  'help.document.line.step.3.text.1': '「チャネルを追加」をクリックします',
  'help.document.line.step.3.text.2':
    'チャネル名を入力後「次へ」をクリックすると以下のページが表示されます。先ほど取得した5つの情報を対応するデータボックスに貼り付けてください。',
  'help.document.line.step.3.text.3':
    '入力が完了したら、「次へ」をクリックしてボット関連の設定を行います',
  'help.document.line.step.3.text.4': 'Webhook urlを取得する',
  'help.document.line.step.3.text.5':
    'Line developersのmessaging APIタブに戻ります',
  'help.document.line.step.3.text.6':
    'Webhook settingsを見つけ、webhook urlの「edit」をクリックします',
  'help.document.line.step.3.text.7':
    'Connectnowバックエンドでコピーしたline webhook urlを貼り付け、「update」をクリックします',
  'help.document.line.step.3.text.8':
    '「Verify」を押すと、「Success」が見えることを確認します。',
  'help.document.line.step.3.text.9':
    '注意：connectnowプラットフォームで完了をクリックしてから「verify」を行うようにしてください。そうしないと失敗します。',
  'help.document.line.step.3.text.10': 'Use webhookスイッチをオンにします',
  'help.document.line.step.3.text.11':
    'Line developersでuse webhookを有効にできない場合は、line OAのバックエンドの「応答設定」で調整し、webhookの場所で「有効にする」にチェックを入れることができます',
  'help.document.line.step.3.text.12':
    'Line OAのネイティブバックエンドではなく、ConnectNowバックエンドでのみメッセージを返信する場合、line OAバックエンドで以下の設定を行ってください：',
  'help.document.line.step.3.text.13':
    '- 「チャットスイッチ」をオフにする；webhookを有効にする',
  'help.document.line.step.3.text.14':
    'Line OAのバックエンドで顧客メッセージに返信する必要がある場合は、line OAの「応答設定」で以下の設定を必ず行ってください：',
  'help.document.line.step.3.text.15': '- 「チャットスイッチ」をオンにする；',
  'help.document.line.step.3.text.16': '- Webhookを有効にする',
  'help.document.line.step.3.text.17':
    '- 「友だち追加の歓迎メッセージ」をオフにする（line OAとConnectNowの両方で同時に歓迎メッセージ機能がトリガーされるのを避けるため）',
  'help.document.line.step.3.text.18':
    '- 「応答時間」をオフにする（line OAとConnectNowの両方で同時にオフラインインスタントメッセージ機能がトリガーされるのを避けるため）',
  'help.document.line.step.3.text.19':
    '- 「チャットの応答方法」は必ず「手動」を選択してください（line OAとConnectNowの両方で同時にボット応答、キーワード自動応答、歓迎メッセージがトリガーされるのを避けるため）',
  'help.document.line.step.3.text.20':
    '完了！lineはconnectnowに正常に統合されました',

  // 微信公众号帮助文档
  'help.document.we.chat.official.left.menu':
    'Wechat公式プラットフォームにログインする',
  'help.document.we.chat.official.left.menu.1':
    'Wechat公式アカウントidを取得する',
  'help.document.we.chat.official.left.menu.2': 'Connectnowチャネル設定',
  'help.document.we.chat.official.step.1.text':
    'Wechat公式プラットフォームにログインします <a>公式プラットフォームへ</a>',
  'help.document.we.chat.official.step.1.text.1':
    '携帯電話のwechatを開き、右上の「+」をクリックし、「スキャン」を選択します。QRコードをスキャンした後、ログインする組織を選択すると、公式プラットフォームが自動的にリダイレクトされます',
  'help.document.we.chat.official.step.2.text':
    '公式アカウントidを取得するには、左側メニューの「設定と開発」->「公式アカウント設定」を選択し、下にスクロールして「オリジナルid」を見つけます',
  'help.document.we.chat.official.step.2.text.1':
    'App ID、app secretを取得するには、左側メニューの「設定と開発」->「基本設定」を選択します',
  'help.document.we.chat.official.step.3.text':
    'Connectnowチャネル設定を通して、パラメーターを取得します',
  'help.document.we.chat.official.step.3.text.1':
    '取得した設定を公式プラットフォームに記入し、「設定を修正」をクリックします',
  'help.document.we.chat.official.step.3.text.2':
    'Connectnowチャネル設定のステップ4で取得したパラメーターを対応するパラメーターに設定します',
  'help.document.we.chat.official.step.3.text.3': '',

  // WhatsApp帮助文档
  'help.document.whats.app.left.menu': '開始前に準備が必要な資料',
  'help.document.whats.app.left.menu.1': '新しいアカウントを作成する',
  'help.document.whats.app.left.menu.18': '作成入り口をクリックします',
  'help.document.whats.app.left.menu.2': 'Facebookにログインする',
  'help.document.whats.app.left.menu.3': '契約を確認する',
  'help.document.whats.app.left.menu.4': 'Bmアカウントを作成する',
  'help.document.whats.app.left.menu.5': 'Wabaを作成する',
  'help.document.whats.app.left.menu.6': 'Wabaと番号情報を設定する',
  'help.document.whats.app.left.menu.7': '携帯電話番号をバインドする',
  'help.document.whats.app.left.menu.8': '認証コード検証',
  'help.document.whats.app.left.menu.9': '二次確認',
  'help.document.whats.app.left.menu.10': '作成成功',
  'help.document.whats.app.left.menu.11': 'バインド成功',
  'help.document.whats.app.left.menu.12': 'チャネル追加完了',
  'help.document.whats.app.left.menu.13': '企業認証',
  'help.document.whats.app.left.menu.14': 'ビジネス検証準備',
  'help.document.whats.app.left.menu.15': '証明書をアップロードする',
  'help.document.whats.app.left.menu.16': '連絡方法を選択する',
  'help.document.whats.app.left.menu.17': '検証結果を待つ',
  'help.document.whats.app.step.1.table.title': '資料を準備する',
  'help.document.whats.app.step.1.table.title.1': '例',
  'help.document.whats.app.step.1.table.title.2': '具体的な要件',
  'help.document.whats.app.step.1.table.body': 'Facebook個人アカウント',
  'help.document.whats.app.step.1.table.body.1': '-',
  'help.document.whats.app.step.1.table.body.2':
    '登録から1ヶ月以上経過した古いアカウント（開始前にリンクをクリックしてアカウントが正常か確認できます）。meta business manager（bmと略す）の作成に使用されます',
  'help.document.whats.app.step.1.table.body.3': '携帯電話番号',
  'help.document.whats.app.step.1.table.body.4': '+1 ***********',
  'help.document.whats.app.step.1.table.body.5':
    '認証コードSMSを受信できる必要があり、その番号は以前whatsappアプリまたはビジネスアカウントに登録されていないこと（以前に個人のwhatsappに登録したことがある場合は、個人のwhatsappアカウントを解約してから使用する必要があります）。whatsappビジネスアカウントの作成に使用されます。',
  'help.document.whats.app.step.1.table.body.5.1':
    '備考：中国大陸の携帯電話番号も利用可能です',
  'help.document.whats.app.step.1.table.body.6': 'Whatsapp business表示名',
  'help.document.whats.app.step.1.table.body.7': 'Connectnow',
  'help.document.whats.app.step.1.table.body.8':
    'ブランド公式サイトと関連性がある必要があり、whatsappビジネスアカウントの命名に使用されます',
  'help.document.whats.app.step.1.table.body.9': '企業名',
  'help.document.whats.app.step.1.table.body.10': 'Connectnow',
  'help.document.whats.app.step.1.table.body.11':
    '営業許可証または登録証明書に記載されている会社名と完全に一致している必要があります',
  'help.document.whats.app.step.1.table.body.12': '企業住所',
  'help.document.whats.app.step.1.table.body.13':
    '7500a beach road #04-307 the plaza singapore 19959',
  'help.document.whats.app.step.1.table.body.14':
    '営業許可証または登録証明書に記載されている住所と完全に一致している必要があります',
  'help.document.whats.app.step.1.table.body.15': '営業許可証または登録証明書',
  'help.document.whats.app.step.1.table.body.16': 'Business_pofile.pdf',
  'help.document.whats.app.step.1.table.body.17':
    '企業に対応する営業許可証または登録ファイル',
  'help.document.whats.app.step.1.table.body.18': 'ブランドURL',
  'help.document.whats.app.step.1.table.body.19': 'Www.connectnowai.com',
  'help.document.whats.app.step.1.table.body.20':
    '1.URLはhttps暗号化されている必要があります；',
  'help.document.whats.app.step.1.table.body.20.1':
    '2.URLの内容は明確に会社のビジネスを表している必要があります；',
  'help.document.whats.app.step.1.table.body.20.2':
    '3.URLの最下部には企業名と住所が含まれている必要があります。例：copyright @ xxxx (当該年) +会社名 all rights reserved',
  'help.document.whats.app.step.1.table.body.21': '企業メールアドレス',
  'help.document.whats.app.step.1.table.body.22': '<EMAIL>',
  'help.document.whats.app.step.1.table.body.23':
    '企業メールのドメインはブランドサイトのドメインと一致している必要があります。例：www.connectnowai.comと************************は要件を満たし、企業認証時に一度の認証コードメールを受信するために使用されます',
  'help.document.whats.app.step.1.text':
    '注：すでにBMアカウント（facebook business manager）をお持ちで、既存のBMアカウントの下にwhatsapp APIアカウントを作成したい場合は、以下のプロセスのステップ2でそのBMアカウント管理者のfacebookアカウントにログインし、ステップ4で既存のBMアカウントを選択してください。',
  'help.document.whats.app.step.2.text':
    '上記の資料を準備したら、管理者がconnectnowにログインし、左側メニューの「チャネル設定」->「whatsapp」->「追加」->「whatsappをバインド」をクリックして、組み込み登録プロセスを開始できます',
  'help.document.whats.app.step.2.text.1':
    '「チャネル設定」をクリックし、下にスクロールしてwhatsappを見つけ、「設定を開始」をクリックします',
  'help.document.whats.app.step.2.text.2':
    '「whatsappチャネル設定」の画面に入り、「チャネルを追加」をクリックします',
  'help.document.whats.app.step.2.text.3':
    '「whatsappチャネルを追加」のページに入り、「whatsappアカウントをバインド」をクリックし、facebookログインページに進みます',
  'help.document.whats.app.step.2.text.4':
    '「whatsappアカウントをバインド」をクリックするとfacebook認証ポップアップが表示されます。準備したfacebookアカウントでログインしてください。アカウントが正しくない場合は、「log in another account」をクリックして切り替えることができます。アカウントが正しいことを確認したら、「続行」をクリックして次のステップに進みます。',
  'help.document.whats.app.step.2.text.5':
    '間違いがないことを確認したら、「開始」をクリックして作成を続けます。',
  'help.document.whats.app.step.2.text.6': '会社の情報を入力してください：',
  'help.document.whats.app.step.2.text.7':
    '*会社名：会社名、営業許可証上の名前と完全に一致させてください。略称やブランド名は使用しないでください。',
  'help.document.whats.app.step.2.text.8':
    '*会社メール：ウェブサイトのドメインと同じメールアドレスの使用をおすすめします。',
  'help.document.whats.app.step.2.text.9':
    '*会社ウェブサイトまたはビジネスホームページ：会社URL。注意、会社のURLはhttpsプロトコルである必要があります。',
  'help.document.whats.app.step.2.text.10':
    '*国/地域：会社の運営国。認証する会社の所属国を必ず選択してください。例えば、企業認証時にインドネシアの営業許可証を提出する場合は、国をインドネシアと選択します。',
  'help.document.whats.app.step.2.text.11':
    '注：すでにBMアカウントがある場合、「business portfolio」オプションから選択できます。',
  'help.document.whats.app.step.2.text.12':
    '新しいWABAを選択/作成します。既存のWABAがある場合はドロップダウンリストから選択し、ない場合は「作成」を選択します。',
  'help.document.whats.app.step.2.text.13':
    '*whatsapp businessビジネスアカウント名：WABAの名前。内部でのビジネス区別に利用され、この情報はwhatsappアカウントプロフィールには表示されません。',
  'help.document.whats.app.step.2.text.14':
    '*whatsapp表示名：番号の名前。顧客が最終的に見る名前で、企業名またはブランド名に関連している必要があります。表示名ガイドラインを確認するにはクリックしてください。',
  'help.document.whats.app.step.2.text.15': '*カテゴリ：業界',
  'help.document.whats.app.step.2.text.16':
    'Whatsapp APIアカウント登録に使用する携帯電話番号を入力し、認証コードの取得方法を選択します：',
  'help.document.whats.app.step.2.text.17': '1. SMS',
  'help.document.whats.app.step.2.text.18': '2. 音声通話',
  'help.document.whats.app.step.2.text.19':
    '入力が完了したら「次へ」をクリックします。',
  'help.document.whats.app.step.2.text.20':
    'ヒント：中国大陸地域（+86）では、音声通話で認証コードを受信することをおすすめします。',
  'help.document.whats.app.step.2.text.21':
    '受け取った認証コードを入力し、「next」をクリックします',
  'help.document.whats.app.step.2.text.22':
    '作成内容を確認し、間違いがないことを確認したら、「続行」をクリックします',
  'help.document.whats.app.step.2.text.23':
    '作成成功のメッセージが表示されたら、ポップアップ下部の「完了」ボタンを必ずクリックしてください。',
  'help.document.whats.app.step.2.text.24':
    'ポップアップが閉じると、connectnowはバインドを行います。',
  'help.document.whats.app.step.2.text.25': '番号を選択する',
  'help.document.whats.app.step.2.text.26': 'ロボットを設定する',
  'help.document.whats.app.step.2.text.27':
    'チャネル名を入力し、「完了」をクリックすると、whatsapp business accountの登録が完了します',
  'help.document.whats.app.step.3.text':
    'Whatsappで毎日無制限のメッセージを送信できるように、BMアカウントを準備し検証してください。',
  'help.document.whats.app.step.3.text.1':
    'BMビジネス認証は、BMアカウントが実際の組織に属しているかどうかを検証することを目的としたプロセスです。',
  'help.document.whats.app.step.3.text.2':
    'まだBMビジネス認証を完了していない場合、whatsapp business APIの使用に制限があり、以下が含まれます：',
  'help.document.whats.app.step.3.text.3':
    '各電話番号から24時間以内に250人のユニークな顧客にビジネス開始の会話を送信する。',
  'help.document.whats.app.step.3.text.4': '最大2つの電話番号を登録。',
  'help.document.whats.app.step.3.text.5':
    '企業検証と表示名審査が完了すると、あなたのビジネスは迅速に制限を解除する機会が得られます：',
  'help.document.whats.app.step.3.text.6':
    'ビジネス開始の会話をより多くの顧客に拡張：24時間のローリング期間で1,000人のユニークな顧客から開始し、各電話番号で10,000、100,000、または無制限に段階的に増加します。',
  'help.document.whats.app.step.3.text.7': '無制限の顧客開始の会話に応答。',
  'help.document.whats.app.step.3.text.8':
    '公式ビジネスアカウント（OBA）になるようリクエスト。',
  'help.document.whats.app.step.3.text.9':
    '追加の電話番号を登録（各BMにつき最大20個）。',
  'help.document.whats.app.step.3.text.10':
    '2024年4月より、メッセージ品質基準を満たし表示名の審査が完了した後、企業はチャットで自社の名前を表示できるようになり、顧客からの信頼度が向上します。これは企業認証なしで可能です。metaドキュメントを参照してください：',
  'help.document.whats.app.step.3.text.11':
    'Https://developers.facebook.com/docs/whatsapp/messaging-limits#open-1k-conversations-in-30-days',
  'help.document.whats.app.step.3.text.12':
    'Meta検証に合格する機会を高めるために、以下の情報を事前に確認しておく必要があります：',
  'help.document.whats.app.step.3.text.13':
    '会社の公式サイトアドレス：HTTPS暗号化されており、会社名、住所、または電話番号が含まれていること',
  'help.document.whats.app.step.3.text.14':
    '会社URLと同じドメインのメールアドレス：一度の認証メールの受信に使用されます（ドメイン認証または携帯電話番号認証の方法では不要ですが、一般的にはメール認証をおすすめします）',
  'help.document.whats.app.step.3.text.15':
    '企業の法的名称を含む公式文書：例えば、営業許可証、会社定款、または企業納税登録証明書',
  'help.document.whats.app.step.3.text.16':
    '文書に含まれる会社名が会社の公式サイトと関連性があることを確認する必要があります。例えば、フッターに「会社abcに所属」と入力するなど',
  'help.document.whats.app.step.3.text.17': '検証プロセス',
  'help.document.whats.app.step.3.text.17.1': '1. 検証プロセス',
  'help.document.whats.app.step.3.text.18':
    'Meta business managerアカウントの管理者である場合（第2章の実行完了後、自動的にmeta business managerアカウントの管理者となります）、以下の手順に従ってビジネスの検証を開始できます：',
  'help.document.whats.app.step.3.text.19':
    'ビジネス管理プラットフォームの<a>「セキュリティセンター」</a>セクションに移動します。',
  'help.document.whats.app.step.3.text.20':
    '「検証」ボタンが表示されない場合は、connectnowプラットフォームにアクセスし、組み込み登録プロセス（つまり章2）を完了してください。',
  'help.document.whats.app.step.3.text.21': '2. 組織の基本情報を提出する',
  'help.document.whats.app.step.3.text.22':
    '組織名、住所、電話番号、ウェブサイトを提供します',
  'help.document.whats.app.step.3.text.23': 'ベストプラクティス',
  'help.document.whats.app.step.3.text.24':
    '入力した会社名/住所は、証明書上の名前/住所と一致している必要があります。',
  'help.document.whats.app.step.3.text.25':
    '電話番号は個人の携帯電話番号でも構いません（ただし、その後のプロセスで携帯電話番号を使用して会社を検証することはできません）',
  'help.document.whats.app.step.3.text.26':
    '提出するウェブサイトには、ドメイン所有権を証明するテキストコンテンツが必要です。例えば、フッターに「会社abcに所属」と入力するなど、この会社名は入力した会社名と一致している必要があります。',
  'help.document.whats.app.step.3.text.27':
    'アップロード完了後、ページの指示に従って情報を入力し、検証を提出します',
  'help.document.whats.app.step.3.text.28': 'ベストプラクティス',
  'help.document.whats.app.step.3.text.29':
    '優先はメール認証ですが、メールアドレスのドメインは提出したドメインと一致している必要があります（www.mypage.com >> <EMAIL>）。',
  'help.document.whats.app.step.3.text.30':
    'ドメイン認証は、ビジネスを検証する次のオプションです。',
  'help.document.whats.app.step.3.text.31':
    '認証コードを入力後、「次へ」をクリックし、最後のステップまで進んで検証を提出します',
  'help.document.whats.app.step.3.text.32':
    '検証提出後、最短10分、最長14営業日で決定が下されます。審査完了後、通知を受け取ります。検証済みであることの確認情報を受け取った場合、他に何もする必要はありません。',
};
