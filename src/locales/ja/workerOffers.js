export default {
  'allocation.please.select.ticket.tag': 'チケットタグを選択',
  'workerOffers.copy.message': 'コピー',
  'workerOffers.references.message': '引用',
  'workerOffers.withdraw.message': '送信取り消し',
  'workerOffers.withdraw.message.tips': 'メッセージの送信を取り消しました',
  'workerOffers.withdraw.message.edit': '再編集',
  'workerOffers.agent.status.limit':
    '現在、オンラインの担当者が上限に達しています。しばらくしてからもう一度お試しください',
  'workerOffers.chatList.setting.translation.tips':
    '有効にすると、顧客との各チャットがリアルタイムで翻訳されます',
  'channel.allocation.detail.tiktok.title': 'TikTokチャネルを設定',
  'add.tiktok.channel.configuration.title': 'TikTok Shopチャネルを追加',
  'add.tiktok.channel.configuration.title.update': 'TikTok Shopチャネルを編集',
  'add.tiktok.channel.configuration.tips':
    'TikTok Shopチャネルを追加してください',
  'add.tiktok.channel.configuration.tips.update':
    'TikTok Shopチャネルを編集してください',
  'tiktokRegion.channel.configuration.title.1':
    'TikTok Shopの販売者地域を選択してください',
  'tiktokRegion.channel.configuration.title.tips.1':
    '複数のTikTok Shopリージョンをお持ちの場合でも問題ありません。最初に設定したいものを選択してください。後で他のリージョンを追加できます',
  'tiktokRegion.channel.configuration.title.2':
    '認証してトークンを取得しましょう',
  'tiktokRegion.channel.configuration.title.tips.2':
    'TikTokのメッセージを受信するには、ConnectNowへのアクセスを許可する必要があります',
  'workerOffers.chatList.setting.customer.avatar.color':
    '顧客アバターの色の設定',
  'workerOffers.chatList.setting.customer.avatar.color.table.order': '一致順',
  'workerOffers.chatList.setting.customer.avatar.color.table.customer.color':
    '顧客アバターの色',
  'workerOffers.chatList.setting.customer.avatar.color.table.add':
    'ルールを作成',
  'workerOffers.chatList.setting.customer.avatar.color.table.other.tag':
    'その他のタグ',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.add':
    'ルールを作成',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.edit':
    'ルールを編集',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.title':
    'タグ名：',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.color.placeholder':
    '色を選択してください',
  'workerOffers.chatLayout.userNav.beInputting': '入力中...',
  'channel.allocation.detail.tiktok.title': 'TikTokチャネルを設定',
  'add.tiktok.channel.configuration.title': 'TikTok Shopチャネルを追加',
  'add.tiktok.channel.configuration.title.update': 'TikTok Shopチャネルを編集',
  'add.tiktok.channel.configuration.tips':
    'TikTok Shopチャネルを追加してください',
  'add.tiktok.channel.configuration.tips.update':
    'TikTok Shopチャネルを編集してください',
  'tiktokRegion.channel.configuration.title.1':
    'TikTok Shopの販売者地域を選択してください',
  'tiktokRegion.channel.configuration.title.tips.1':
    '複数のTikTok Shopリージョンをお持ちの場合でも問題ありません。最初に設定したいものを選択してください。後で他のリージョンを追加できます',
  'tiktokRegion.channel.configuration.title.2':
    '認証してトークンを取得しましょう',
  'tiktokRegion.channel.configuration.title.tips.2':
    'TikTokのメッセージを受信するには、ConnectNowへのアクセスを許可する必要があります',
  'tiktokRegion.channel.configuration.title.3.modal.title.tips':
    'このステップが完了していない場合、TikTokメッセージを配信できません。続行する前に、上記すべての指示に従ったことを確認してください',
  'user.management.operation.add.avator': 'パーソナライズされたアバターを追加',
  'user.management.operation.add.avator.nickname': 'あなたのニックネーム',
  'user.management.operation.add.avator.avatar': 'あなたのアバター',
  'user.management.operation.add.avator.setting': 'パーソナライズ設定',
  'user.management.operation.add.avator.nickname.placeholder':
    'ニックネームを入力してください',
  'user.management.operation.add.avator.avatar.placeholder':
    'アバターをアップロードしてください',
  'user.management.operation.add.avator.setting.btn':
    'パーソナライズアバター設定',
  'user.management.operation.add.avator.setting.delete.confirm':
    'このパーソナライズアバター設定を削除してもよろしいですか？',
  'user.management.operation.add.avator.setting.duplicate.tips':
    'このチャネルは他のアバター設定ルールで使用されています！',
  'personal.tabs.3': 'パーソナライズアバター',
  'personal.tabs.3.tips':
    '通常、アバターはプロフィールページで設定するだけで十分です。チャネルごとに異なるアバターやニックネームを使用したい場合は、ここで設定できます',
  'tiktokRegion.channel.configuration.title.2.shop':
    'アカウントを認証し、接続するTikTok Shopを選択してください',
  'tiktokRegion.channel.configuration.title.tips.2.shop':
    'TikTokのメッセージングおよび注文システムに接続するために、ConnectNowがあなたのTikTok Shop情報にアクセスすることを許可してください。複数のショップをお持ちの場合は、いずれか一つを選択してください',
  'tiktokRegion.channel.configuration.title.3.modal.title.tips':
    'このステップが完了していない場合、TikTokメッセージを配信できません。続行する前に、上記すべての指示に従ったことを確認してください',
  'channel.allocation.detail.select.account.tiktok.name': 'ショップ名：',
  'channel.allocation.detail.select.account.tiktok.name.placeholder':
    'ショップ名を入力してEnterキーで検索',
  'channel.allocation.detail.select.account.tiktok.shopId': 'ショップID：',
  'channel.allocation.detail.select.account.tiktok.shopName': 'ショップ名：',
  'channel.allocation.detail.select.account.tiktok.shopCode':
    'ショップコード：',
  'workerOffers.withdraw.message.tips.1': 'メッセージを取り消しました',
  'ticketOrCustomerSettings.config.title.1': 'チケットタイプ定義',
  'ticketOrCustomerSettings.config.title.2': 'チケットフィールド定義',
  'ticketOrCustomerSettings.config.title.3': '顧客フィールド定義',
  'ticketOrCustomerSettings.config.title.4': '権限設定',
  'agentOperationSettings.config.title.1': 'SLA設定',
  'agentOperationSettings.config.title.2': '営業時間',
  'agentOperationSettings.config.title.3': 'エージェントステータス定義',
  'workerOffers.chatList.setting.translation.phone.tips':
    'オンにすると、お客様との通話履歴がリアルタイムで翻訳されます。',
  'worktable.live.micro.phone': 'マイク台',
  'work.order.reply.select.micro.phone': 'マイクの選択',
  'worktable.live.speaker': 'スピーカ',
  'work.order.reply.select.speaker': 'スピーカの選択',
  'worktable.live.testing.speakers': 'テストスピーカ',
  'worktable.live.testing.speakers.not.support':
    '現在のブラウザではスピーカデバイスの切り替えはサポートされていません',
  'agentOperationSettings.config.title.4': '转人工等待音乐',
  'hold.music.content.waiting.prompt.sound': '上传等待提示音',
  'hold.music.content.waiting.prompt.sound.required': '请上传等待提示音',
  'hold.music.content.carousel': '是否轮播',
  'hold.music.content.carousel.num': '轮播次数',
  'hold.music.content.carousel.num.placeholder': '请输入1～10之间的整数',
  'hold.music.content.carousel.num.placeholder.1': '轮播次数不能为空',
  'hold.music.content.carousel.tips':
    '备注：等待转人工音乐轮播结束后自动挂断电话',
  'hold.music.content.upload.tips':
    '将文件拖拽到此处或<a>{CH}</a>，支持单个上传。目前支持mp3、AAC、OGG、WAV等格式文件。',
  'hold.music.content.upload.waring':
    '只能上传 .mp3, .aac, .ogg, .wav 格式的音频文件！',
};
