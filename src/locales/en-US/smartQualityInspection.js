export default {
  'smart.quality.evaluation.table.title': 'Intelligent quality assessment form',
  'smart.quality.evaluation.table.add': 'Add assessment form',
  'smart.quality.evaluation.table.name': 'Assessment form name',
  'smart.quality.evaluation.rule.category': 'Rule category',
  'smart.quality.evaluation.assessment.name': 'Assessment form name',
  'smart.quality.evaluation.table.channel': 'Channel',
  'smart.quality.evaluation.table.channel.all': 'All channels',
  'smart.quality.evaluation.table.ticket.type': 'Ticket type',
  'smart.quality.evaluation.table.status': 'Publish status',
  'smart.quality.evaluation.table.status.published': 'Published',
  'smart.quality.evaluation.table.status.disabled': 'Unpublished',
  'smart.quality.evaluation.table.rule.count': 'Number of scoring rules',
  'smart.quality.evaluation.table.total.score': 'Total score',
  'smart.quality.evaluation.table.operation': 'Actions',
  'smart.quality.evaluation.table.enable': 'Enable',
  'smart.quality.evaluation.table.enable.success': 'Enabled successfully',
  'smart.quality.evaluation.table.disable': 'Disable',
  'smart.quality.evaluation.table.disable.success': 'Disabled successfully',
  'smart.quality.evaluation.table.edit': 'Edit',
  'smart.quality.evaluation.table.edit.rule': 'Edit rule',
  'smart.quality.evaluation.table.history': 'Evaluation history',
  'smart.quality.evaluation.table.delete': 'Delete',
  'smart.quality.evaluation.table.delete.confirm':
    'Delete this assessment form?',
  'smart.quality.evaluation.table.delete.ok': 'Yes',
  'smart.quality.evaluation.table.delete.cancel': 'No',
  'smart.quality.evaluation.table.delete.success': 'Deleted successfully',
  'smart.quality.evaluation.list.page.total.num': 'Total {total} items',
  // ====== 添加评估表页面相关 ======
  'smart.quality.evaluation.add': 'Add assessment form',
  'smart.quality.evaluation.add.baseinfo': 'Basic information',
  'smart.quality.evaluation.add.rule': 'Scoring rules',
  'smart.quality.evaluation.add.permission': 'Permission settings',
  'smart.quality.evaluation.add.name': 'Assessment form name',
  'smart.quality.evaluation.add.name.placeholder': 'Enter assessment form name',
  'smart.quality.evaluation.add.name.required': 'Enter assessment form name',
  'smart.quality.evaluation.add.name.max': 'Length cannot exceed 80 characters',
  'smart.quality.evaluation.add.channel': 'Applicable channels',
  'smart.quality.evaluation.add.channel.placeholder':
    'Select applicable channels',
  'smart.quality.evaluation.add.channel.required': 'Select applicable channels',
  'smart.quality.evaluation.add.ticket.type': 'Applicable ticket types',
  'smart.quality.evaluation.add.ticket.type.placeholder':
    'Select applicable ticket types',
  'smart.quality.evaluation.add.ticket.type.required':
    'Select applicable ticket types',
  'smart.quality.evaluation.add.total.score': 'Full score',
  'smart.quality.evaluation.add.total.score.placeholder': 'Enter full score',
  'smart.quality.evaluation.add.total.score.required': 'Enter full score',
  'smart.quality.evaluation.add.score.mechanism': 'Scoring mechanism',
  'smart.quality.evaluation.add.score.mechanism.add': 'Additive scoring',
  'smart.quality.evaluation.add.score.mechanism.subtract': 'Deductive scoring',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.title':
    'Additive scoring',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.1':
    'Performance value-add assessment',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.2':
    'Starts from a base score, adds points for excellent performance',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.3':
    'Emphasizes positive behaviors and extra value beyond the standard',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.title':
    'Deductive scoring',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.1':
    'Compliance baseline assessment',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.2':
    'Starts from a full score, deducts points for non-compliant items',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.3':
    'Emphasizes maintaining basic service standards and compliance',
  'smart.quality.evaluation.add.score.mechanism.required':
    'Select scoring mechanism',
  'smart.quality.evaluation.add.permission.scorer': 'Evaluator',
  'smart.quality.evaluation.add.permission.scorer.tooltip':
    'Defines who can score based on this assessment form',
  'smart.quality.evaluation.add.permission.scorer.required': 'Select evaluator',
  'smart.quality.evaluation.add.permission.scorer.placeholder':
    'Select evaluator',
  'smart.quality.evaluation.add.cancel.confirm':
    'Canceling will clear the form. are you sure you want to cancel?',
  'smart.quality.common.cancel': 'Cancel',
  'smart.quality.common.next': 'Next',
  'smart.quality.common.yes': 'Yes',
  'smart.quality.common.no': 'No',
  'smart.quality.evaluation.add.channel.all': 'All',
  'smart.quality.evaluation.add.ticket.type.all': 'All',
  // ====== END 添加评估表页面相关 ======

  // ====== 评估历史记录页面相关 ======
  'smart.quality.evaluation.history.title': 'Evaluation history',
  'smart.quality.evaluation.history.name.placeholder':
    'Enter assessment form name',
  'smart.quality.evaluation.history.channel.placeholder': 'Select channel',
  'smart.quality.evaluation.history.ticket.type.placeholder':
    'Select ticket type',
  'smart.quality.evaluation.history.agent.name': 'Agent name',
  'smart.quality.evaluation.history.agent.name.placeholder':
    'Select agent name',
  'smart.quality.evaluation.history.ticket.id': 'Ticket ID',
  'smart.quality.evaluation.history.ticket.id.placeholder': 'Enter ticket ID',
  'smart.quality.evaluation.history.score': 'Score',
  'smart.quality.evaluation.history.evaluator': 'Evaluator',
  'smart.quality.evaluation.history.evaluator.placeholder': 'Select evaluator',
  'smart.quality.evaluation.history.score.range': 'Score range',
  'smart.quality.evaluation.history.score.min': 'Min',
  'smart.quality.evaluation.history.score.max': 'Max',
  'smart.quality.evaluation.history.score.min.error':
    'Min value cannot be greater than max value',
  'smart.quality.evaluation.history.score.max.error':
    'Max value cannot be less than min value',
  'smart.quality.evaluation.history.score.range.error':
    'Max value cannot be less than min value',
  'smart.quality.evaluation.history.score.both.required':
    'Both max and min values are required',
  'smart.quality.evaluation.history.score.range.warning':
    'Min value should not be greater than max value',
  'smart.quality.evaluation.history.score.format.error':
    'Please enter a valid number with up to 2 decimal places',
  'smart.quality.evaluation.history.search': 'Search',
  'smart.quality.evaluation.history.details': 'Details',
  'smart.quality.evaluation.history.export.pdf': 'Export PDF',
  'smart.quality.evaluation.history.page.total.num': 'Total {total} items',
  'smart.quality.evaluation.history.evaluation.time': 'Evaluation time',
  'smart.quality.evaluation.history.return.list': 'Back to list',
  // ====== END 评估历史记录页面相关 ======

  'smart.quality.evaluation.rule.version.current': 'Latest version',
  'smart.quality.evaluation.rule.version.current.tip': 'Select latest version',
  'smart.quality.evaluation.rule.delete.category.tip.title': 'Prompt',
  'smart.quality.evaluation.rule.delete.category.tip':
    'This category contains rules. deleting it will also delete all rules within it',

  // start 规则列表
  'smart.quality.evaluation.rule.category': 'Rule category',
  'smart.quality.evaluation.rule.title': 'Add assessment form',
  'smart.quality.evaluation.rule.edit.title': 'Edit rule',
  'smart.quality.evaluation.rule.deploy.status': 'Deployment status',
  'smart.quality.evaluation.rule.deploy.status.unpublished': 'Unpublished',
  'smart.quality.evaluation.rule.deploy.status.published': 'Published',
  'smart.quality.evaluation.rule.button.cancel': 'Cancel',
  'smart.quality.evaluation.rule.button.save': 'Save',
  'smart.quality.evaluation.rule.button.save.and.publish': 'Save and publish',
  'smart.quality.evaluation.rule.search.placeholder':
    'Enter rule name or description to search',
  'smart.quality.evaluation.rule.add': 'Add rule',
  'smart.quality.evaluation.rule.table.category': 'Rule category',
  'smart.quality.evaluation.rule.table.name': 'Rule name',
  'smart.quality.evaluation.rule.table.description': 'Rule description',
  'smart.quality.evaluation.rule.table.score': 'Scoring rule',
  'smart.quality.evaluation.rule.table.evaluation.method': 'Scoring method',
  'smart.quality.evaluation.rule.table.evaluation.method.manual':
    'Manual scoring',
  'smart.quality.evaluation.rule.table.evaluation.method.ai': 'AI scoring',
  'smart.quality.evaluation.rule.table.operation': 'Actions',
  'smart.quality.evaluation.rule.table.operation.edit': 'Edit',
  'smart.quality.evaluation.rule.table.delete.confirm': 'Delete this rule?',
  'smart.quality.evaluation.rule.table.delete.ok': 'Yes',
  'smart.quality.evaluation.rule.table.delete.cancel': 'No',
  'smart.quality.evaluation.rule.table.operation.delete': 'Delete',
  'smart.quality.evaluation.rule.table.operation.detail': 'Details',
  'smart.quality.evaluation.rule.new.top.level': 'New category',
  'smart.quality.evaluation.rule.new.category': 'New category',
  'smart.quality.evaluation.rule.page.total.num': 'Total {total} items',
  'smart.quality.evaluation.rule.add.score.add': 'Add',
  'smart.quality.evaluation.rule.add.score.subtract': 'Deduct',
  // end 规则列表

  // start 添加规则页面
  'smart.quality.evaluation.rule.add.title': 'Add rule',
  'smart.quality.evaluation.rule.edit.title': 'Edit rule',
  'smart.quality.evaluation.rule.detail.title': 'Rule details',
  'smart.quality.evaluation.rule.add.baseinfo': 'Basic information',
  'smart.quality.evaluation.rule.add.baseinfo.name': 'Rule name',
  'smart.quality.evaluation.rule.add.baseinfo.name.placeholder':
    'Enter rule name',
  'smart.quality.evaluation.rule.add.baseinfo.name.required': 'Enter rule name',
  'smart.quality.evaluation.rule.name': 'Rule name',
  'smart.quality.evaluation.rule.category.required': 'Select rule category',
  'smart.quality.evaluation.rule.name.placeholder': 'Enter rule name',
  'smart.quality.evaluation.rule.name.required': 'Enter rule name',
  'smart.quality.evaluation.rule.name.max':
    'Length cannot exceed 80 characters',
  'smart.quality.evaluation.rule.description': 'Rule description',
  'smart.quality.evaluation.rule.description.placeholder':
    'Enter rule description',
  'smart.quality.evaluation.rule.description.required':
    'Enter rule description',
  'smart.quality.evaluation.rule.description.max':
    'Length cannot exceed 200 characters',
  'smart.quality.evaluation.rule.category.placeholder': 'Select rule category',
  'smart.quality.evaluation.rule.category.required': 'Select rule category',
  'smart.quality.evaluation.rule.evaluation.method': 'Scoring method',
  'smart.quality.evaluation.rule.evaluation.method.ai': 'AI scoring',
  'smart.quality.evaluation.rule.evaluation.method.manual': 'Manual scoring',
  'smart.quality.evaluation.rule.ai.settings': 'AI scoring rule',
  'smart.quality.evaluation.rule.manual.settings': 'Manual scoring rule',
  'smart.quality.evaluation.rule.total.score': 'Rule total score',
  'smart.quality.evaluation.rule.total.score.placeholder':
    'Enter rule total score',
  'smart.quality.evaluation.rule.total.score.required':
    'Enter rule total score',
  'smart.quality.evaluation.rule.total.score.exceed.error':
    "Rule total score cannot exceed the form's total score",
  'smart.quality.evaluation.rule.total.score.exceed.error.add':
    "Rule's total added points cannot exceed the form's total score",
  'smart.quality.evaluation.rule.total.score.exceed.error.subtract':
    "Rule's total deducted points cannot exceed the form's total score",
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error':
    "AI scoring rule score cannot exceed the rule's total score",
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.add':
    "AI scoring rule's added points cannot exceed the rule's total score",
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.subtract':
    "AI scoring rule's deducted points cannot exceed the rule's total score",
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error':
    "AI scoring rule score cannot exceed the rule's total score",
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.add':
    "AI scoring rule's added points cannot exceed the rule's total score",
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.subtract':
    "AI scoring rule's deducted points cannot exceed the rule's total score",
  'smart.quality.evaluation.rule.manual.option.required':
    'Please add at least one scoring rule',
  'smart.quality.evaluation.rule.manual.option.save.first':
    'Please save the rule you are editing first',
  'smart.quality.evaluation.rule.check.points': 'Checkpoint',
  'smart.quality.evaluation.rule.check.point.placeholder':
    'Enter a detailed checkpoint description',
  'smart.quality.evaluation.rule.check.point.required': 'Enter checkpoint',
  'smart.quality.evaluation.rule.add.check.point': 'Add checkpoint',
  'smart.quality.evaluation.rule.check.points.max':
    'Up to 15 checkpoints can be added',
  'smart.quality.evaluation.add.rule.button.return': 'Back',
  'smart.quality.evaluation.add.rule.button.save': 'Save',
  // end 添加规则页面

  // start 人工评分规则相关
  'smart.quality.evaluation.add.rule.manual.option.name': 'Option name',
  'smart.quality.evaluation.add.rule.manual.option.name.required':
    'Enter option name',
  'smart.quality.evaluation.add.rule.manual.option.name.placeholder':
    'Enter option name',
  'smart.quality.evaluation.add.rule.manual.option.name.duplicate':
    'Option names cannot be duplicated',
  'smart.quality.evaluation.add.rule.manual.option.score': 'Score',
  'smart.quality.evaluation.add.rule.manual.option.score.required':
    'Enter score',
  'smart.quality.evaluation.add.rule.manual.option.operation': 'Actions',
  'smart.quality.evaluation.add.rule.manual.rules': 'Manual scoring rule',
  'smart.quality.evaluation.add.rule.manual.add.option': 'Add option',
  'smart.quality.evaluation.add.rule.manual.standard':
    'Evaluation criteria reference',
  'smart.quality.evaluation.add.rule.manual.standard.required':
    'Enter evaluation criteria reference',
  'smart.quality.evaluation.add.rule.manual.standard.placeholder':
    'Enter evaluation criteria reference',
  // end 人工评分规则相关

  // start AIGC评分规则相关
  'smart.quality.evaluation.add.rule.aigc.rules': 'AI scoring rule',
  'smart.quality.evaluation.add.rule.aigc.rules.required':
    'Select AI scoring rule',
  'smart.quality.evaluation.add.rule.aigc.rule1.title':
    'When any one of the checkpoints appears',
  'smart.quality.evaluation.add.rule.aigc.rule1.prefix':
    ', for each occurrence',

  'smart.quality.evaluation.add.rule.aigc.rule1.score.required': 'Enter score',
  'smart.quality.evaluation.add.rule.aigc.rule1.middle':
    'points, with a maximum of',
  'smart.quality.evaluation.add.rule.aigc.rule1.max.score.required':
    'Enter maximum cumulative score',
  'smart.quality.evaluation.add.rule.aigc.rule1.suffix': 'points',
  'smart.quality.evaluation.add.rule.aigc.rule2.title':
    'When multiple checkpoints appear',
  'smart.quality.evaluation.add.rule.aigc.rule2.prefix':
    ', when the number of occurrences is',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.required':
    'Select number of occurrences',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.gt': 'Greater than',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.eq': 'Equal to',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.lt': 'Less than',
  'smart.quality.evaluation.add.rule.aigc.rule2.threshold.required':
    'Enter number of times',
  'smart.quality.evaluation.add.rule.aigc.rule2.middle': 'times,',
  'smart.quality.evaluation.add.rule.aigc.rule2.score.required': 'Enter score',
  'smart.quality.evaluation.add.rule.aigc.rule2.suffix': 'score',
  // end AIGC评分规则相关
};
