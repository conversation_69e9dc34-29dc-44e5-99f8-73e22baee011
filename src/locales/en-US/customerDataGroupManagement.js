export default {
  'customerDataGroupManagement.groupName': 'Customer segment name: ',
  'customerDataGroupManagement.groupName.placeholder':
    'Enter customer segment name, press enter to search',
  // 'Please enter the group name and press Enter to search',
  'customerDataGroupManagement.management': 'Customer Segment Management',
  'customerDataGroupManagement.addGroup': 'Add customer segment',
  'customerDataGroupManagement.groupName.table': 'Customer segment name',
  'customerDataGroupManagement.groupName.table.pattern':
    'Customer segment names can only consist of English letters (upper or lower case) or Chinese characters',
  'customerDataGroupManagement.groupName.table.placeholder':
    'Please enter the Customer segment name',
  'customerDataGroupManagement.groupName.table.max':
    'The customer segment name can have a maximum of 40 characters',

  'customerDataGroupManagement.customersNumber.table': 'Number of customers',
  'customerDataGroupManagement.creationTime.table': 'Create time',
  'customerDataGroupManagement.founder.table': 'Creator',
  'customerDataGroupManagement.description.table': 'Description',
  'customerDataGroupManagement.operation.table': 'Action',
  'customerDataGroupManagement.editor.table': 'Update',
  'customerDataGroupManagement.delete.table': 'Delete',
  'customerDataGroupManagement.customer.list.table': 'Details',
  'customerDataGroupManagement.describe.detail': 'Description',
  'customerDataGroupManagement.describe.detail.placeholder':
    'Please enter the description, with a maximum of 2000 characters',
  'customerDataGroupManagement.describe.detail.max':
    'The note content can have a maximum of 2000 characters',
  'customerDataGroupManagement.cancel.btn': 'Cancel',
  'customerDataGroupManagement.add.btn': 'Add',
  'customerDataGroupManagement.sure.btn': 'Confirm',
  'platformUserManagement.update.title': 'Modify group',

  'new.customerDataGroupManagement.btn.1': 'Batch remove',
  'new.customerDataGroupManagement.btn.2': 'Batch upload',
  'new.customerDataGroupManagement.btn.3': 'Add customer',
  'new.customerDataGroupManagement.left.btn.1': 'All Customers',
  'new.customerDataGroupManagement.left.btn.2': 'Excel Import',
  'new.customerDataGroupManagement.left.btn.3': 'Tags',
  'new.customerDataGroupManagement.left.btn.3.select': 'Tags: ',
  'new.customerDataGroupManagement.left.btn.4': 'Attributes',
  'new.customerDataGroupManagement.left.btn.5': 'Contact Time',
  'new.customerDataGroupManagement.left.btn.6': 'Subscriptions',
  'new.customerDataGroupManagement.left.btn.7': 'Customer Level',
  'new.customerDataGroupManagement.left.btn.8': 'Advanced Rule',
  'new.customerDataGroupManagement.left.title': 'Add customer segment',
  'new.customerDataGroupManagement.left.editor.title':
    'Editor customer segment',
  'new.customerDataGroupManagement.left.tips':
    'Please select a method for customer segment',
  'new.customerDataGroupManagement.right.Popconfirm':
    'Are you sure you want to remove this customer?',
  'new.customerDataGroupManagement.right.com1.upload.1':
    'Drag and drop files here or',
  'new.customerDataGroupManagement.right.com1.upload.2':
    ',Supports single upload.',
  'new.customerDataGroupManagement.right.com1.upload.3':
    'Choose to download document template,',
  'new.customerDataGroupManagement.right.com1.upload.4': 'Click to download.',
  'new.customerDataGroupManagement.right.com1.tips':
    'Set the customer data attribute field corresponding to each column in Excel in the system',
  'new.customerDataGroupManagement.right.com.title': 'Customer segment rules',
  'new.customerDataGroupManagement.right.com1.label': 'Excel header',
  'new.customerDataGroupManagement.right.com1.btn.save':
    'Save customer segment',
  'new.customerDataGroupManagement.right.table.input':
    'Please enter your contact information, supporting email and phone number',
  'new.customerDataGroupManagement.right.com2.tips.new.add.1': 'Please note:',
  'new.customerDataGroupManagement.right.com2.tips.new.add':
    '1.That the customer segmentation you have currentlydefined is a dynamic rule (not fixed customer data). When new customers meet this condition, they will be automatically added to this customer segment.',
  'new.customerDataGroupManagement.right.com2.tips':
    '2.When you enter multiple tags, if a customer has any of those tags, they will be categorized into the current customer segment.',
  'new.customerDataGroupManagement.right.com2.tips.new.add.2':
    'Please note:That the customer segmentation you have currentlydefined is a dynamic rule (not fixed customer data). When new customers meet this condition, they will be automatically added to this customer segment.',
  'new.customerDataGroupManagement.right.com2.btn': 'Search',
  'new.customerDataGroupManagement.right.com3.label.1': 'Age: ',
  'new.customerDataGroupManagement.right.com3.placeholder.1':
    'Please select age',

  'new.customerDataGroupManagement.right.com3.label.2': 'Gender: ',
  'new.customerDataGroupManagement.right.com3.placeholder.2':
    'Please select gender',

  'new.customerDataGroupManagement.right.com3.label.3': 'Country: ',
  'new.customerDataGroupManagement.right.com3.placeholder.3':
    'Please select your country',

  'new.customerDataGroupManagement.right.com3.label.4': 'Career: ',
  'new.customerDataGroupManagement.right.com3.placeholder.4':
    'Please choose your career',

  'new.customerDataGroupManagement.right.com3.label.5': 'Hobbies: ',
  'new.customerDataGroupManagement.right.com3.placeholder.5':
    'Please choose your hobbies',

  'new.customerDataGroupManagement.right.com4.jin': 'Within the last',
  'new.customerDataGroupManagement.right.com4.tiannei': 'days',
  'new.customerDataGroupManagement.right.com5.label': 'Subscription status: ',
  'new.customerDataGroupManagement.right.com6.label': 'Customer level: ',
  'new.customerDataGroupManagement.right.com7.label':
    'Current filtering criteria: ',
  'new.customerDataGroupManagement.right.com7.radio.1':
    'It is enough to meet one of the following conditions',
  'new.customerDataGroupManagement.right.com7.radio.2':
    'All conditions must be met',
  'new.customerDataGroupManagement.btn.4': 'Add conditions',
  'new.customerDataGroupManagement.right.com7.map.title': 'Judgment condition',
  'new.customerDataGroupManagement.modal.title':
    'Basic customer segment information',
  'new.customerDataGroupManagement.groupName.table': 'Segment name',
  'new.customerDataGroupManagement.describe.detail': 'Description',
  'new.customerDataGroupManagement.groupName.table.pattern':
    'Subcategory names can only consist of uppercase or lowercase English letters or Chinese characters',
  'new.customerDataGroupManagement.groupName.table.placeholder':
    'Please enter the segment name',
  'customerInformation.table.nation.contact.email': 'Contact email',
  'customerInformation.table.contact.phone': 'Phone number',
  'customer.segmentation.customer.attitude.1':
    'The customer contacts the company proactively',
  'customer.segmentation.customer.attitude.2':
    'The system pushes marketing messages to customers',
  'customer.segmentation.customer.attitude.3':
    'The company actively contacts customers',
  'customer.segmentation.customer.attitude.4': 'Customer subscription',
  'customer.segmentation.customer.attitude.5': 'Customer unsubscription',
  'new.customerDataGroupManagement.delete.modal.title': 'Batch remove',
  'new.customerDataGroupManagement.delete.modal.text':
    'Are you sure you want to remove customers in bulk?',
  'new.customerDataGroupManagement.delete.modal.text.tips':
    'After removing the customer from the list, they will not be saved to the current customer segment.',
  'new.customerDataGroupManagement.import.excel.tips': 'No Excel file imported',
  'new.customerDataGroupManagement.import.excel.tips.1':
    'Please complete the selection of all customer profile attribute fields',

  'new.customerDataGroupManagement.right.com1.upload.5': 'File Instructions:',
  'new.customerDataGroupManagement.right.com1.upload.6':
    '1.Only one file upload is supported.',
  'new.customerDataGroupManagement.right.com1.upload.7':
    '2.Excel format supports two formats: .xls and .xlsx.',
  'new.customerDataGroupManagement.right.com1.upload.8':
    '3.Please make sure that the first row in the Excel file is the header, such as: customer name, customer phone number, etc. You can customize the specific header according to your actual needs.',
  'new.customerDataGroupManagement.delete.tips':
    'Confirm deletion of this customer segmentation?',

  'new.customerDataGroupManagement.customer.unsubscribed': 'Not subscribed',
  'new.customerDataGroupManagement.customer.subscribed.select': 'Subscribed',
  'new.customerDataGroupManagement.customer.cancel.subscribed.select':
    'Unsubscribed',
  'new.customerDataGroupManagement.customer.subscribed.required':
    'Please select customer subscription status',

  'new.customerDataGroupManagement.customer.eliminate.unsubscribed':
    'Exclude unsubscribed customers',
};
