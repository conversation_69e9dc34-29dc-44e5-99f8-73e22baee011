export default {
  // Ticket management
  'work.order.management.status.all': 'All',
  'work.order.management.status.in.process': 'Processing',
  'work.order.management.status.undistributed': 'Unassigned',
  'work.order.management.status.queue': 'Queue',
  'work.order.management.status.robot': 'Robot',
  'work.order.management.status.resolved': 'Resolved',
  'work.order.management.status.transferred.order': 'Transferred',
  'work.order.management.status.terminated': 'Terminated',

  'work.order.management.more.fields': ' More fields',
  'work.order.management.search': ' Search',
  'work.order.management.create.work.order': 'Create ticket',
  'work.order.management.batch.export': 'Batch export',
  'work.order.management.batch.marking': 'Mark as resolved',
  'work.order.management.column': 'Column options',

  'work.order.management.fuzzy.query': 'Fuzzy search',
  'work.order.management.table.more': 'More options',
  'work.order.management.table.detail': 'Details',
  'work.order.management.table.operation': 'Actions',
  'work.order.management.table.work.order.id': 'Ticket ID',
  'work.order.management.table.channel.name': 'Channel type',
  'work.order.management.table.channel.name.1': 'Channel',

  'work.order.management.table.priority': 'Priority',
  'work.order.management.table.SLA.service.objectives': 'SLA target',
  'work.order.management.table.status': 'Status',
  'work.order.management.table.customer.name': 'Customer name',
  'work.order.management.table.customer.contact.information':
    'Customer contact information',
  'work.order.management.table.duration': 'Duration',
  'work.order.management.table.seat.name': 'Agent name',
  'work.order.management.table.creation.time': 'Create time',
  'work.order.management.table.modify.time': 'Modify time',
  'work.order.management.table.resolution.time': 'Resolution time',

  'work.order.management.table.work.order.type': 'Ticket type',
  'work.order.management.table.work.order.theme': 'Ticket subject',
  'work.order.management.table.robot.work.order': 'Chatbot ticket',

  'work.order.management.btn.cancel': 'Cancel',
  'work.order.management.btn.sure': 'Confirm',
  'work.order.management.btn.replay': 'Reply',

  'work.order.management.tips': 'Please enter',

  'work.order.management.resolve.work.orders': 'Resolve ticket',
  'work.order.management.terminate.work.orders': 'Terminate ticket',
  'work.order.management.transfer.work.orders': 'Reassign ticket',
  'work.order.management.claim.work.orders': 'Claim',
  'work.order.management.reminder.work.orders': 'Urge ticket',
  'work.order.management.notes': 'Note',
  'work.order.management.replay': 'Reply',

  'work.order.management.top.reminder': 'Top Urge ticket',
  'work.order.management.table.work.order.code': 'Ticket number',

  // Ticket creation
  'create.work.order.title.text': 'Ticket content',
  'create.work.order.title.text1': 'Customer historical tickets',
  'create.work.order.reply.topic': 'Ticket subject：',
  'create.work.order.reply.topic.required': 'Please fill in the ticket subject',
  'create.work.order.attachment.information': 'Attachment files:',
  'create.work.order.btn.upload': 'Upload',
  'create.work.order.reply.content': 'Ticket content:',
  'create.work.order.reply.content.required':
    'Please fill in the ticket content',
  'create.work.order.work.order.type': 'Ticket type: ',
  'create.work.order.work.order.type.required': 'Please select the ticket type',
  'create.work.order.urgency': 'Priority:',
  'create.work.order.urgency.required': 'Please select the priority',
  'create.work.order.related.customers': 'Customer:',
  'create.work.order.related.customers.required': 'Please select the customer',
  'create.work.order.select.seats': 'Select agent:',
  'create.work.order.select.seats.required': 'Please select the agent',
  'create.work.order.notify.customers': 'Notify customer:',
  'create.work.order.notification.channels': 'Email customer:',
  'create.work.order.notification.channels.required':
    'Please select the Email customer',
  'create.work.order.extended.information': 'Extension information:',
  'create.work.order.extended.information.required':
    'Please enter the extension information',

  'create.work.order.radio.yes': 'Yes',
  'create.work.order.radio.no': 'No',

  'work.order.ai.editor': 'AI editor',
  'work.order.reply.work.order': 'Reply',
  'work.order.assignment.work.order': 'Assign',
  'work.order.transfer.work.order': 'Reassign',
  'work.order.status.change': 'Status',
  'work.order.other.operations': 'Other',
  'work.order.editor.work.order': 'Update',

  'work.order.ai.editor.text':
    "Using CoCo's AI assistant to help you reply emails faster and better",
  'work.order.ai.editor.tips': "Don't show again",
  'work.order.ai.editor.btn.try': 'Try it',
  'work.order.ai.editor.btn.close': 'Close',

  // 工单详情
  'work.order.detail.replay.detail': 'Reply details',
  'work.order.detail.customer.information': 'Customer information',
  'work.order.detail.associated.work.order': 'Associated tickets',
  'work.order.detail.historical.work.order': 'Historical tickets',
  'work.order.detail.notes.list': 'Notes',

  'work.order.detail.withdraw.text': '您已撤回一条消息',

  'work.order.detail.title.text': 'Operation records',
  'work.order.detail.title.text1': 'Basic information',
  'work.order.detail.title.text2': 'Ticket extension information',

  'work.order.detail.content.type.hot.spot.issues': '热点问题',

  'work.order.detail.work.order.level': 'Ticket priority:',
  'work.order.detail.work.order.level.required':
    'Please select ticket priority',
  'work.order.detail.seat': 'Agent:',
  'work.order.detail.order.number': 'Ticket ID:',
  'work.order.detail.order.number.required': 'Please enter order number',
  'work.order.detail.logistics.tracking.number': 'Logistics tracking number：',
  'work.order.detail.logistics.tracking.number.required':
    'Please enter logistics tracking number',
  'work.order.detail.model': 'Machine model:',
  'work.order.detail.model.required': 'Please enter machine model',

  'work.order.detail.intelligent.ai': 'Artificial Intelligence',
  'work.order.detail.ai.writing': 'AI writing',
  'work.order.detail.ai.drawing': 'AI drawing',
  'work.order.detail.ai.title.text': 'What do you want AI to help you with?',
  'work.order.detail.ai.title.text1':
    'Which language do you want the content to be generated in?',
  'work.order.detail.ai.title.text2': 'Generate content',
  'work.order.detail.ai.title.text8': 'Generate image',
  'work.order.detail.ai.title.text3':
    'Please enter the size of the generated image (in pixels)',
  'work.order.detail.ai.title.text4': 'Positive prompt words',
  'work.order.detail.ai.title.text5':
    'Positive prompt is to describe what you want to appear in the image',
  'work.order.detail.ai.title.text6': 'Negative prompt words',
  'work.order.detail.ai.title.text7':
    'Negative prompt is to describe what you do not want to appear in the image',

  'work.order.detail.ai.textarea.text':
    'Enter the topic for generating content, press Enter to generate text',
  'work.order.detail.ai.textarea.text1':
    'Describe the image in English, use commas to separate the description sentences, such as panda, bamboo, forest',
  'work.order.detail.ai.textarea.text2':
    "Can't write English prompts? Type Chinese to English here, such as panda, bamboo, forest",

  'work.order.detail.ai.img.width': 'Width:',
  'work.order.detail.ai.img.height': 'Height:',

  'work.order.detail.ai.btn.associate': 'Association',
  'work.order.detail.ai.btn.translate': 'Translation',
  'work.order.detail.ai.btn.meihua': 'Optimize',

  'work.order.detail.ai.tips1': 'Looking forward to your creation!',
  'work.order.detail.ai.tips2': 'Write down your ideas in the prompt!',

  'work.order.detail.add.associated.work.order': 'Add associated tickets',
  'work.order.detail.work.order.upgrade': 'Ticket escalation',
  'work.order.detail.transfer.assign.work.order': 'Reassign/Assign tickets',
  'work.order.filter.name.add': 'Add Filter',

  'work.order.detail.input.search.placeholder':
    'Enter user information, press Enter to search',

  'work.order.detail.current.level': 'Current Priority:',
  'work.order.detail.escalate.to.level': 'Escalate to:',
  'work.order.detail.replacing.seats': 'Rule agent:',
  'work.order.detail.reason': 'Reason:',
  'work.order.detail.reason.transfer': 'Transfer reason:',
  'work.order.detail.reason.transfer.placeholder':
    'Please enter the transfer reason',
  'work.order.detail.reason.assignment': 'Reason:',
  'work.order.detail.reason.assignment.placeholder':
    'Please enter the assignment reason',
  'work.order.detail.reason.placeholder': 'Please enter the reason',
  'work.order.detail.reason.urging': 'Reminder reason:',
  'work.order.detail.reason.urging.placeholder': 'Please enter the reason',
  'work.order.detail.reason.termination': 'Reason:',
  'work.order.detail.reason.termination.placeholder': 'Please enter the reason',

  'work.order.batch.marking.tips': 'Select at least one row of data!',
  'work.order.filter.name': 'Filter name:',
  'work.order.filter.name.required': 'Please enter the filter name',

  'work.order.management.table.status.text1': 'Pending assignment',
  'work.order.management.table.status.text2': 'Pending agent handling',
  'work.order.management.table.status.text3': 'Pending customer response',
  'work.order.management.table.status.text4': 'Resolved',
  'work.order.management.table.status.text5': 'Terminated',
  'work.order.management.table.status.text6': 'Transferred',

  'work.order.save.filter': 'Save Filter',
  'work.order.reset.filter': 'Reset Filter',
  'work.order.save.as.filter': 'Save Filter As',

  'work.order.ticket.01': 'Waiting list',
  'work.order.ticket.02.1': 'Processing',
  'work.order.ticket.02.2': 'Terminated',
  'work.order.ticket.02.3': 'Transferred order',
  'work.order.ticket.03': 'Resolved',

  'work.order.detail.info': 'Agent:',
  'work.order.detail.custom.info': 'Customer: ',
  'work.order.detail.info.1': 'Priority:',
  'work.order.detail.info.2': 'Source channel: ',
  'work.order.detail.info.3': 'Ticket type: ',
  'work.order.detail.info.4': 'Creation date: ',
  'work.order.detail.info.5': 'Creator:',
  'work.order.detail.info.6': 'Creation Type: ',
  'work.order.detail.info.7': 'Automatic creation',
  'work.order.detail.info.8': 'Manual creation',
  'work.order.detail.info.9': 'Satisfaction evaluation: ',
  'work.order.detail.info.10': 'Channel type: ',

  'pages.home.index.card.no1': 'Ticket summary',
  'pages.home.index.card.no2': 'Ticket type',
  'pages.home.index.card.no3': 'Ticket efficiency',
  'pages.home.index.card.no4': "Tickets I'm handling",
  'pages.home.index.card.no6': 'Pending tickets',
  'pages.home.index.card.no5': 'Ticket backlog',
  'pages.home.index.card.no7': 'Chatbot ticket',

  // AI智能总结
  'work.order.detail.ai.intelligence.summary': 'AI summary',
  'work.order.manual.summary': 'Manual summary',
  'work.order.detail.summary.content': 'Summary',
  'work.order.detail.recap': 'AI recap',
  'work.order.detail.customer.mood': 'Sentiment',
  'work.order.detail.to.do': 'Action items',
  'work.order.detail.add.to.do': ' Add',
  'work.order.CoCo.intelligent.summary': 'CoCo intelligent summary',
  'work.order.service.summary': 'Service summary',

  'work.order.detail.voice.tips': 'Voice file parsing, please try again later.',
  'work.order.detail.effective.content.tips':
    'There is currently no valid content to summarize!',
  'work.order.detail.todo.placeholder': 'Enter to-do and press Enter to save',
  'work.order.detail.todo.empty': 'The to-do content cannot be empty',
  'work.order.detail.loading.summary': 'Loading...',
  'work.order.detail.intelligent.summary.content': 'Content summary',
  'work.order.management.btn.save': 'Save',
  'work.order.service.summary.tips':
    'This consultation has ended. You can make intelligent summary on the detail page of the ticket. Do you need manual summary now?',
  'work.order.service.summary.tips1':
    'This consultation has ended. Click the button below, and CoCo will help you intelligently generate a summary of this ticket and its tasks!',
  'work.order.service.summary.tips2':
    'CoCo will help you intelligently generate a summary of this ticket and its tasks!',
  'work.order.intelligent.summary.btn': 'CoCo intelligent summary',
  'work.order.intelligent.manual.btn': 'Manual summary',
  'work.order.continuing.follow.btn': 'Follow up',
  'work.order.resolved.btn': 'Resolved',
  'work.order.saved.btn': 'Save',
  'work.order.cancel.btn': 'Cancel',
  'work.order.return.list': 'Return List',
  'create.work.order.five.file': 'Upload up to five files!',
  'editorWorkOrder.workRecordTheme': 'Ticket subject',

  'work.order.reply.test.translate': 'Text Translation',
  'work.order.reply.select.language': 'Select target language:',
  'work.order.reply.select.language.required':
    'Please select the target language',

  'work.order.reply.agent.name': '(Agent)',
  'work.order.reply.robot.name': '(Chatbot)',
  'work.order.reply.custom.name': '(Customer)',
  'work.order.reply.agent.name1': 'Agent',
  'work.order.reply.robot.name1': 'Chatbot',
  'work.order.reply.custom.name1': 'Customer',
  'work.order.reply.translated.text': 'Translated text',
  'work.order.reply.hide.content': 'Hide content',
  'work.order.reply.speech.text': 'Speech-to-text',
  'work.order.reply.left.right':
    'Display agent and customer dialogue separately',
  'work.order.reply.show.replies': 'Show replies',
  'work.order.reply.hide.replies': 'Hide replies',
  'work.order.notes.no.data': 'There is currently no comment information.',
  'work.order.voice.no.data':
    'The recording file is currently being synchronized...',
  'work.order.voice.synchronous.btn': 'Re-sync',
  'work.order.detail.intelligent.summary.try': 'Try "CoCo intelligent summary"',
  'work.order.create.notification.tips':
    'If the Email customer is selected, the customer will receive an alert email.',
  'work.order.detail.copy.work.order.code': 'Click to copy the ticket number',
  'work.order.detail.copy.success': 'Successfully copied!',
  'work.order.detail.copy.error': 'Copy failed. Please try again later',
  'work.order.reply.no.data': 'There is currently no data available',
  'work.order.collect.ticket': 'Assign to me',
  'work.order.allocation.ticket': 'Assign tickets',

  'work.order.reply.call.back': 'Call Back',
  'work.order.reply.phone.voice': 'Telephone recording',
  'work.order.reply.email.sender': 'Sender:',
  'work.order.reply.email.sender.required': 'Please select sender',
  'work.order.reply.email.theme': 'Subject: ',
  'work.order.reply.email.theme.required': 'Please fill in the email subject',
  'work.order.reply.email.content': 'Email content:',
  'work.order.reply.email.content.required': 'Please fill in the email content',
  'work.order.reply.chat.voice': 'Online video communication recording',
  'work.order.reply.chat.voice.app': 'APP online video communication recording',
  'work.order.reply.chat.voice.web': 'WEB online video communication recording',
  'work.order.reply.email.tips': 'You can reply to emails here.',
  'work.order.management.table.robot.work.order.yes': 'Yes',
  'work.order.management.table.robot.work.order.no': 'No',

  'work.order.add.tag.modal': 'Add customer tags',
  'work.order.add.tag.name': 'Tags:',
  'work.order.detail.colon': ': ',

  'work.order.ticket.sort.1': 'Sort by Message Arrival Time (Ascending)',
  'work.order.ticket.sort.2': 'Sort by Message Arrival Time (Descending)',
  'work.order.ticket.sort.3': 'Sort by Reply Time (Ascending)',
  'work.order.ticket.sort.4': 'Sort by Reply Time (Descending)',

  'work.order.ticket.options.1': 'Batch reply',
  'work.order.ticket.options.2': 'Batch tag',
  'work.order.ticket.options.3': 'Pin message',
  'work.order.ticket.options.4': 'Unpin message',
  'work.order.ticket.shou.cang': 'Favorite ticket',
  'work.order.ticket.call': 'Call',
  'work.order.add.to.customer.profile': 'Add to customer profile',
  'work.order.ticket.zhuan.pai': 'Reassign ticket',
  'work.order.ticket.jie.jue': 'Resolve ticket',
  'work.order.ticket.jie.jue.btn': 'Resolve',
  'work.order.ticket.guan.lian': 'Associated tickets',
  'work.order.ticket.zhong.zhi': 'Terminate ticket',
  'work.order.ticket.xiang.qing': 'Ticket details',
  'work.order.detail.allocation.rules': 'Assignment rule: ',
  'ticket.assign.specific.team': 'Assigned to a specific team',
  'ticket.assign.specific.agent': 'Assigned to a specific agent',
  'work.order.detail.replacing.team': 'Rule team: ',
  'work.order.detail.replacing.team.allocate': 'Assign team: ',
  'work.order.detail.replacing.seats.allocate': 'Assign agent: ',
  'work.order.detail.transfer.ticket.error':
    'The current agent is offline and cannot be transferred.',
  'work.order.detail.transfer.ticket.dept':
    ' ( {onlineUserCount} people online )',
  'work.order.detail.ai.intelligence.summary.loading': 'Summarizing',
  'work.order.detail.ai.intention.text':
    'Detected the "{incomingIntentName}" intent, executing the "{intelligentAgentName}" intelligent agent.',
  'work.order.detail.customer.entry.intention.reason':
    'Reason for customer call:',
  'work.order.detail.customer.entry.intention.reason.1':
    'Reason for customer call',
  'work.order.detail.deeply.pondered': 'Have thought deeply',
  'create.work.order.assigned.to.me': 'Assigned to me',
  'create.work.order.title.custom.attribute': 'Custom properties for tickets',
  'work.order.create.send.email.customer': 'Send an email to the client',

  'work.order.add.ticket.tag.modal': 'Add ticket tag',
  'work.order.detail.info.note': 'Latest note:',
  'work.order.detail.info.content.summary': 'AIGC summary',
  'work.order.detail.ai.intelligent.form.filling':
    'AI intelligent form filling',
  'work.order.detail.add.tag.tips':
    'Identical standard tags were found in different categories. Please confirm and select the tag you wish to retain. ',

  // 智能质检
  'intelligent.quality.inspection.title': 'AI quality ',
  'intelligent.quality.inspection.select.evaluating': 'Select evaluation form',
  'intelligent.quality.inspection.select.evaluating.required':
    'Please select an evaluation form',
  'intelligent.quality.inspection.total.scope': 'Total score:',
  'intelligent.quality.inspection.export.pdf': 'Export PDF',
  'intelligent.quality.inspection.evaluated.btn': 'Evaluation complete',
  'intelligent.quality.inspection.evaluate.complete':
    'AI quality check complete',
  'intelligent.quality.inspection.operator.add': 'Add',
  'intelligent.quality.inspection.operator.minus': 'Subtract',
  'intelligent.quality.inspection.input.number.placeholder':
    'Enter score and press enter to save',
  'intelligent.quality.inspection.manual.evaluation': 'Manual evaluation',
  'intelligent.quality.inspection.evaluation.remarks': 'Evaluation notes',
  'intelligent.quality.inspection.evaluation.remarks.placeholder':
    'Enter evaluation notes and press enter to save',
  'intelligent.quality.inspection.evaluation.reference.standards':
    'Evaluation criteria reference',
  'intelligent.quality.inspection.score.details': 'AI quality score details',
  'intelligent.quality.inspection.thead.rule': 'Rule',
  'intelligent.quality.inspection.thead.scope': 'Score',
  'intelligent.quality.inspection.total.scope.1': 'Total points:',
  'work.order.quality.inspection.progress': 'Quality check in progress...',
  'work.order.quality.inspection.scope.tips': "Input can't be empty!",
  'work.order.quality.inspection.scope.tips.1': `Please enter an integer from 0 to {value}`,
  'intelligent.quality.inspection.double.click.scope.tips':
    'Double-click to modify score',
  'work.order.quality.inspection.web.socket.tips': 'WebSocket not connected',
  'intelligent.quality.inspection.result.scope': 'Final evaluation score',
  'intelligent.quality.inspection.evaluation.name': 'Evaluation form name:',
  'intelligent.quality.inspection.evaluation.version':
    'Evaluation form version:',
  'intelligent.quality.inspection.evaluation.time': 'Evaluation time:',
  'intelligent.quality.inspection.evaluation.agent': 'Evaluator:',

  'work.table.email.draft.reply': 'Reply draft ',
  'work.table.email.draft.reply.1': ' (Optional) ',
  'work.table.email.draft.reply.btn': 'Generate reply',
};
