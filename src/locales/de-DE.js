import customerInformation from './de-DE/customerInformation';
import selfConfiguration from './de-DE/selfConfiguration';
import orderAmazon from './de-DE/orderAmazon';
import piecemealWhatsApp from './de-DE/piecemealWhatsApp';
import customerDataGroupManagement from './de-DE/customerDataGroupManagement';
import auth from './de-DE/auth';
import sendMsg from '@/locales/de-DE/sendMsg';
import awsAccountSetting from './de-DE/awsAccountSetting';
import customerList from './de-DE/customerList';
import crmWorkRecord from './de-DE/crmWorkRecord';
import channel from './de-DE/channel';
import worktable from './de-DE/worktable';
import userManagement from '@/locales/de-DE/userManagement';
import agentManagement from '@/locales/de-DE/agentManagment';
import knowledgeQA from '@/locales/de-DE/knowledgeQA';
import statistics from '@/locales/de-DE/statistics';
import personal from '@/locales/de-DE/personal';
import home from '@/locales/de-DE/home';
import emailChannelConfiguration from './de-DE/emailChannelConfiguration';
import agentIMChat from './de-DE/agentIMChat';
import feedbackPerformance from './de-DE/feedbackPerformance';
import faceBookConfiguration from './de-DE/faceBookConfiguration';
import weChatConfiguration from './de-DE/weChatConfiguration';
import instagramConfiguration from './de-DE/instagramConfiguration';
import chatVoiceChannelConfiguration from './de-DE/chatVoiceChannelConfiguration';
// import homePage from '@/locales/de-DE/homePage';
import customerExtensionInformation from './de-DE/customerExtensionInformation';
import workOrderManagement from './de-DE/workOrderManagement';
import cloudContactCenter from './de-DE/cloudContactCenter';
import aigc from './de-DE/AIGC';
import homePage from './de-DE/homePage';
import contactCustomers from './de-DE/contactCustomers';
import documentKnowledgeBase from './de-DE/documentKnowledgeBase';
import definitionSynonyms from './de-DE/definitionSynonyms';
import chatChannelConfiguration from './de-DE/chatChannelConfiguration';
import whatsAppChannelConfiguration from './de-DE/whatsAppChannelConfiguration';
import beginnerGuidance from './de-DE/beginnerGuidance';
import marketingActivities from './de-DE/marketingActivities';
import marketingResults from './de-DE/marketingResults';
import emailMarketing from './de-DE/emailMarketing';
import agentWorkloadReport from './de-DE/agentWorkloadReport';
import selfAssessmentDetails from './de-DE/selfAssessmentDetails';
import amazonRegionConfiguration from './de-DE/amazonRegionConfiguration';
import googlePlayConfiguration from './de-DE/googlePlayConfiguration';
import tagManagement from './de-DE/tagManagement';
import alloctionRule from './de-DE/alloctionRule';
import site from './de-DE/site';
import meteringBilling from './de-DE/meteringBilling';

import AIAgent from './de-DE/AIAgent';
import intentionManagement from './de-DE/intentionManagement';
import hotlineKeyIndicators from './de-DE/hotlineKeyIndicators';
import hotlineKeyIndicatorsConfig from './de-DE/hotlineKeyIndicatorsConfig';
import manageAgentStatus from './de-DE/manageAgentStatus';
import apiManage from './de-DE/apiManage';

import header from './de-DE/header';
import footer from './de-DE/footer';
import homePageNew from './de-DE/homePageNew';
import productChannel from './de-DE/productChannel';
import finance from './de-DE/finance';
import retail from './de-DE/retail';
import manufacture from './de-DE/manufacture';
import electronics from './de-DE/electronics';
import newEnergy from './de-DE/newEnergy';
import partner from './de-DE/partner';
import resources from './de-DE/resources';
import callCenter from './de-DE/callCenter';
import productAiAgent from './de-DE/productAiAgent';
import productAssistant from './de-DE/productAssistant';
import dataReport from './de-DE/dataReport';
import aigcCustomerService from './de-DE/aigcCustomerService';
import marketing from './de-DE/marketing';
import smartWorkOrder from './de-DE/smartWorkOrder';
import videoCustomerService from './de-DE/videoCustomerService';
import voiceRobot from './de-DE/voiceRobot';
import settingTicket from './de-DE/settingTicket';
import aiAgentLibrary from './de-DE/aiAgentLibrary';
import privacyPolicy from './de-DE/privacyPolicy';
import cookiePolicy from './de-DE/cookiePolicy';
import inactiveMessageReminder from './de-DE/inactiveMessageReminder';
import productComplianceGuide from './de-DE/productComplianceGuide';
import intelligentFormFilling from './de-DE/intelligentFormFilling';
import joinUs from './de-DE/joinUs';
import cookie from './de-DE/cookie';
import userTerms from './de-DE/userTerms';
import euAiActCompliance from './de-DE/euAiActCompliance';
import gdprCompliance from './de-DE/gdprCompliance';
import workerOffers from './de-DE/workerOffers';
import smartQualityInspection from './de-DE/smartQualityInspection';
import homePageMobile from './de-DE/homePageMobile';
import helpDocument from './de-DE/helpDocument';
import shareComponents from './de-DE/shareComponents';
export default {
  ...piecemealWhatsApp,
  ...workerOffers,
  ...meteringBilling,
  ...orderAmazon,
  ...AIAgent,
  ...weChatConfiguration,
  ...chatVoiceChannelConfiguration,
  ...googlePlayConfiguration,
  ...instagramConfiguration,
  ...customerInformation,
  ...customerDataGroupManagement,
  ...selfConfiguration,
  ...amazonRegionConfiguration,
  ...faceBookConfiguration,
  ...auth,
  ...feedbackPerformance,
  ...sendMsg,
  ...home,
  ...statistics,
  ...aigc,
  ...emailMarketing,
  ...knowledgeQA,
  ...chatChannelConfiguration,
  ...whatsAppChannelConfiguration,
  ...agentWorkloadReport,
  // ...homePage,
  ...awsAccountSetting,
  ...cloudContactCenter,
  ...customerList,
  ...crmWorkRecord,
  ...channel,
  ...agentManagement,
  ...worktable,
  ...userManagement,
  ...personal,
  ...customerExtensionInformation,
  ...workOrderManagement,
  ...homePage,
  ...contactCustomers,
  ...documentKnowledgeBase,
  ...definitionSynonyms,
  ...emailChannelConfiguration,
  ...beginnerGuidance,
  ...marketingActivities,
  ...marketingResults,
  ...agentIMChat,
  ...selfAssessmentDetails,
  ...tagManagement,
  ...alloctionRule,
  ...site,
  ...intentionManagement,
  ...hotlineKeyIndicators,
  ...hotlineKeyIndicatorsConfig,
  ...manageAgentStatus,
  ...apiManage,
  ...header,
  ...footer,
  ...homePageNew,
  ...productChannel,
  ...finance,
  ...retail,
  ...manufacture,
  ...electronics,
  ...newEnergy,
  ...partner,
  ...resources,
  ...callCenter,
  ...productAiAgent,
  ...productAssistant,
  ...dataReport,
  ...aigcCustomerService,
  ...marketing,
  ...smartWorkOrder,
  ...videoCustomerService,
  ...voiceRobot,
  ...settingTicket,
  ...aiAgentLibrary,
  ...privacyPolicy,
  ...cookiePolicy,
  ...inactiveMessageReminder,
  ...productComplianceGuide,
  ...joinUs,
  ...intelligentFormFilling,
  ...cookie,
  ...userTerms,
  ...euAiActCompliance,
  ...gdprCompliance,
  ...workerOffers,
  ...smartQualityInspection,
  ...homePageMobile,
  ...helpDocument,
  ...shareComponents,
};
