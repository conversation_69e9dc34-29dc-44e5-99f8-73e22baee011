export default {
  'help.document.title': 'Hilfedokumentation',
  'help.document.whats.app.title': 'Whatsapp business account registrieren',
  'help.document.line.title': 'Line-kanal registrieren',
  'help.document.we.chat.official.title':
    'Wechat-offiziellen account-kanalinformationen konfigurieren',

  // line帮助文档
  'help.document.line.left.menu': 'Line-offiziellen account erstellen',
  'help.document.line.left.menu.1':
    'Line-kanal-konfigurationsinformationen abrufen',
  'help.document.line.left.menu.2': 'Line in connectnow integrieren',
  'help.document.line.step.1.title': 'Schritt 1:',
  'help.document.line.step.2.title': 'Schritt 2:',
  'help.document.line.step.3.title': 'Schritt 3:',
  'help.document.line.step.4.title': 'Schritt 4:',
  'help.document.line.step.5.title': 'Schritt 5:',
  'help.document.line.step.6.title': 'Schritt 6:',
  'help.document.line.step.7.title': 'Schritt 7:',
  'help.document.line.step.9.title': 'Schritt 8:',
  'help.document.line.step.10.title': 'Schritt 9:',
  'help.document.line.step.1.text.1':
    'Link öffnen: <a>https://tw.linebiz.com/account/</a>, klicken sie auf „kostenloses konto erstellen“',
  'help.document.line.step.1.text.2':
    'Wählen sie die anmeldung mit einem line-konto oder einem geschäftskonto',
  'help.document.line.step.1.text.3':
    'Wenn sie unsicher sind, welche anmeldeoption sie verwenden sollen, lesen sie bitte zuerst die erklärung auf der line-offiziellen website:<a>„was ist der unterschied zwischen der anmeldung mit einem line-konto und der anmeldung mit einem geschäftskonto?“</a>',
  'help.document.line.step.1.text.4':
    'Nach dem anmelden füllen sie die informationen des line-offiziellen accounts aus, lesen sie die „line officiellen account-nutzungsbedingungen“ und klicken sie dann auf „bestätigen“',
  'help.document.line.step.1.text.5':
    'Nachdem sie die korrektheit der eingaben bestätigt haben, klicken sie auf „submit“',
  'help.document.line.step.1.text.6':
    'Wenn der folgende bildschirm angezeigt wird, bedeutet dies, dass die bewerbung für den line-offiziellen account erfolgreich war',
  'help.document.line.step.2.text':
    'Wenn sie bereits einen line-offiziellen account haben, klicken sie auf <a>line official account manager</a>, um sich anzumelden',
  'help.document.line.step.2.text.1':
    'Melden sie sich bei „line official account manager“ an und wählen sie zuerst den line-offiziellen account aus, den sie integrieren möchten:',
  'help.document.line.step.2.text.2': 'Klicken sie auf „einstellungen“',
  'help.document.line.step.2.text.3':
    'Wählen sie links „messaging API“ aus und klicken sie dann auf dieser messaging API-seite auf „messaging API aktivieren“',
  'help.document.line.step.2.text.4':
    'Nach dem klicken auf „messaging API aktivieren“ müssen sie zuerst einen bereits erstellten dienstanbieter auswählen oder einen neuen dienstanbieter (provider) erstellen. nach der auswahl lesen sie bitte die „line official account API-nutzungsbedingungen“ sorgfältig durch und klicken sie dann auf „zustimmen“.',
  'help.document.line.step.2.text.5':
    'Füllen sie anschließend die „URL der datenschutzrichtlinie und der nutzungsbedingungen (optional)“ aus. wenn sie nichts eingeben, können sie auch auf „bestätigen“ klicken, um zum nächsten schritt zu gelangen',
  'help.document.line.step.2.text.6':
    'Bestätigen sie, dass der „kontoname“ und der „dienstanbietername“ korrekt sind, und klicken sie dann auf „bestätigen“, um die messaging API offiziell zu aktivieren',
  'help.document.line.step.2.text.7':
    'Nach dem klicken auf „bestätigen“ erscheint der folgende bildschirm und der status wird als „in verwendung“ angezeigt',
  'help.document.line.step.2.text.8':
    'Als nächstes können sie auf „line developers“ klicken, um weitere für die integration benötigte informationen zu erhalten!',
  'help.document.line.step.2.text.9':
    'Um line in das ConnectNow-backend zu integrieren, müssen sie die folgenden 5 informationen kopieren:',
  'help.document.line.step.2.text.10': '- App name',
  'help.document.line.step.2.text.11': '- Channel id',
  'help.document.line.step.2.text.12': '- Channel secret',
  'help.document.line.step.2.text.13': '- Channel access token (long lived)',
  'help.document.line.step.2.text.14': '- Line offizieller account id',
  'help.document.line.step.2.text.15':
    'Klicken sie auf der messaging API-seite auf „line developers“, um darauf zuzugreifen',
  'help.document.line.step.2.text.16':
    'Tipp: die channel id und das channel secret werden auf der messaging API-seite angezeigt und können auch von dieser seite kopiert werden',
  'help.document.line.step.2.text.17':
    'Nach dem zugriff auf line developers klicken sie zuerst auf das avatar-symbol oben rechts und wählen dann „line-konto“ aus',
  'help.document.line.step.2.text.18':
    'Wählen sie von der linken seite im admin-bereich den provider (dienstanbieter) aus',
  'help.document.line.step.2.text.19':
    'Klicken sie dann auf „channel“ (offizieller account), um zur einstellungsseite zu gelangen',
  'help.document.line.step.2.text.20':
    'Nachdem sie die einstellungsseite aufgerufen haben, rufen sie die für die integration erforderlichen daten aus den beiden tabs „basic settings“ und „messaging API“ ab',
  'help.document.line.step.2.text.21':
    'Die ersten drei punkte können in den „basic settings“ abgerufen werden',
  'help.document.line.step.2.text.22': 'Wechseln sie zum messaging API-tab',
  'help.document.line.step.2.text.23':
    'Scrollen sie die seite nach unten und klicken sie auf die „issue“-schaltfläche, um den channel access token (long lived) zu erhalten',
  'help.document.line.step.2.text.24':
    'Nach dem klicken können sie die informationen zum channel access token (long-lived) sehen',
  'help.document.line.step.2.text.25': 'Line-offiziellen account id abrufen',
  'help.document.line.step.2.text.26':
    'Greifen sie auf das backend des line-offiziellen accounts zu: <a>https://manager.line.biz</a>',
  'help.document.line.step.2.text.27':
    'Kopieren sie den rot markierten text im folgenden bild, um die line id des line-offiziellen accounts zu erhalten (ohne @)',
  'help.document.line.step.3.text':
    'Melden sie sich mit dem administratorkonto bei der connectnow-plattform an, klicken sie auf „kanal-setup“-> „line“-> „hinzufügen“',
  'help.document.line.step.3.text.1': 'Klicken sie auf „kanal hinzufügen“',
  'help.document.line.step.3.text.2':
    'Nachdem sie den kanalnamen eingegeben haben, klicken sie auf „weiter“. die folgende seite wird angezeigt. fügen sie bitte die 5 soeben erhaltenen daten in die entsprechenden eingabefelder ein.',
  'help.document.line.step.3.text.3':
    'Nach abschluss der eingabe klicken sie auf „weiter“, um die relevanten einstellungen für den bot vorzunehmen',
  'help.document.line.step.3.text.4': 'Webhook-adresse abrufen',
  'help.document.line.step.3.text.5':
    'Kehren sie zum messaging API-tab von line developers zurück',
  'help.document.line.step.3.text.6':
    'Suchen sie „webhook settings“ und klicken sie hier bei „webhook url“ auf „bearbeiten“',
  'help.document.line.step.3.text.7':
    'Fügen sie die soeben im connectnow-backend kopierte line webhook-url ein und klicken sie auf „update“',
  'help.document.line.step.3.text.8':
    'Drücken Sie "Verifizieren", um sicherzustellen, dass Sie "Erfolg" sehen.',
  'help.document.line.step.3.text.9':
    'Hinweis: stellen sie sicher, dass sie erst auf „abschließen“ auf der connectnow-plattform klicken, bevor sie „verify“ durchführen, da dies sonst zu einem fehler führen kann.',
  'help.document.line.step.3.text.10': 'Use webhook-schalter öffnen',
  'help.document.line.step.3.text.11':
    'Wenn sie den „use webhook“-schalter in line developers nicht aktivieren können, können sie dies in den „antworteinstellungen“ des line OA-backends anpassen und das kästchen „aktivieren“ bei webhook markieren',
  'help.document.line.step.3.text.12':
    'Wenn sie nachrichten nicht im nativen line OA backend, sondern nur im ConnectNow backend beantworten, nehmen sie bitte die folgenden einstellungen im line OA backend vor:',
  'help.document.line.step.3.text.13':
    '- „Chat-schalter“ ausschalten; webhook aktivieren',
  'help.document.line.step.3.text.14':
    'Wenn sie nachrichten von kunden im line OA backend beantworten müssen, stellen sie sicher, dass sie die folgenden einstellungen in den „antworteinstellungen“ des line OA vornehmen:',
  'help.document.line.step.3.text.15': '- „Chat-schalter“ einschalten;',
  'help.document.line.step.3.text.16': '- Webhook aktivieren',
  'help.document.line.step.3.text.17':
    '- „Willkommensnachricht beim hinzufügen als freund“ ausschalten (um zu vermeiden, dass die willkommensnachrichtenfunktionen auf line OA und ConnectNow gleichzeitig ausgelöst werden)',
  'help.document.line.step.3.text.18':
    '- „Antwortzeit“ ausschalten (um zu vermeiden, dass die offline-echtzeit-nachrichtenfunktionen auf line OA und ConnectNow gleichzeitig ausgelöst werden)',
  'help.document.line.step.3.text.19':
    '- Wählen sie bei „gesprächs-antwortmethode“ unbedingt „manuell“ aus (um zu vermeiden, dass die bot-antworten, automatischen schlüsselwortantworten und willkommensnachrichten auf line OA und ConnectNow gleichzeitig ausgelöst werden)',
  'help.document.line.step.3.text.20':
    'Fertig! ihr line wurde erfolgreich in connectnow integriert',

  // 微信公众号帮助文档
  'help.document.we.chat.official.left.menu':
    'Bei der wechat-offiziellen plattform anmelden',
  'help.document.we.chat.official.left.menu.1':
    'Wechat-offiziellen account-id abrufen',
  'help.document.we.chat.official.left.menu.2':
    'Connectnow-kanal-konfiguration',
  'help.document.we.chat.official.step.1.text':
    'Melden sie sich bei der wechat-offiziellen plattform an <a>zur öffentlichen plattform</a>',
  'help.document.we.chat.official.step.1.text.1':
    'Öffnen sie wechat auf ihrem mobiltelefon, klicken sie auf das „+“-symbol oben rechts, wählen sie „scannen“ aus. nachdem sie den QR-code gescannt und die organisation ausgewählt haben, bei der sie sich anmelden möchten, wird die offizielle plattform automatisch weitergeleitet',
  'help.document.we.chat.official.step.2.text':
    'Um die öffentliche account-ID abzurufen, wählen sie im menü auf der linken seite „einstellungen und entwicklung“ -> „öffentliche account-einstellungen“ und scrollen sie nach unten, um die ursprüngliche id zu finden',
  'help.document.we.chat.official.step.2.text.1':
    'App ID, app secret abrufen, wählen sie im menü auf der linken seite „einstellungen und entwicklung“ -> „grundlegende konfiguration“',
  'help.document.we.chat.official.step.3.text':
    'Parameter über die connectnow-kanal-konfiguration abrufen',
  'help.document.we.chat.official.step.3.text.1':
    'Geben sie die abgerufenen konfigurationen in die öffentliche plattform ein und klicken sie auf „konfiguration ändern“',
  'help.document.we.chat.official.step.3.text.2':
    'Konfigurieren sie die in schritt 4 der connectnow-kanal-konfiguration abgerufenen parameter in den entsprechenden parametern',
  'help.document.we.chat.official.step.3.text.3': '',

  // WhatsApp帮助文档
  'help.document.whats.app.left.menu':
    'Vorzubereitende unterlagen vor dem start',
  'help.document.whats.app.left.menu.1': 'Neues konto erstellen',
  'help.document.whats.app.left.menu.18':
    'Klicken sie den eingang zur erstellung',
  'help.document.whats.app.left.menu.2': 'Facebook anmelden',
  'help.document.whats.app.left.menu.3': 'Vereinbarung bestätigen',
  'help.document.whats.app.left.menu.4': 'Bm-konto erstellen',
  'help.document.whats.app.left.menu.5': 'Waba erstellen',
  'help.document.whats.app.left.menu.6':
    'Waba- und nummerninformationen einrichten',
  'help.document.whats.app.left.menu.7': 'Mobiltelefonnummer binden',
  'help.document.whats.app.left.menu.8':
    'Verifizierung des verifizierungscodes',
  'help.document.whats.app.left.menu.9': 'Zweitbestätigung',
  'help.document.whats.app.left.menu.10': 'Erfolgreich erstellt',
  'help.document.whats.app.left.menu.11': 'Erfolgreich gebunden',
  'help.document.whats.app.left.menu.12': 'Kanal-hinzufügen abgeschlossen',
  'help.document.whats.app.left.menu.13': 'Unternehmenszertifizierung',
  'help.document.whats.app.left.menu.14':
    'Vorbereitung der geschäftsvalidierung',
  'help.document.whats.app.left.menu.15': 'Nachweisdokumente hochladen',
  'help.document.whats.app.left.menu.16': 'Kontaktmethode auswählen',
  'help.document.whats.app.left.menu.17': 'Auf das validierungsergebnis warten',
  'help.document.whats.app.step.1.table.title': 'Materialien vorbereiten',
  'help.document.whats.app.step.1.table.title.1': 'Beispiel',
  'help.document.whats.app.step.1.table.title.2': 'Spezifische anforderungen',
  'help.document.whats.app.step.1.table.body': 'Facebook-personenkonto',
  'help.document.whats.app.step.1.table.body.1': '-',
  'help.document.whats.app.step.1.table.body.2':
    'Ein mindestens einen monat altes konto (sie können vor dem start den link anklicken, um zu überprüfen, ob das konto normal ist), das zum erstellen des meta business manager (kurz: bm) verwendet wird',
  'help.document.whats.app.step.1.table.body.3': 'Mobiltelefonnummer',
  'help.document.whats.app.step.1.table.body.4': '+1 ***********',
  'help.document.whats.app.step.1.table.body.5':
    'In der lage sein, SMS mit verifizierungscodes zu empfangen; diese nummer darf zuvor weder für eine whatsapp-app noch für ein geschäftskonto registriert gewesen sein (falls sie zuvor für ein privates whatsapp-konto registriert war, muss dieses zuerst gekündigt werden, bevor sie diese nummer verwenden können); wird zum erstellen des whatsapp-geschäftskontos verwendet.',
  'help.document.whats.app.step.1.table.body.5.1':
    'Hinweis: mobiltelefonnummern aus festlandchina sind ebenfalls zulässig',
  'help.document.whats.app.step.1.table.body.6':
    'Whatsapp business-anzeigename',
  'help.document.whats.app.step.1.table.body.7': 'Connectnow',
  'help.document.whats.app.step.1.table.body.8':
    'Muss mit der offiziellen website der marke in verbindung stehen und wird zur benennung des whatsapp-geschäftskontos verwendet',
  'help.document.whats.app.step.1.table.body.9': 'Firmenname',
  'help.document.whats.app.step.1.table.body.10': 'Connectnow',
  'help.document.whats.app.step.1.table.body.11':
    'Muss genau mit dem firmennamen auf dem handelsregisterauszug oder der registrierungsbescheinigung übereinstimmen',
  'help.document.whats.app.step.1.table.body.12': 'Firmenadresse',
  'help.document.whats.app.step.1.table.body.13':
    '7500a beach road #04-307 the plaza singapore 19959',
  'help.document.whats.app.step.1.table.body.14':
    'Muss genau mit der adresse auf dem handelsregisterauszug oder der registrierungsbescheinigung übereinstimmen',
  'help.document.whats.app.step.1.table.body.15':
    'Handelsregisterauszug oder registrierungsbescheinigung',
  'help.document.whats.app.step.1.table.body.16': 'Business_pofile.pdf',
  'help.document.whats.app.step.1.table.body.17':
    'Der firma entsprechende handelsregisterauszug oder registrierungsdokument',
  'help.document.whats.app.step.1.table.body.18': 'Marken-website',
  'help.document.whats.app.step.1.table.body.19': 'Www.connectnowai.com',
  'help.document.whats.app.step.1.table.body.20':
    '1.die webadresse muss HTTPS-verschlüsselt sein;',
  'help.document.whats.app.step.1.table.body.20.1':
    '2.der inhalt der website muss das geschäft des unternehmens klar ausdrücken;',
  'help.document.whats.app.step.1.table.body.20.2':
    '3.am fuß der website müssen firmenname und adresse angegeben sein, z.b.: copyright @ xxxx (aktuelles jahr) +firmenname all rights reserved',
  'help.document.whats.app.step.1.table.body.21': 'Firmen-e-mail-adresse',
  'help.document.whats.app.step.1.table.body.22': '<EMAIL>',
  'help.document.whats.app.step.1.table.body.23':
    'Die e-mail-suffix des unternehmens muss mit der domain der markenwebsite übereinstimmen. z.b. www.connectnowai.<NAME_EMAIL> entsprechen den anforderungen und werden verwendet, um bei der unternehmenszertifizierung eine einmalige verifizierungscode-e-mail zu erhalten',
  'help.document.whats.app.step.1.text':
    'Hinweis: wenn sie bereits ein BM-konto (facebook business manager) haben und ein whatsapp API-konto unter diesem bestehenden BM-konto erstellen möchten, können sie sich in schritt 2 des folgenden prozesses mit dem facebook-konto des BM-kontoadministrators anmelden und in schritt 4 das bestehende BM-konto auswählen.',
  'help.document.whats.app.step.2.text':
    'Nachdem sie die oben genannten materialien vorbereitet haben, können sie sich als administrator bei connectnow anmelden und im linken menü auf „kanal-setup“-> „whatsapp“-> „hinzufügen“-> „whatsapp verbinden“ klicken, um den integrierten registrierungsprozess zu starten',
  'help.document.whats.app.step.2.text.1':
    'Klicken sie auf „kanal-setup“, scrollen sie nach unten, um whatsapp zu finden, und klicken sie dann auf „konfiguration starten“',
  'help.document.whats.app.step.2.text.2':
    'Rufen sie die oberfläche für die „whatsapp-kanal-konfiguration“ auf und klicken sie auf „kanal hinzufügen“',
  'help.document.whats.app.step.2.text.3':
    'Rufen sie die seite „whatsapp-kanal hinzufügen“ auf, klicken sie auf „whatsapp-konto verbinden“, um zur facebook-anmeldeseite zu gelangen',
  'help.document.whats.app.step.2.text.4':
    'Nach dem klicken auf „whatsapp-konto verbinden“ erscheint ein facebook-autorisierungsfenster. bitte melden sie sich mit ihrem vorbereiteten facebook-konto an. falls das konto nicht korrekt ist, können sie auf „log in another account“ klicken, um zu wechseln. nachdem sie bestätigt haben, dass das konto korrekt ist, klicken sie auf „weiter“, um mit dem nächsten schritt fortzufahren.',
  'help.document.whats.app.step.2.text.5':
    'Nach bestätigung der korrektheit klicken sie auf „starten“, um mit der erstellung fortzufahren.',
  'help.document.whats.app.step.2.text.6':
    'Geben sie ihre firmeninformationen ein:',
  'help.document.whats.app.step.2.text.7':
    '*Firmenname: der firmenname muss genau mit dem namen auf dem handelsregisterauszug übereinstimmen; keine abkürzungen oder markennamen verwenden.',
  'help.document.whats.app.step.2.text.8':
    '*Firmen-e-mail: es wird empfohlen, eine e-mail-adresse mit derselben website-domain zu verwenden.',
  'help.document.whats.app.step.2.text.9':
    '*Firmenwebsite oder geschäfts-homepage: firmen-URL. beachten sie, dass die firmen-URL HTTPS-protokollbasiert sein muss.',
  'help.document.whats.app.step.2.text.10':
    '*Land/region: das land, in dem das unternehmen tätig ist. stellen sie sicher, dass sie das land auswählen, zu dem das unternehmen gehört, das sie zertifizieren möchten. wenn sie beispielsweise bei der unternehmenszertifizierung eine indonesische geschäftslizenz einreichen, wählen sie indonesien als land aus.',
  'help.document.whats.app.step.2.text.11':
    'Hinweis: wenn sie bereits ein BM-konto haben, können sie es unter der option „business portfolio“ auswählen.',
  'help.document.whats.app.step.2.text.12':
    'Wählen/erstellen sie ein neues WABA. wenn bereits ein WABA vorhanden ist, können sie es im drop-down-menü auswählen, andernfalls wählen sie „erstellen“.',
  'help.document.whats.app.step.2.text.13':
    '*Whatsapp business geschäftskontoname: name des WABA. wird zur internen geschäftsunterscheidung verwendet, ihre zielgruppe sieht diese information nicht in ihrem whatsapp-kontoprofil.',
  'help.document.whats.app.step.2.text.14':
    '*Whatsapp-anzeigename: der name der nummer. der name, den der kunde letztendlich sieht, muss mit dem firmennamen oder dem markennamen in verbindung stehen. klicken sie hier, um die richtlinien für den anzeigenamen anzuzeigen.',
  'help.document.whats.app.step.2.text.15': '*Kategorie: branche',
  'help.document.whats.app.step.2.text.16':
    'Geben sie die mobiltelefonnummer ein, die zur registrierung des whatsapp API-kontos verwendet werden soll, und wählen sie die methode zum abrufen des verifizierungscodes:',
  'help.document.whats.app.step.2.text.17': '1. SMS',
  'help.document.whats.app.step.2.text.18': '2. sprachanruf',
  'help.document.whats.app.step.2.text.19':
    'Nach abschluss der eingabe klicken sie auf „weiter“.',
  'help.document.whats.app.step.2.text.20':
    'Tipp: für das festlandchinesische gebiet (+86) wird empfohlen, einen sprachanruf zu verwenden, um den verifizierungscode zu erhalten.',
  'help.document.whats.app.step.2.text.21':
    'Geben sie den erhaltenen verifizierungscode ein und klicken sie auf „next“',
  'help.document.whats.app.step.2.text.22':
    'Bestätigen sie den inhalt ihrer erstellung, und wenn alles korrekt ist, klicken sie auf „weiter“',
  'help.document.whats.app.step.2.text.23':
    'Die meldung „erstellung erfolgreich“ wird angezeigt. stellen sie sicher, dass sie auf die schaltfläche „fertig“ unter dem pop-up-fenster klicken.',
  'help.document.whats.app.step.2.text.24':
    'Wenn das pop-up-fenster geschlossen wird, führt connectnow die verknüpfung durch.',
  'help.document.whats.app.step.2.text.25': 'Nummer auswählen',
  'help.document.whats.app.step.2.text.26': 'Roboter einrichten',
  'help.document.whats.app.step.2.text.27':
    'Nachdem sie den kanalnamen eingegeben und auf „fertigstellen“ geklickt haben, ist die registrierung des whatsapp business account abgeschlossen',
  'help.document.whats.app.step.3.text':
    'Bereiten sie ihr BM-konto vor und verifizieren sie es, um täglich unbegrenzt nachrichten auf whatsapp zu senden.',
  'help.document.whats.app.step.3.text.1':
    'Die BM-unternehmenszertifizierung ist ein prozess, der darauf abzielt zu überprüfen, ob das BM-konto zu einer echten organisation gehört.',
  'help.document.whats.app.step.3.text.2':
    'Wenn sie die BM-unternehmenszertifizierung noch nicht abgeschlossen haben, werden sie bei der verwendung der whatsapp business API eingeschränkt sein, einschließlich:',
  'help.document.whats.app.step.3.text.3':
    'Jede telefonnummer kann innerhalb von 24 stunden rollierend 250 einzigartigen kunden geschäftlich initiierte gespräche senden.',
  'help.document.whats.app.step.3.text.4':
    'Maximal 2 telefonnummern registrieren.',
  'help.document.whats.app.step.3.text.5':
    'Nach abschluss der unternehmensverifizierung und der überprüfung des anzeigenamens, hat ihr unternehmen die möglichkeit, beschränkungen schnell aufzuheben:',
  'help.document.whats.app.step.3.text.6':
    'Geschäftlich initiierte gespräche auf mehr kunden ausdehnen: beginnend mit 1.000 einzigartigen kunden in einem rollierenden 24-stunden-zeitraum, schrittweise erhöht auf 10.000, 100.000 oder unbegrenzt pro telefonnummer.',
  'help.document.whats.app.step.3.text.7':
    'Auf unbegrenzte vom kunden initiierte gespräche antworten.',
  'help.document.whats.app.step.3.text.8':
    'Beantragung eines offiziellen unternehmenskontos (OBA).',
  'help.document.whats.app.step.3.text.9':
    'Zusätzliche telefonnummern registrieren (maximal 20 pro BM).',
  'help.document.whats.app.step.3.text.10':
    'Ab april 2024 können unternehmen, nachdem sie die nachrichtenqualitätsstandards erfüllt und die überprüfung des anzeigenamens abgeschlossen haben, ihren namen im chat anzeigen, wodurch das vertrauen der kunden gestärkt wird, ohne dass eine unternehmensverifizierung erforderlich ist. siehe meta-dokumentation:',
  'help.document.whats.app.step.3.text.11':
    'Https://developers.facebook.com/docs/whatsapp/messaging-limits#open-1k-conversations-in-30-days',
  'help.document.whats.app.step.3.text.12':
    'Um die chancen ihres unternehmens zu erhöhen, die meta-verifizierung zu bestehen, müssen sie die folgenden informationen im voraus bestätigen:',
  'help.document.whats.app.step.3.text.13':
    'Die offizielle website-adresse des unternehmens: muss HTTPS-verschlüsselt sein und den namen, die adresse oder die telefonnummer des unternehmens enthalten',
  'help.document.whats.app.step.3.text.14':
    'E-mail-adresse mit derselben domain wie die unternehmenswebsite: sie wird verwendet, um eine einmalige verifizierungs-e-mail zu erhalten (nicht erforderlich bei domain- oder mobiltelefonnummer-authentifizierung, aber im allgemeinen empfehlen wir die e-mail-authentifizierung)',
  'help.document.whats.app.step.3.text.15':
    'Offizielle dokumente, die den rechtlichen namen des unternehmens enthalten: z.b. handelsregisterauszug, gesellschaftsvertrag oder steuerbescheinigung',
  'help.document.whats.app.step.3.text.16':
    'Sie müssen sicherstellen, dass der in den dokumenten enthaltene firmenname mit der offiziellen unternehmenswebsite in verbindung steht, z.b. durch eingabe von „gehört zu firma abc“ im fußbereich der seite',
  'help.document.whats.app.step.3.text.17': 'Verifizierungsprozess',
  'help.document.whats.app.step.3.text.17.1': '1. verifizierungsprozess',
  'help.document.whats.app.step.3.text.18':
    'Wenn sie der administrator eines meta business manager-kontos sind (nach abschluss von kapitel 2 werden sie automatisch zum administrator des meta business manager-kontos), können sie die folgenden schritte befolgen, um die verifizierung ihres unternehmens zu starten:',
  'help.document.whats.app.step.3.text.19':
    'Gehen sie zum abschnitt<a>„sicherheitscenter“</a> der business manager-plattform.',
  'help.document.whats.app.step.3.text.20':
    'Wenn sie die schaltfläche „verifizieren“ nicht sehen, besuchen sie bitte die connectnow-plattform und schließen sie den integrierten registrierungsprozess ab (d.h. kapitel 2).',
  'help.document.whats.app.step.3.text.21':
    '2. grundlegende organisationinformationen einreichen',
  'help.document.whats.app.step.3.text.22':
    'Geben sie den namen, die adresse, die telefonnummer und die website ihres unternehmens an',
  'help.document.whats.app.step.3.text.23': 'Best practices',
  'help.document.whats.app.step.3.text.24':
    'Der von ihnen eingegebene firmenname/die adresse muss mit dem namen/der adresse auf den nachweisdokumenten übereinstimmen.',
  'help.document.whats.app.step.3.text.25':
    'Die telefonnummer kann eine private mobiltelefonnummer sein (aber in den folgenden schritten kann das unternehmen nicht über die mobiltelefonnummer verifiziert werden)',
  'help.document.whats.app.step.3.text.26':
    'Die übermittelte website muss textinhalte aufweisen, die den domainbesitz belegen, z.b. durch die eingabe von „gehört zu firma abc“ im fußbereich der seite; dieser firmenname muss mit dem von ihnen eingegebenen firmennamen übereinstimmen.',
  'help.document.whats.app.step.3.text.27':
    'Nach dem hochladen die seiteninformationen installieren und zur verifizierung einreichen',
  'help.document.whats.app.step.3.text.28': 'Best practices',
  'help.document.whats.app.step.3.text.29':
    'Bevorzugt ist die e-mail-verifizierung, aber das suffix ihrer e-mail-adresse muss mit der eingereichten domain übereinstimmen (www.mypage.com >> <EMAIL>).',
  'help.document.whats.app.step.3.text.30':
    'Die domain-verifizierung ist die nächste option zur verifizierung ihres unternehmens.',
  'help.document.whats.app.step.3.text.31':
    'Geben sie den verifizierungscode ein und klicken sie dann auf „weiter“, bis zum letzten schritt, um die verifizierung einzureichen',
  'help.document.whats.app.step.3.text.32':
    'Nach dem einreichen der verifizierung kann die entscheidung in frühestens 10 minuten und spätestens 14 werktagen getroffen werden. sie werden nach abschluss der überprüfung benachrichtigt. wenn sie eine bestätigung erhalten, dass die verifizierung erfolgreich war, müssen sie nichts weiter tun.',
};
