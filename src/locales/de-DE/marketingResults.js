export default {
  // 营销结果
  'marketing.results.activity.name.placeholder':
    'Bitte wählen sie den aktivitätsnamen',
  'marketing.results.marketing.channel.type': 'Marketingkanaltyp:',
  'marketing.results.marketing.channel.type.placeholder':
    'Bitte marketingkanaltyp auswählen',
  'marketing.results.marketing.event.name': 'Marketingereignisname:',
  'marketing.results.marketing.event.name.placeholder':
    'Bitte wählen sie den marketingereignisnamen',

  'marketing.results.table.activity.name': 'Aktivitätsname',
  'marketing.results.table.marketing.channel.type': 'Marketingkanaltyp',
  'marketing.results.table.send.channel': 'Sendekanal',
  'marketing.results.table.marketing.events': 'Marketingereignis',
  'marketing.results.table.marketing.methods': 'Marketingmethode',
  'marketing.results.table.test.type': 'A/B-testtyp',
  'marketing.results.table.marketing.event.batches': 'Marketingereignis-charge',
  'marketing.results.table.number.customers': 'Gesamtzahl der kunden',
  'marketing.results.table.delivery.rate': 'Zustellrate',
  'marketing.results.table.read.rate': 'Leserquote',
  'marketing.results.table.click.rate': 'Klickrate',
  'marketing.results.table.subscription.rate': 'Abonnementrate',
  'marketing.results.table.unsubscribe.rate': 'Abbestellrate',
  'marketing.results.table.complaint.rate': 'Beschwerderate',
  'marketing.results.table.complaint.rate1': 'Beschwerderate',
  'marketing.results.table.failure.rate': 'Fehlerrate',

  'marketing.results.table.bounce.rate': 'Bounce-rate',
  'marketing.results.table.delivery.delay.rate': 'Verzögerte zustellrate',
  'marketing.results.table.reject.rate': 'Ablehnungsrate',
  'marketing.results.table.rendering.failure.rate': 'Rendering-fehlerrate',

  'marketing.results.table.operation': 'Betrieb',
  'marketing.results.table.export.customer': 'Kundenliste exportieren',
  'marketing.results.table.customer.list': 'Kundenliste',
  'marketing.results.table.marketing.event.sending.time':
    'Sendezeit des marketingereignisses',

  // 营销详情
  'marketing.results.marketing.event.batches': 'Marketingereignis-charge:',
  'marketing.results.marketing.event.batches.placeholder':
    'Bitte wählen sie die marketingereignis-charge',
  'marketing.results.marketing.methods': 'Marketingmethode:',
  'marketing.results.marketing.methods.1': 'Standardtest',
  'marketing.results.marketing.methods.2': 'A/B-test',
  'marketing.results.marketing.methods.3': 'Plan a',
  'marketing.results.marketing.methods.4': 'Plan b',
  'marketing.results.marketing.methods.placeholder':
    'Bitte wählen sie die marketingmethode',
  'marketing.results.contact.information': 'Kundenkontaktinformationen:',
  'marketing.results.contact.information.placeholder':
    'Bitte geben sie die kundenkontaktinformationen ein',
  'marketing.results.table.customer.name': 'Kundenname',
  'marketing.results.table.customer.contact.information':
    'Kundenkontaktinformationen',
  'marketing.results.table.status': 'Status',
  'marketing.results.table.failure.reason': 'Fehlergrund',
  'marketing.results.table.detail': 'Detail',
  'event.notification.status.service': 'Zugestellt',
  'event.notification.status.read': 'Gelesen',
  'event.notification.status.click': 'Geklickt',
  'event.notification.status.subscribe': 'Abonniert',
  'event.notification.status.unsubscribe': 'Abbestellt',
  'event.notification.status.fail': 'Wiedergabefehler',
  'event.notification.status.complaint': 'Beschwerde',
  'event.notification.status.have.send': 'Gesendet',
  'event.notification.status.bounce': 'Bounce',
  'event.notification.status.reject': 'Abgelehnt',
  'event.notification.status.delivery.delay': 'Lieferverzögerung',

  'marketing.details.customer.information.title': 'Kundenbasisinformationen',
  'marketing.details.activity.information.title':
    'Aktivitätsbasisinformationen',
  'marketing.details.customer.information.customer.name': 'Kundenname:',
  'marketing.details.customer.information.customer.phone': 'Kundenhandy:',
  'marketing.details.customer.information.customer.whats.app':
    'WhatsApp-nummer:',
  'marketing.details.customer.information.customer.email': 'Kunden-e-mail:',
  'marketing.details.customer.information.marketing.result':
    'Aktuelles marketingergebnis:',
  'marketing.details.history.title': 'Historie',

  'marketing.channel.type.email': 'E-mail',
  'marketing.channel.type.all': 'Alle Kanäle',
  'marketing.channel.type.all.small': 'Alle Kanäle',
  'marketing.channel.type.phone': 'Telefon',
  'marketing.channel.type.whats.app': 'WhatsApp',
  'marketing.channel.type.info': 'Sms',
  'marketing.channel.type.chat': 'Web-chat',
  'marketing.channel.type.app.chat': 'App-chat',
  'marketing.channel.type.web.video': 'Web-video',
  'marketing.channel.type.app.video': 'App-video',
  'marketing.channel.type.amazon.message': 'Amazon message',
  'marketing.channel.type.facebook': 'Facebook messenger',
  'marketing.channel.type.instagram': 'Instagram',
  'marketing.channel.type.line': 'Line',
  'marketing.channel.type.weCom': 'Wecom',
  'marketing.channel.type.weChat.official.account': 'WeChat official account',
  'marketing.channel.type.web.online.video': 'Web-online-video',
  'marketing.channel.type.app.online.video': 'App-online-video',
  'marketing.channel.type.twitter': 'Twitter',
  'marketing.channel.type.telegram': 'Telegram',
  'marketing.channel.type.weChat.mini.program': 'WeChat mini program',
  'marketing.channel.type.shopify': 'Shopify',
  'marketing.channel.type.google.play': 'Google play',
  'marketing.channel.type.discord': 'Discord',

  // A/B测试分析页面
  'test.analysis.result.title': 'Analyseergebnis',
  'test.analysis.result.comparison.dimension': 'Vergleichsdimension:',
  'test.analysis.result.comparison.dimension.1': 'Kundengruppe',
  'test.analysis.result.comparison.dimension.2': 'Marketinginhalt',
  'test.analysis.result.comparison.dimension.3': 'Marketingzeit',
  'test.analysis.result.overall.result': 'Gesamtergebnis:',
  'marketing.results.table.delivery.rate.1': 'Zustellrate:',
  'marketing.results.table.read.rate.1': 'Leserquote:',
  'marketing.results.table.click.rate.1': 'Klickrate:',
  'marketing.results.table.subscription.rate.1': 'Abonnementrate:',
  'marketing.results.table.unsubscribe.rate.1': 'Abbestellrate:',
  'marketing.results.table.complaint.rate.1': 'Beschwerderate:',
  'marketing.results.table.failure.rate.1': 'Fehlerrate:',

  'marketing.results.table.bounce.rate.1': 'Bounce-rate:',
  'marketing.results.table.delivery.delay.rate.1': 'Verzögerte zustellrate:',
  'marketing.results.table.reject.rate.1': 'Ablehnungsrate:',
  'marketing.results.table.rendering.failure.rate.1': 'Wiedergabefehlerrate:',

  'test.analysis.semicolon': ';',
  'test.analysis.period': '.',

  'marketing.channel.type.discord': 'Discord',
};
