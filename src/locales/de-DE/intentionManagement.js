export default {
  'intention.management.intent.classification': 'Intent-Klassifizierung:',
  'intention.management.intent.classification.placeholder':
    'Bitte Intent-Klassifizierung wählen',
  'intention.management.intent.language': 'Intent-Sprache:',
  'intention.management.intent.language.placeholder':
    'Bitte Intent-Sprache wählen',
  'intention.management.intent.name': 'Intent-Name:',
  'intention.management.intent.name.placeholder': 'Bitte Intent-Namen eingeben',
  'intention.management.create.intention.text': 'Neuer Intent',
  'intention.management.user.number': '{num}次',
  'intention.management.user.number.month.tips':
    'Nutzungshäufigkeit des aktuellen Monats',
  'intention.management.intent.request.reason': 'Grund der Intent-Anfrage:',
  'intention.management.create.information': 'Informationen erstellen:',
  'intention.management.create.intelligent.agent': 'Neuer Agent',
  'intention.management.intelligent.agent': 'Agent',
  'intention.management.delete.tips':
    '<PERSON><PERSON> sic<PERSON>, dass Sie diesen Inhalt löschen möchten?',

  // 添加意图
  'new.intent.title.add': 'Intent hinzufügen',
  'new.intent.title.edit': 'Intent bearbeiten',
  'new.intent.intention.basic.information': 'Grundlegende Intent-Informationen',
  'new.intent.intention.name': 'Intent-Name',
  'new.intent.intention.name.maxlength':
    'Die Länge darf 40 Zeichen nicht überschreiten',
  'new.intent.intention.language': 'Intent-Sprache',
  'new.intent.intention.classification': 'Intent-Klassifizierung',
  'new.intent.intention.request.reason': 'Grund der Intent-Anfrage',
  'new.intent.intention.request.reason.placeholder':
    'Bitte geben Sie den Grund ein, warum der Kunde diesen Intent ausgelöst hat, z. B. der Kunde fordert eine Abfrage des Bestellstatus an',
  'new.intent.intention.reason.maxlength':
    'Die Länge darf 2000 Zeichen nicht überschreiten',
  'new.intent.intention.basic.information.tips':
    'Intent-Name und Intent-Anfragegrund sind wichtige Grundlagen für AIGC, um Kunden-Intents zu identifizieren.',
  'new.intent.intention. language.information':
    'Informationen zur Intent-Sprache',
  'new.intent.static.language.technique': 'Statische Sprache',
  'new.intent.dynamic.speech.technique': 'Dynamische Sprache',
  'new.intent.static.language.technique.placeholder':
    'Bitte statischen Intent-Sprachtext eingeben',
  'new.intent.dynamic.speech.technique.placeholder':
    'Bitte geben Sie dynamischen Sprachtext ein',
  'new.intent.dynamic.speech.technique.placeholder2': '请输入动态意图话术',
  'new.intent.ai.static.language.technique': 'AI-generierter Intent-Sprachtext',
  'new.intent.add.static.language.technique': 'Intent-Sprache hinzufügen',
  'new.intent.intention.dynamic.speech.technique.tips':
    '动态话术中可包含动态属性，动态属性格式为 {Device}，其中Device为属性名，仅支持英文',
  'new.ai.generate.intent.intention.language': '选择意图话术语言',
  'new.intent.intention.language.attribute': 'Intent-Sprachattribute',
  'new.intent.intention.language.attribute.name': 'Attributname',
  'new.intent.intention.language.attribute.name.placeholder':
    'Bitte Attributnamen eingeben',
  'new.intent.intention.language.attribute.example':
    'Beispiel für Attributwert',
  'new.intent.intention.language.attribute.example.placeholder':
    'Bitte Attributwert eingeben',
  'new.intent.intention.language.attribute.example.tips':
    'Das Attributwertbeispiel dient als wichtige Grundlage für die Extraktion des aktuellen Attributs. Wenn Ihr aktuelles Attribut Device ist, könnte das Beispiel hier Klimaanlage, Luftentfeuchter usw. sein.',
  'new.intent.intention.attribute.description': 'Attributbeschreibung',
  'new.intent.intention.attribute.description.placeholder':
    'Bitte Beschreibung der Variable eingeben',
  'new.intent.intention.attribute.mandatory': 'Ist es ein Pflichtfeld',
  'new.intent.intention.attribute.verify.format':
    'Wählen Sie das Validierungsformat des Attributs',
  'new.intent.intention.attribute.verify.format.placeholder':
    'Bitte Validierungsformat des Attributs wählen',
  'new.intent.intention.attribute.verify.rule.placeholder':
    'Bitte regulären Ausdruck für die Attributvalidierung eingeben',
  'new.intent.intention.attribute.verify.rule.maxlength':
    'Die Länge darf 200 Zeichen nicht überschreiten',
  'new.intent.intention.failed.verification.attempts':
    'Anzahl der Wiederholungsversuche bei fehlgeschlagener Validierung',
  'new.intent.intention.failed.verification.attempts.placeholder':
    'Bitte Anzahl der Validierungen eingeben',
  'new.intent.intention.failed.num.verification.attempts.placeholder':
    'Die maximale Anzahl der Validierungen überschreitet nicht 99',
  'new.intent.intention.reply.after.verification.failure':
    'Antwort nach fehlgeschlagener Validierung',
  'new.intent.intention.reply.after.verification.failure.placeholder':
    'Bitte Antwort nach fehlgeschlagener Validierung eingeben',
  'new.intent.intention.reply.after.verification.failure.final':
    'Antwort nach endgültig fehlgeschlagener Validierung',
  'new.intent.intention.reply.after.verification.failure.final.placeholder':
    'Bitte Antwort nach endgültig fehlgeschlagener Validierung eingeben',
  'new.intent.intelligent.agent.variables': 'In Agent gespeicherte Variablen',
  'new.intent.intelligent.agent.variables.placeholder':
    'Bitte wählen Sie die im Agent zu speichernden Variablen aus',
  'new.intent.intention.rhetorical.question.not.collecting':
    'Rhetorische Frage für nicht erfasste aktuelle Variable',
  'new.intent.intention.rhetorical.question.not.collecting.placeholder':
    'Bitte Sprachtext eingeben',
  'new.intent.add.variables': 'Attribute hinzufügen',
  'new.intent.intention.llm.extract.attribute': 'LLM 提取意图属性',
  'new.intent.llm.extract.attribute.add.variables': 'Attribut hinzufügen',
  'new.intent.add.cancel.confirm':
    'Abbrechen leert das Formular, möchten Sie wirklich abbrechen?',
  'new.intent.add.success.tips':
    'Herzlichen Glückwunsch, Sie haben erfolgreich einen Intent erstellt. Als Nächstes können Sie einen Agent für diesen Intent erstellen',
  'new.intent.add.static.language.technique.tips':
    'Erstellen Sie maximal 100 Intent-Sprachtexte',
  'new.intent.add.attribute.example.tips':
    'Fügen Sie maximal vier Attributinstanzen pro Variable hinzu!',
  'new.intent.add.verbal.variables.tips':
    'Erstellen Sie maximal 4 Sprachvariablen!',
  'new.intent.intention.classification.management':
    'Intent-Klassifizierungsverwaltung',
  'new.intent.intention.classification.management.placeholder':
    'Bitte geben Sie den Namen der Intent-Klassifizierung ein, drücken Sie die Eingabetaste zur Suche oder klicken Sie auf die Schaltfläche zum Hinzufügen',
  'new.intent.add.variables.only.tips':
    'Hinzugefügte Variablen müssen eindeutig sein und dürfen nicht doppelt vorkommen!',
  'new.intent.add.variables.only.tips.1':
    '请配置话术中包含的{variable}变量属性。',
  'new.intent.add.variables.only.tips.2':
    '话术中缺少{variable}变量，请添加对应话术。',
  'new.intent.add.variables.only.tips.3': '话术{script}重复！',
  'new.intent.add.ai.script.create':
    'Intent-Name und Intent-Anfragegrund dürfen nicht leer sein!',
  'new.intent.add.script.create.tips':
    'Hinweis: Eingabetaste generiert automatisch Variablen',
  'new.intent.intention.classification.tips':
    'Intent-Klassifizierung darf nicht leer sein!',
  'new.intent.intention.language.attribute.code': 'Attributcode',
  'new.intent.intention.language.attribute.format.requirement':
    'Attributformatanforderung',
  'new.intent.intention.language.attribute.is.required':
    'Ist es ein Pflichtfeld',
  'new.intent.intention.language.attribute.operation': 'Operation',
  'new.intent.intention.language.attribute.is.required.yes': 'Pflicht',
  'new.intent.intention.language.attribute.is.required.no': 'Nicht Pflicht',
  'new.intent.intention.language.attribute.code.only.support.english':
    'Nur Englisch wird unterstützt',
  'new.intent.intention.language.attribute.code.only.support.english.message':
    'Attributcode nur Englisch unterstützt',
  'new.intent.add.required.one.speech':
    'Es muss mindestens ein Skript vorhanden sein',
  'new.intent.add.dynamic.speech.editing.tips':
    'Bitte speichern Sie die dynamische Sprachtechnik-Intent-Attributvariable, die gerade bearbeitet wird',

  // 智能体列表
  'external.intelligent.agent.delete.synonym.rules': 'Löschhinweis',
  'external.intelligent.agent.intent.language.placeholder':
    'Bitte Intent-Namen wählen',
  'external.intelligent.agent.name': 'Agent-Name:',
  'external.intelligent.agent.name.placeholder': 'Bitte Agent-Namen eingeben',
  'external.intelligent.agent.deployment.status': 'Bereitstellungsstatus:',
  'external.intelligent.agent.deployment.status.placeholder':
    'Bitte Bereitstellungsstatus wählen',
  'external.intelligent.agent.tab.chat': 'Chat',
  'external.intelligent.agent.tab.email': 'E-Mail',
  'external.intelligent.agent.tab.phone': 'Telefon',
  'external.intelligent.agent.create.text': 'Neuer Agent',
  'external.intelligent.agent.use.rule': 'Nutzungsregeln:',
  'external.intelligent.agent.deployment.time': 'Bereitstellungszeit:',
  'external.intelligent.agent.delete.tips':
    'Sind Sie sicher, dass Sie diesen Agent löschen möchten?',
  'external.intelligent.agent.deploy.status.1': 'Bereitgestellt',
  'external.intelligent.agent.deploy.status.2': 'Entwurf',
  'external.intelligent.agent.default.welcome.message':
    'Wenn Kunden über WEB-Online-Chat, APP-Online-Chat, Shopify, WeChat Mini Programme mit dem Unternehmen kommunizieren, wird zuerst der aktuelle Willkommens-Agent ausgeführt',
  'external.intelligent.agent.fallback':
    'Wenn das System keinen Agenten findet, wird der Standard-Intent ausgeführt',
  'intelligent.agent.deployed.text': '已部署，{deployName}',
  'external.intelligent.agent.share.success': 'Kopiervorgang erfolgreich',
  'external.intelligent.agent.share.tips':
    'Hinweis: Dieser Freigabecode ist 24 Stunden gültig. Bitte verwenden Sie ihn vor {expiryTime}.',
  'external.intelligent.agent.share': 'Freigabe',
  'external.intelligent.agent.share.content':
    '【ConnectNow】Sie haben eine AI-Agent-Freigabe von "{username}" erhalten. Ihr exklusiver Erfahrungscode lautet: "{shareCode}". Dieser Code ist 24 Stunden gültig (vor {expiryTime}).',

  // 新建智能体
  'external.intelligent.agent.create.title': 'Neuer Agent',
  'external.intelligent.agent.create.add': 'Agent erstellen',
  'external.intelligent.agent.create.basic.information':
    'Grundlegende Informationen',
  'external.intelligent.agent.create.name': 'Agent-Namen eingeben',
  'external.intelligent.agent.create.channel.type': 'Anwendbarer Kanaltyp',
  'external.intelligent.agent.create.select.scenario':
    'Agentenszenario auswählen',
  'external.intelligent.agent.phone.voice': 'Telefon/Sprache',
  'external.intelligent.agent.create.select.channel.type':
    'Wählen Sie den zutreffenden Kanaltyp aus',
  'external.intelligent.agent.create.select.channel.type.tips':
    'Wählen Sie mindestens einen Kanaltyp',
  'external.intelligent.agent.create.facebook.message': 'Facebook Message',
  'marketing.channel.type.web.voice': 'WEB-Sprache',
  'marketing.channel.type.app.voice': 'APP-Sprache',
};
