export default {
  'add.chat.channel.configuration.title': 'WEB-Online-Chat-Kanal Hinzufügen',
  'add.chat.channel.configuration.title.update':
    'WEB-Online-Chat-Kanal Bearbeiten',

  'app.add.chat.channel.configuration.title.update':
    'APP-Online-Chat-Kanal Ändern',
  'add.chat.channel.configuration.tips':
    'Kunden können das unternehmen einfacher über die WEB-website kontaktieren, und kundenanfragen können schneller beantwortet werden.',
  'app.add.chat.channel.configuration.title':
    'App-Seitigen Online-Chat-Kanal Hinzufügen',
  'app.add.chat.channel.configuration.tips':
    'Kunden können das unternehmen einfacher über die APP-website kontaktieren, und kundenanfragen können schneller beantwortet werden',
  'chat.channel.configuration.title.1':
    'Füllen Sie die grundlegenden Kanalinformationen aus',
  'chat.channel.configuration.title.tips.1':
    'Bitte füllen sie die grundlegenden Kanalinformationen aus',
  'chat.channel.configuration.title.2': 'Erscheinungsbild-Einstellungen',
  'chat.channel.configuration.title.tips.2':
    'Hier können Sie das Chatfenster konfigurieren und Ihr bevorzugtes Logo und Ihre bevorzugte Farbe auswählen.',
  'chat.channel.configuration.title.3': 'Grundeinstellungen',
  'chat.channel.configuration.title.tips.3':
    'Hier können Sie die Grundfunktionen des Chatfensters konfigurieren.',
  'chat.channel.configuration.title.4':
    'Intelligente Kundenservice-Einstellungen',
  'chat.channel.configuration.title.tips.4':
    'Hier können Sie die relevanten Informationen des intelligenten Kundendienstes konfigurieren.',
  'chat.channel.configuration.title.5': 'Bereitstellung',
  'chat.channel.configuration.title.tips.5':
    'Bitte befolgen sie die folgenden anweisungen, um das chatfenster auf ihrer website bereitzustellen.',
  'chat.channel.configuration.channel.name': 'Kanalname',
  'chat.channel.configuration.channel.name.placeholder':
    'Bitte geben Sie den Kanalnamen ein',
  'chat.channel.configuration.chat.types': 'Sprache auswählen',
  'chat.channel.configuration.chat.types.placeholder':
    'Bitte wählen Sie eine Sprache aus',
  'chat.channel.configuration.chat2.logo': 'Firmenlogo',
  'chat.channel.configuration.chat2.logo.message1':
    'Es können nur JPG/PNG-Dateien hochgeladen werden, die 500 KB nicht überschreiten',
  'chat.channel.configuration.chat2.logo.message2':
    '会在聊天框左上角展示，建议上传50*20px的PNG图片',
  'chat.channel.configuration.chat2.chatBoxName.placeholder':
    'Bitte geben Sie den Chatfensternamen ein',
  'chat.channel.configuration.chat2.chatBoxName': 'Chatfenstername',
  'chat.channel.configuration.chat2.chatBoxName.message':
    'Wird in der oberen linken Ecke des Chatfensters angezeigt',
  'chat.channel.configuration.chat2.templete': 'Themenfarbe',
  'chat.channel.configuration.chat2.templete.custom': 'Benutzerdefiniert',
  'chat.channel.configuration.chat2.templete.color': 'Aktuelle Farbe:',
  'chat.channel.configuration.chat2.templete.placeholder':
    'Bitte Farbe auswählen',
  'chat.channel.configuration.chat2.boxColor':
    'Farbe des Kundendienst-Chatfensters',
  'chat.channel.configuration.chat2.userBox': 'Farbe des Benutzer-Chatfensters',
  'chat.channel.configuration.chat2.information.configuration.completed':
    'Sie haben die Einstellungen für das Erscheinungsbild des Chatfensters abgeschlossen. Sie können auf die Schaltfläche "Vorschau" klicken, um eine Vorschau des Stils anzuzeigen.',
  'chat.channel.configuration.work.panels.checkbox': 'Einschalten',
  'chat.channel.configuration.chat3.form': 'Benutzerinformations-Formularseite',
  'chat.channel.configuration.chat3.form.message':
    'Es wird empfohlen, es einzuschalten. Nach dem Einschalten müssen die Benutzer zuerst grundlegende Informationen eingeben, bevor sie weiter kommunizieren können.',
  'chat.channel.configuration.chat3.welcome': 'Ursprüngliche Begrüßung',
  'chat.channel.configuration.chat3.welcome.words': 'Ein-Satz-Begrüßung',
  'chat.channel.configuration.chat3.welcome.words.placeholder':
    'Bitte geben Sie die Begrüßung ein',
  'chat.channel.configuration.chat3.welcome.words.message':
    'Bitte stellen Sie sicher, dass die Einstellung hier mit der im ersten Schritt ausgewählten Sprache übereinstimmt',
  'chat.channel.configuration.chat3.welcome.QA': 'FAQ auslösen',
  'chat.channel.configuration.chat3.welcome.QA.placeholder':
    'Bitte wählen Sie die FAQ aus, die ausgelöst werden soll',
  'chat.channel.configuration.chat3.interval.placeholder':
    'Geben sie die automatische einladungssitzungszeitein',
  'chat.channel.configuration.chat3.welcome.QA.message':
    'Mit FAQ können sie dem benutzer mehrere antworten gleichzeitig senden, nicht nur text, sondern auch bilder und videos. Wenn sie eine bestimmte FAQ ausgewählt haben, antwortet das system automatisch mit der in der FAQ konfigurierten standardantwort, wenn der benutzer sie kontaktiert. wenn sie noch keine FAQ eingerichtet haben, können sie auf klicken',
  'chat.channel.configuration.chat3.welcome.QA.message.1': 'Hier',
  'chat.channel.configuration.chat3.welcome.QA.message.2':
    'Einstellungen vornehmen.',
  'chat.channel.configuration.chat3.talk': 'Sitzung automatisch einladen',
  'chat.channel.configuration.chat3.talk.ge': 'Nach',
  'chat.channel.configuration.chat3.talk.ge2':
    'Sekunden öffnet das system automatisch das chatfenster',
  'chat.channel.configuration.chat3.message':
    'Während der benutzer die website durchsucht, fordert das system den benutzer automatisch in einem popup-fenster auf, sich beraten zu lassen',
  'chat.channel.configuration.chat3.talk.Input':
    'Begrüßungsnachricht für die automatische einladungssitzung',
  'chat.channel.configuration.chat3.talk.Input.placeholder':
    'Bitte geben sie eine begrüßungsnachricht für die automatische einladungssitzung ein',
  'chat.channel.configuration.chat3.talk.Input.message':
    'Legen sie hier die begrüßungsnachricht fest, die angezeigt wird, wenn die automatische einladungssitzung angezeigt wird',

  'chat.channel.configuration.chat3.voice.message':
    'Während der kommunikation mit dem kundendienst kann der benutzer auf die schaltfläche für die videokommunikation unten klicken, um eine sprachkommunikation durchzuführen',
  'chat.channel.configuration.chat3.voice': 'Online-Sprachkommunikation',
  'chat.channel.configuration.chat3.video.message':
    '用户在与客服沟通过程中可以点击下方视频沟通按钮进行视频沟通',
  'chat.channel.configuration.chat3.video': 'Online-Videokommunikation',
  'chat.channel.configuration.chat3.whatsApp.message':
    'Nach dem Öffnen wird der WhatsApp-Eingang unten im Chatfenster angezeigt',
  'chat.channel.configuration.chat3.whatsApp': 'Zeige WhatsApp-Kanal',
  'chat.channel.configuration.chat3.email.message':
    'Nach dem Öffnen wird der E-Mail-Eingang unten im Chatfenster angezeigt',
  'chat.channel.configuration.chat3.email': 'E-Mail-Kanal anzeigen',
  'chat.channel.configuration.chat3.evaluate.message':
    'Nach Beendigung des Chats zeigt das System automatisch eine Zufriedenheitsbewertung an',
  'chat.channel.configuration.chat3.evaluate': 'Zufriedenheitsbewertung',

  'chat.channel.configuration.chat3.email.select': 'Verbundener E-Mail-Kanal',
  'chat.channel.configuration.chat3.email.select.placeholder':
    'Bitte wählen Sie den verknüpften E-Mail-Kanal aus',
  'chat.channel.configuration.chat3.email.select.message':
    'Nach der Verknüpfung mit der E-Mail-Adresse kann der Benutzer direkt auf das E-Mail-Symbol unten im Chatfenster klicken, um Kontakt aufzunehmen',
  'chat.channel.configuration.chat3.WhatsApp.select':
    'Verbundener WhatsApp-Kanal',
  'chat.channel.configuration.chat3.WhatsApp.select.placeholder':
    'Bitte wählen Sie den verknüpften WhatsApp-Kanal aus',
  'chat.channel.configuration.chat3.whatsApp.select.message':
    'Nach der Verknüpfung mit der WhatsApp-Nummer kann der Benutzer direkt auf das WhatsApp-Symbol unten im Chatfenster klicken, um eine WhatsApp-Sitzung zu starten',

  'chat.channel.configuration.chat3.information.configuration.completed':
    'Sie haben die Grundeinstellungen abgeschlossen, die nach dem Speichern im Chatfenster implementiert werden.',

  'chat.channel.configuration.chat4.mode.message':
    'Intelligenter kundendienst wird zuerst vom roboter beantwortet, und fragen, die der roboter nicht beantworten kann, können jederzeit manuell vom benutzer beantwortet werden / nur manuelle antworten werden nur von menschlichen kundendienstmitarbeitern beantwortet / nur roboterantworten werden nur von robotern beantwortet',
  'chat.channel.configuration.chat4.mode.message.1': ` `,
  'chat.channel.configuration.chat4.mode.message.2': ` `,
  'chat.channel.configuration.chat4.mode.1': 'Intelligenter Kundendienst',
  'chat.channel.configuration.chat4.mode.2': 'Nur Menschlich',
  'chat.channel.configuration.chat4.mode.3': 'Nur Roboter',
  'chat.channel.configuration.chat4.mode': 'Kundendienstmodus',
  'chat.channel.configuration.chat4.robot.message':
    'Der Robotername wird über der Antwort des Roboters angezeigt',
  'chat.channel.configuration.chat4.robot.placeholder':
    'Bitte geben Sie den Roboternamen ein',
  'chat.channel.configuration.chat4.robot': 'Robotername',
  'chat.channel.configuration.chat4.language.message':
    'Nach dem Einschalten erkennt das System automatisch die Sprache der "Eingabefrage" des Benutzers. Wenn es nicht eingeschaltet ist, wird die Sprache des Browsers des Benutzers verwendet',
  'chat.channel.configuration.chat4.language': 'Sprache automatisch erkennen',
  'chat.channel.configuration.chat4.document': 'Dokumenten-Wissensdatenbank',
  'chat.channel.configuration.chat4.document.placeholder':
    'Bitte wählen Sie die Dokumenten-Wissensdatenbank aus',
  'chat.channel.configuration.chat4.document.message.1':
    'Die Dokumenten-Wissensdatenbank hier zeigt nur die externe Wissensdatenbank an, bitte gehen Sie im Voraus zu',
  'chat.channel.configuration.chat4.document.message':
    'Dokumenten-Wissensdatenbank',
  'chat.channel.configuration.chat4.document.message.2':
    'Konfigurieren sie die wissensdatenbank auf der seite.',
  'chat.channel.configuration.chat4.ai.message':
    'Sie können konfigurieren, ob eine verbindung zu generativer KI hergestellt werden soll',
  'chat.channel.configuration.chat4.ai':
    'Verbindung zu generativer KI herstellen',
  'chat.channel.configuration.chat4.workers':
    'Bei unbekannten Antworten, schaltfläche zum weiterleiten an einen menschlichen agenten',
  'chat.channel.configuration.chat4.workers.content':
    'Entschuldigung, ich kann diese frage nicht beantworten. bitte wenden sie sich an den menschlichen kundendienst.',
  'chat.channel.configuration.chat4.workers.position': 'Position',
  'chat.channel.configuration.chat4.workers.zhuan':
    'An einen menschlichen agenten weiterleiten',
  'chat.channel.configuration.chat4.workers.message':
    'Wenn diese option aktiviert ist, wird die schaltfläche „An einen menschlichen agenten weiterleiten“ automatisch unter der antwort des roboters angezeigt, wenn der roboter die antwort nicht kennt',
  'chat.channel.configuration.chat4.unknown':
    'Antwort des Roboters auf unbekannte Fragen',
  'chat.channel.configuration.chat4.unknown.placeholder':
    'Bitte geben sie die antwort des roboters auf unbekannte fragen ein',
  'chat.channel.configuration.chat4.unknown.message':
    'Die Einstellung Hier Ist Die Antwort Des Roboters, Wenn Er Auf Ein Unbekanntes Problem Stößt',
  'chat.channel.configuration.chat4.information.configuration.completed':
    'Sie haben die einstellungen für den intelligenten kundenservice abgeschlossen. diese werden nach dem speichern im chatfenster implementiert.',
  'chat.channel.configuration.chat5.message':
    'Kopieren Sie Den Folgenden Code Und Fügen Sie Ihn in den Inhalt des <body> </body> -Tags Ihrer Website Ein.',
  'chat.channel.configuration.chat5.message.link':
    'Chat-Link: Kopieren Sie Den Folgenden Link in den Code Ihrer Website.',
  'live.chat.title': 'Chatfenster-Vorschaubereich',
  'live.chat.title.subtitle':
    'Hier können Sie eine Vorschau des Chatfenster-Effekts anzeigen',
  'live.chat.customer': 'Kunde',
  'live.chat.customer.Dialogue':
    'Können Sie mir bitte die wichtigsten Funktionen des Produkts erläutern?',
  'live.chat.submit': 'Absenden',
  'live.chat.end': 'Chat beenden',
  'live.chat.video': 'Videokommunikation',
  'chat.channel.configuration.cancel.btn': 'Abbrechen',
  'chat.channel.configuration.next.btn': 'Weiter',
  'chat.channel.configuration.complete.btn': 'Fertig',
  'chat.channel.configuration.title.knowledge_unknown_reply':
    'Mit meinen aktuellen Fähigkeiten kann ich Ihre Frage nicht beantworten. Bei Bedarf können Sie sich direkt an unsere Kundendienstmitarbeiterin wenden, um professionellere Unterstützung zu erhalten~',
  'chat.channel.configuration.chat5.end':
    'Bitte beachten Sie: Nachdem Sie den obigen Code in Ihre Website integriert haben, kontaktieren Sie bitte den „ConnectNow“-Administrator, um die oben genannten Domains zur Whitelist hinzuzufügen. Erst nach Abschluss der Whitelist-Konfiguration wird die Chat-Komponente ordnungsgemäß angezeigt.',
  'chat.channel.configuration.chat5.end.1': ' ',

  'chat.channel.configuration.channel.name.web': 'Website-Domainname',
  'chat.channel.configuration.channel.name.placeholder.web':
    'Bitte geben Sie den Website-Domainnamen ein',
  'live.chat.customer.Dialogue.product':
    'Welches Produkt möchten Sie kennenlernen?',
  'chat.channel.configuration.chat5.message.Settings':
    'Bereitstellungseinstellungen',
  'chat.channel.configuration.channel.name.placeholder.error':
    'Es können nur Chinesisch, Groß- und Kleinbuchstaben, Zahlen, "-" und "_" eingegeben werden',
  'chat.channel.configuration.channel.chatBoxName.placeholder.error':
    'Es können nur Chinesisch, Groß- und Kleinbuchstaben und Leerzeichen eingegeben werden',
  'chat.channel.configuration.chat1.document.placeholder.language':
    'Wenn sich die Trigger-FAQ-Daten ändern, wählen Sie bitte erneut aus',
  'chat.channel.configuration.channel.website':
    'Das Format des Website-Domainnamens lautet wie folgt: www.connectnow.cn',
  'chat.channel.configuration.channel.website.name.placeholder.error':
    'Bitte geben Sie einen regulären Website-Domainnamen ein',
  'chat.channel.configuration.work.panels.checkbox.ccp':
    'Sprachidentifikation aktivieren?',
  'chat.channel.configuration.title.pop_welcome_msg':
    'Hallo, ich bin der intelligente Kundenservice von ConnectNow. Wie kann ich Ihnen helfen?',
  'chat.channel.configuration.chat4.workers.keyword.message':
    'Nachdem der Kunde dieses Schlüsselwort eingegeben hat, wird er an den menschlichen Kundendienst weitergeleitet',
  'chat.channel.configuration.chat4.workers.keyword':
    'Schlüsselwort für die Weiterleitung an einen menschlichen Agenten',
  'chat.channel.configuration.chat4.document.placeholder.keyword':
    'Bitte geben Sie mindestens ein Schlüsselwort ein',

  'wx.program.channel.configuration.title':
    'WeChat-Miniprogramm-Chat-Kanal hinzufügen',
  'wx.program.channel.configuration.title.update':
    'WeChat-Miniprogramm-Chat-Kanal bearbeiten',
  'wx.program.channel.configuration.tips':
    'Kunden können das Unternehmen einfacher über WeChat-Miniprogramme kontaktieren, und Kundenanfragen können schneller beantwortet werden.',
  'shopify.channel.configuration.title': 'Shopify-Chat-Kanal hinzufügen',
  'shopify.channel.configuration.title.update': 'Shopify-Chat-Kanal bearbeiten',
  'shopify.channel.configuration.tips':
    'Kunden können das Unternehmen einfacher über die Shopify-Website kontaktieren, und Kundenanfragen können schneller beantwortet werden.',

  'chat.channel.configuration.chat1.ai.aiagent': 'AI Agent aktivieren',
  'chat.channel.configuration.chat1.ai.aiagent.tips':
    'Nachdem der AI-Agent aktiviert wurde, müssen die Begrüßung und die Auswahl der AIGC-Wissensdatenbank auf der AI-Agent-Seite konfiguriert werden.',
  'chat.channel.configuration.chat2.agent.avac':
    'Standard-Avatar des Kundendienstes',
  'chat.channel.configuration.chat2.customer.avac':
    'Standard-Avatar des Kunden',
  'chat.channel.configuration.chat2.robot.avac': 'Roboter-Avatar',
  'chat.channel.configuration.chat2.robot.avac.kexuan': '(Optional)',
  'chat.channel.configuration.chat2.robot.avac.tips':
    '会在聊天框里展示，建议上传50*50px的PNG图片',
  'chat.channel.configuration.chat2.agent.font.color':
    'Schriftfarbe des Kundendienst-Chats',
  'chat.channel.configuration.chat2.customer.font.color':
    'Schriftfarbe des Benutzer-Chats',
  'chat.channel.configuration.chat3.history.chat':
    'Historische Gespräche anzeigen',
  'chat.channel.configuration.chat3.history.chat.tips':
    'Nach dem Einschalten können Endbenutzer frühere historische Gespräche sehen',
  'chat.channel.configuration.chat3.history.chat.num':
    'Anzahl der historischen Gespräche anzeigen',
  'chat.channel.configuration.chat3.history.chat.num.p':
    'Bitte wählen Sie die Anzahl der anzuzeigenden historischen Gespräche aus',
  'chat.channel.configuration.chat3.history.chat.select': 'Unbegrenzt',
  'chat.channel.configuration.chat4.transfor.btn':
    'Schaltfläche "An einen menschlichen Agenten weiterleiten" anzeigen',
  'chat.channel.configuration.chat4.transfor.btn.tips':
    'Nach dem Öffnen wird die Schaltfläche „An einen menschlichen Agenten weiterleiten“ auf der rechten Seite des Chatfensters angezeigt',
  'chat.channel.configuration.chat4.agent.workTime': 'Agenten-Arbeitszeit',
  'chat.channel.configuration.chat4.agent.workTime.p':
    'Bitte wählen Sie die Agenten-Arbeitszeit aus',

  'chat.channel.configuration.chat4.agent.workTime.no':
    'Roboter-Antwortbotschaft außerhalb der Arbeitszeit',

  'chat.channel.configuration.chat4.agent.workTime.no.default':
    'Entschuldigung, wir sind im Moment nicht erreichbar. Bitte kontaktieren Sie uns wieder zwischen 08:00 und 18:00 Uhr.',
  'chat.channel.configuration.chat4.agent.workTime.no.tips':
    'Botschaft, die der Roboter antwortet, wenn ein Kunde außerhalb der Agenten-Arbeitszeit eine Weiterleitung an einen menschlichen Agenten anfordert',

  'chat.channel.configuration.chat1.push.agents.join.chat':
    'Push Sitz zum Chat',

  // discord渠道
  'channel.allocation.detail.discord.title': 'Discord-Kanal-Konfiguration',
  'add.discord.channel.configuration.title': 'Discord-Kanal hinzufügen',
  'editor.discord.channel.configuration.title': 'Discord-Kanal bearbeiten',
  'add.discord.channel.configuration.tips':
    'Bitte fügen Sie einen Discord-Kanal hinzu',
  'discord.channel.configuration.title.1': 'Discord-Bot-Token konfigurieren',
  'discord.channel.configuration.title.tips.1':
    'Geben Sie den Bot-Token aus dem Discord-Entwicklerportal in das folgende Eingabefeld ein',
  'discord.channel.configuration.title.tips.2':
    'Konfigurationsdokument anzeigen',
  'discord.channel.allocation.complete.1':
    'Sie haben den Bot-Token erfolgreich eingegeben, fahren Sie bitte mit der Konfiguration des Chatbots fort',
  'discord.channel.configuration.channel.bot.name': 'Bot-Name',
  'discord.channel.configuration.channel.bot.name.placeholder':
    'Bitte geben Sie den Bot-Namen ein',
  'discord.channel.configuration.channel.bot.token': 'Bot-Token',
  'discord.channel.configuration.channel.bot.token.placeholder':
    'Bitte geben Sie den Bot-Token ein',
  'channel.allocation.detail.input.bot.name': 'Bot-Name:',
  'channel.allocation.detail.input.bot.name.placeholder':
    'Geben Sie einen Bot-Namen ein und drücken Sie die Eingabetaste, um zu suchen',
  'discord.channel.configuration.channel.application.id': 'Anwendungs-ID:',
  'discord.channel.configuration.channel.application.id.placeholder':
    'Bitte geben Sie die Anwendungs-ID ein',

  // discord帮助文档
  'channel.allocation.discord.document.title':
    'Discord: Integration mit ConnectNow',
  'channel.allocation.discord.document.h1': 'Einen Discord-Server vorbereiten',
  'channel.allocation.discord.document.step.1':
    ' Ein Discord-Konto vorbereiten',
  'channel.allocation.discord.document.step.1.text':
    'Wenn Sie noch kein Discord-Konto haben, besuchen Sie bitte <a>https://discord.com/</a>, um sich zu registrieren',
  'channel.allocation.discord.document.step.1.text.1':
    ' Wenn Sie bereits ein Discord-Konto haben, gehen Sie direkt zu Schritt 2.',
  'channel.allocation.discord.document.step.2':
    ' Einen Discord-Server vorbereiten',
  'channel.allocation.discord.document.step.2.text':
    'Hinweis: Wenn Sie bereits einen Discord-Server haben, überspringen Sie bitte diesen Schritt',
  'channel.allocation.discord.document.step.2.text.1':
    ' Klicken Sie auf „Einen Server erstellen“',
  'channel.allocation.discord.document.step.2.text.2':
    ' Wählen Sie „Selbst erstellen“',
  'channel.allocation.discord.document.step.2.text.3':
    'Wählen Sie Ihre Server-Informationen entsprechend Ihren Bedürfnissen aus. Wenn Sie nicht sicher sind, können Sie auch auf „Diese Frage überspringen“ klicken',
  'channel.allocation.discord.document.step.2.text.4':
    'Geben Sie Ihren Server-Namen ein, laden Sie ein Logo hoch und klicken Sie dann auf „Erstellen“',
  'channel.allocation.discord.document.step.3': ' Eine Anwendung erstellen',
  'channel.allocation.discord.document.step.3.text':
    'Besuchen Sie das Entwicklerportal <a>https://discord.com/developers/applications</a> und klicken Sie auf „New Application“',
  'channel.allocation.discord.document.step.3.text.1':
    'Geben Sie den Namen der Anwendung ein und klicken Sie auf Erstellen',
  'channel.allocation.discord.document.step.3.text.2':
    'Richten Sie nach Abschluss der Erstellung Ihre Anwendungsinformationen ein',
  'channel.allocation.discord.document.step.3.text.3':
    'Klicken Sie im linken Menü auf „Bot“',
  'channel.allocation.discord.document.step.3.text.4':
    'Konfigurieren Sie die Bot-Berechtigungen. Folgen Sie dem Bild unten, um die Berechtigungen zu konfigurieren, und klicken Sie dann auf die Schaltfläche [Save Changes]',
  'channel.allocation.discord.document.step.3.text.5':
    'Autorisieren. Klicken Sie im linken Menü auf OAuth2, scrollen Sie nach unten, aktivieren Sie Bot, scrollen Sie dann nach unten und aktivieren Sie nacheinander Send Messages, Send Messages in Threads, Manage Messages, Read Message History und View Channels. Siehe die beiden Bilder unten',
  'channel.allocation.discord.document.step.3.text.6':
    'Nachdem Sie die Auswahl getroffen haben, wird unten auf der Seite ein Autorisierungslink generiert. Klicken Sie zum Kopieren und öffnen Sie ihn in einem Browser',
  'channel.allocation.discord.document.step.3.text.7':
    'Nachdem Sie den Link geöffnet haben, wählen Sie, wie unten gezeigt, den Server aus, dem der Bot hinzugefügt werden soll, klicken Sie auf „Weiter“ und dann im nächsten Schritt auf die Schaltfläche „Autorisieren“',
  'channel.allocation.discord.document.step.3.text.8':
    'Bot erfolgreich zum Server hinzugefügt',
  'channel.allocation.discord.document.step.4':
    ' Den Bot in ConnectNow integrieren',
  'channel.allocation.discord.document.step.4.text':
    'Den Bot-Token abrufen. Besuchen Sie das Entwicklerportal <a>https://discord.com/developers/applications</a>, klicken Sie im linken Menü auf „Bot“ und dann auf „Reset Token“',
  'channel.allocation.discord.document.step.4.text.1':
    'Klicken Sie auf Kopieren, um den Token zu speichern.',
  'channel.allocation.discord.document.step.4.text.6':
    'Die Anwendungs-ID abrufen. Dieses Feld ist sehr wichtig, ein Fehler führt dazu, dass der Kanal keine Nachrichten empfängt!',
  'channel.allocation.discord.document.step.4.text.2':
    'Gehen Sie zum ConnectNow-Admin, klicken Sie auf Kanäle -> Discord -> Kanal hinzufügen, geben Sie den gespeicherten Token und den Bot-Namen ein und klicken Sie auf „Weiter“',
  'channel.allocation.discord.document.step.4.text.3':
    'Legen Sie die Chatbot-Parameter entsprechend Ihren Bedürfnissen fest und klicken Sie dann auf „Weiter“',
  'channel.allocation.discord.document.step.4.text.4':
    'Geben Sie einen Kanalnamen ein',
  'channel.allocation.discord.document.step.4.text.5':
    '🎉Herzlichen Glückwunsch! Ihr Discord wurde erfolgreich in ConnectNow integriert',
};
