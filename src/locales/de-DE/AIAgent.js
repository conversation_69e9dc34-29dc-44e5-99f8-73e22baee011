export default {
  'worktable.notification.chat.title': '「ConnectNow」 Chat-Benachrichtigung',
  'worktable.notification.chat.content': 'Kunden Nachricht:',
  'worktable.notification.call.title': '「ConnectNow」 Anruf-Benachrichtigung',
  'worktable.notification.call.content':
    'Kunde {number} ruft an, bitte antworten Sie am Arbeitsplatz.',
  'beta.tip':
    'Diese Funktion befindet sich derzeit in der Beta-Phase. Wenn sie diese funktion nutzen möchten, wenden sie sich bitte an unseren kundenbetreuer. Vielen dank für ihre unterstützung und mitarbeit!',

  'ai.agent.verification.start':
    'Es muss mindestens ein Knoten mit dem Startknoten verbunden sein!',
  'ai.agent.verification.conditionIntent':
    'Die Absichtserkennungskomponente kann nur mit der Textkomponente in der Frage Verknüpft Werden!',
  'ai.agent.verification.node': `Der Inhalt von {formName} in Knoten {nodeName} darf nicht leer sein!`,

  'ai.agent.channnel': 'Kanal',
  'ai.agent.channnel.options': 'Wählen Sie Den Passenden Kanaltyp Aus',
  'ai.agent.copy.fail': 'Agent ist leer, kopieren fehlgeschlagen!',
  'ai.agent.var': 'Variablenverwaltung',
  'ai.agent.var.add': 'Variable Hinzufügen',
  'ai.agent.var.select': 'Typ Auswählen',
  'ai.agent.var.setting.content.title':
    'Proaktive Erinnerung, Wenn Der Kunde Nicht Antwortet',
  'ai.agent.var.setting.content.tip':
    'Die Einstellung Hier Ist Die Proaktive Erinnerung Des Roboters, Wenn Der Kunde Nicht Auf Die Nachricht Antwortet',
  'ai.agent.var.name.rules':
    'Der variablenname darf nicht chinesisch sein und die maximale länge beträgt 200!',
  'ai.agent.var.tabs.1': 'Aktuelle Agentenvariable',
  'ai.agent.var.tabs.2': 'Aktuelle Sitzungsvariable',
  'ai.agent.var.tabs.3': 'Globale Variable',
  'ai.agent.var.tabs.4': 'Systemintegrierte Variable',
  'ai.agent.var.table.1': 'Variablenname',
  'ai.agent.var.table.2': 'Datentyp',
  'ai.agent.var.table.3': 'Standardwert',
  'ai.agent.var.table.4': 'Operation',
  'ai.agent.var.table.select.option.1': 'String',
  'ai.agent.var.table.select.option.2': 'Numerisch',
  'ai.agent.var.table.select.option.3': 'JSON',
  'ai.agent.var.table.select.option.4': 'Array',

  'ai.agent.back.title': 'Hinweis Nicht Gespeichert',
  'ai.agent.back.title.content':
    'Der Aktuelle Agent Wurde Geändert. Möchten Sie Die änderungen Speichern?',
  'ai.agent.back.button.no': 'Nicht Speichern',

  'ai.agent.nodes.start': 'Startknoten',

  'ai.agent.nodes.header.tips.status.1': 'Nicht Bereitgestellt',
  'ai.agent.nodes.header.tips.status.2': 'Nicht Gespeichert',
  'ai.agent.nodes.header.tips.status.3': 'Bereitgestellt',
  'ai.agent.nodes.header.tips.save':
    'Neueste Version Gespeichert, Noch Nicht Bereitgestellt',
  'ai.agent.nodes.header.test': 'Test',
  'ai.agent.testAgent.endTest': 'Agent hat die ausführung abgeschlossen',
  'ai.agent.nodes.header.test.tips':
    'Bitte speichern sie zuerst, bevor sie testen',
  'ai.agent.nodes.header.save': 'Speichern Und Bereitstellen',
  'ai.agent.nodes.header.save.success': 'Bereitstellung Erfolgreich',
  'ai.agent.nodes.header.shareCode': '输入分享码',
  'ai.agent.enable.confirm.title': 'Aktuellen Agenten aktivieren',
  'ai.agent.enable.confirm.content':
    'Möchten Sie den aktuellen Agenten aktivieren? Nach der Aktivierung beginnt der aktuelle Agent zu laufen.',
  'ai.agent.enable.confirm.confirm': 'Aktivierung bestätigen',
  'ai.agent.enable.confirm.cancel': 'Abbrechen',
  'ai.agent.enable.success': 'Erfolgreich aktiviert',

  'ai.agent.nodes.header.tips.shortcut.title':
    'Tipp: <b>Mehrfachauswahl von Komponenten</b>',
  'ai.agent.nodes.header.tips.shortcut.1': '• Shift + Ziehen: Bereichsauswahl',
  'ai.agent.nodes.header.tips.shortcut.2':
    '• Command (unter Windows Ctrl) + Klick: Einzelauswahl',

  'ai.agent.nodes.header.save.error.noIntent':
    'Bitte wählen sie eine absicht aus, bevor sie bereitstellen',
  'ai.agent.nodes.start.noDelete':
    'Der Startknoten Darf Nicht Gelöscht Werden!',
  'ai.agent.nodes.start.illegal':
    'Bitte Wählen Sie Eine Gültige Komponente Für Den Vorgang Aus',
  'ai.agent.nodes.form.name': 'Komponenten',
  'ai.agent.nodes.form.name.small': 'Komponenten',
  'ai.agent.nodes.form.search': 'Suche',
  'ai.agent.nodes.form.message': 'Roboternachricht',
  'ai.agent.nodes.form.question': 'Frage Stellen',
  'ai.agent.nodes.form.tools': 'Werkzeug',
  'ai.agent.nodes.form.condition': 'Bedingung',

  'ai.agent.nodes.form.popup.p': 'Bitte Geben Sie Den Komponentennamen Ein...',
  'ai.agent.nodes.form.popup.messageText.title': 'Roboter-Wortlaut',
  'ai.agent.nodes.form.popup.messageImage.radio.1': 'Bild Hochladen',
  'ai.agent.nodes.form.popup.messageImage.radio.2': 'URL hinzufügen',
  'ai.agent.nodes.form.popup.messageImage.input.url': 'URL Eingeben',
  'ai.agent.nodes.form.popup.messageImage.input.url.p':
    'Bitte Geben Sie Die URL Ein',
  'ai.agent.nodes.form.popup.messageImage.input.alt': 'Bildbeschreibung',
  'ai.agent.nodes.form.popup.messageImage.input.alt.p':
    'Bitte Geben Sie Eine Beschreibung Ein',
  'ai.agent.nodes.form.popup.messageImage.input.note':
    'Hinweis: Die Beschreibung Wird Auch Dem Kunden Angezeigt',
  'ai.agent.nodes.form.popup.messageImage.input.upload':
    'Laden sie bilder hoch, maximal 10 MB',
  'ai.agent.nodes.form.popup.messageImage.input.upload.btn':
    'Klicken Sie Zum Hochladen',

  'ai.agent.nodes.form.popup.MessageVideo.radio.1': 'Video Hochladen',
  'ai.agent.nodes.form.popup.MessageVideo.radio.2': 'URL hinzufügen',
  'ai.agent.nodes.form.popup.MessageVideo.input.url': 'URL Eingeben',
  'ai.agent.nodes.form.popup.MessageVideo.input.url.p':
    'Bitte Geben Sie Die URL Ein',
  'ai.agent.nodes.form.popup.MessageVideo.input.alt': 'Videobeschreibung',
  'ai.agent.nodes.form.popup.MessageVideo.input.alt.p':
    'Bitte Geben Sie Eine Beschreibung Ein',
  'ai.agent.nodes.form.popup.MessageVideo.input.note':
    'Hinweis: Die Beschreibung Wird Auch Dem Kunden Angezeigt',
  'ai.agent.nodes.form.popup.MessageVideo.input.upload':
    'Laden sie videos hoch, maximal 100 MB',
  'ai.agent.nodes.form.popup.MessageVideo.input.upload.btn':
    'Klicken Sie Zum Hochladen',

  'ai.agent.nodes.form.popup.MessageDoc.radio.1': 'Dokument Hochladen',
  'ai.agent.nodes.form.popup.MessageDoc.radio.2': 'URL hinzufügen',
  'ai.agent.nodes.form.popup.MessageDoc.input.url': 'URL Eingeben',
  'ai.agent.nodes.form.popup.MessageDoc.input.url.p':
    'Bitte Geben Sie Die URL Ein',
  'ai.agent.nodes.form.popup.MessageDoc.input.alt': 'Documentbeschreibung',
  'ai.agent.nodes.form.popup.MessageDoc.input.alt.p':
    'Bitte Geben Sie Eine Beschreibung Ein',
  'ai.agent.nodes.form.popup.MessageDoc.input.note':
    'Hinweis: Die Beschreibung Wird Auch Dem Kunden Angezeigt',
  'ai.agent.nodes.form.popup.MessageDoc.input.upload':
    'Laden sie das dokument hoch, maximal 20 MB',
  'ai.agent.nodes.form.popup.MessageDoc.input.upload.btn':
    'Klicken Sie Zum Hochladen',

  'ai.agent.nodes.startNode.select.title.1': 'Triggermethode',
  'ai.agent.nodes.startNode.select.title.1.options.1': 'Absichtstrigger',
  'ai.agent.nodes.startNode.select.title.1.p':
    'Bitte wählen sie die triggermethode',
  'ai.agent.nodes.startNode.select.title.2': 'Absicht Auswählen',
  'ai.agent.nodes.startNode.select.title.2.p':
    'Bitte wählen sie die absicht aus',
  'ai.agent.nodes.startNode.var': 'Variablen Übergeben',
  'ai.agent.nodes.startNode.var.table.1': 'Dynamische Absichtsvariable',
  'ai.agent.nodes.startNode.var.table.2': 'Aktuelle Agentenvariable',
  'ai.agent.nodes.startNode.var.table.add.warn':
    'Bitte speichern sie zuerst die bearbeitungsdaten',

  'ai.agent.nodes.ToolToAgent.title.1': 'Standardnachricht',
  'ai.agent.nodes.ToolToAgent.title.2': 'Zuweisungsregel',
  'ai.agent.nodes.ToolToAgent.title.3': 'Ticket-Typ festlegen',
  'ai.agent.nodes.ToolToAgent.title.4': 'Ticket-Priorität festlegen',
  'ai.agent.nodes.ToolToAgent.title.5': 'Eingehende Absicht Festlegen',
  'ai.agent.nodes.ToolToAgent.title.6': 'Kunden-Tag Festlegen',
  'ai.agent.nodes.ToolToAgent.title.span': '(Optional)',
  'ai.agent.nodes.ToolToAgent.radio.title.1': 'System Automatische Zuweisung',
  'ai.agent.nodes.ToolToAgent.radio.title.2': 'Manuelle Zuweisung',
  'ai.agent.nodes.ToolToAgent.select.rule.p': 'Zuweisungsmethode Auswählen',
  'ai.agent.nodes.ToolToAgent.select.agent': 'Agenten Auswählen:',
  'ai.agent.nodes.ToolToAgent.select.team': 'Team Auswählen:',
  'ai.agent.nodes.ToolToAgent.select.agent.p':
    'Bitte Wählen Sie Einen Agenten Aus',
  'ai.agent.nodes.ToolToAgent.select.team.p': 'Bitte Wählen Sie Ein Team Aus',
  'ai.agent.nodes.ToolToAgent.select.type':
    'Bitte Wählen Sie Einen Ticket-Typ Aus',
  'ai.agent.nodes.ToolToAgent.select.priority':
    'Bitte Wählen Sie Eine Ticket-Priorität Aus',
  'ai.agent.nodes.ToolToAgent.input.default':
    'Wir Leiten Sie Zum Menschlichen Agenten Weiter',
  'ai.agent.nodes.ToolFailure.text': 'Rückgabe Fehlgeschlagen',

  'ai.agent.nodes.ToolLLM.title.1': 'Hinweis Festlegen',
  'ai.agent.nodes.ToolLLM.checkBox.1': 'Anzeigen, Ob Von AIGC Generiert',
  'ai.agent.nodes.ToolLLM.back.type': 'Rückgabetyp',
  'ai.agent.nodes.ToolLLM.storage.var': 'In Variable Speichern',
  'ai.agent.nodes.ToolLLM.storage.var.p': 'Bitte Wählen Sie Eine Variable Aus',
  'ai.agent.nodes.ToolLLM.back.type.1': 'Text',
  'ai.agent.nodes.ToolLLM.contentShow': 'Ergebnis Anzeigen',
  //aiagent ToolAPI
  'ai.agent.nodes.ToolAPI.title1': 'API zum Aufrufen auswählen',
  'ai.agent.nodes.ToolAPI.title3': 'API-Verwaltung',
  'ai.agent.nodes.ToolAPI.title2.0': 'Hinweis: Wenn der',
  'ai.agent.nodes.ToolAPI.title2.1': ' HTTP-Statuscode',
  'ai.agent.nodes.ToolAPI.title2.2': ' gleich',
  'ai.agent.nodes.ToolAPI.title2.3': '200',
  'ai.agent.nodes.ToolAPI.title2.4':
    'ist, wird der API-Aufruf als erfolgreich betrachtet, andernfalls wird die Fallback-Logik ausgeführt.',
  //aiagent ToolWorkHours
  'ai.agent.nodes.ToolWorkHours.title1': 'Arbeitszeit auswählen',
  'ai.agent.nodes.ToolWorkHours.workTime': 'In Arbeitszeiten',
  'ai.agent.nodes.ToolWorkHours.notWorkTime': 'Nicht in Arbeitszeiten',
  //aiagent AskQuestionText
  'ai.agent.nodes.AskQuestionText.title1':
    'Kundenantwort-Validierung Und Speicherung',
  'ai.agent.nodes.AskQuestionText.title2':
    'Wählen Sie Die Validierungsformat Aus',
  'ai.agent.nodes.AskQuestionText.title21': 'Überprüfungsart',
  'ai.agent.nodes.AskQuestionText.title3': 'Überprüfungsfehler Wiederholungen',
  'ai.agent.nodes.AskQuestionText.title4': 'Antwort Nach Validierungsfehler',
  'ai.agent.nodes.AskQuestionText.title5':
    'Antwort Nach Dem Letzten Validierungsfehler',
  'ai.agent.nodes.AskQuestionText.title6': 'Auf Validierungsregulärexpression',
  'ai.agent.nodes.AskQuestionText.date.title1':
    'Wählen Sie Den Datums- Und Zeitbereich Aus',
  'ai.agent.nodes.AskQuestionText.date.to': 'An',
  'ai.agent.nodes.AskQuestionText.date.select1': 'Benutzerdefiniert',
  'ai.agent.nodes.AskQuestionText.date.select2': 'Datum vor heute einschließen',
  'ai.agent.nodes.AskQuestionText.date.select3':
    'Datum Nach Heute Einschließen',
  'ai.agent.nodes.AskQuestionText.int.title1':
    'Geben sie den numerischen bereich ein',
  'ai.agent.nodes.AskQuestionText.reg.title1':
    'Bitte Geben Sie Den Regulären Ausdruck Ein',
  'ai.agent.nodes.AskQuestionText.llm.title1':
    'Bitte Geben Sie Den Überprüfungsmodus Ein',
  'ai.agent.nodes.AskQuestionText.SaveValue': 'In Variable Speichern',
  'ai.agent.nodes.AskQuestionText.None': 'Keine Validierung Erforderlich',
  'ai.agent.nodes.AskQuestionText.Regex': 'Regulärer Ausdruck',
  'ai.agent.nodes.AskQuestionText.checkType1': 'Standard Validierung',
  'ai.agent.nodes.AskQuestionText.checkType2': 'LLM Validierung',
  //aiagent AskQuestionButton
  'ai.agent.nodes.AskQuestionButton.title1': 'Leitfaden-Taste',
  'ai.agent.nodes.AskQuestionButton.title2': 'Schaltfläche Anordnung',
  'ai.agent.nodes.AskQuestionButton.type1': 'Vertikal',
  'ai.agent.nodes.AskQuestionButton.type2': 'Horizontal',
  'ai.agent.nodes.AskQuestionButton.addButton': 'Schaltfläche Hinzufügen',
  //aiagent AskQuestionForm
  'ai.agent.nodes.AskQuestionForm.title1': 'Attributname',
  'ai.agent.nodes.AskQuestionForm.title2': 'Anzeigename',
  'ai.agent.nodes.AskQuestionForm.title3': 'Eingabeaufforderung Für Attribute',
  'ai.agent.nodes.AskQuestionForm.title4': 'Attributform Auswählen',
  'ai.agent.nodes.AskQuestionForm.title5': 'Attributwert',
  'ai.agent.nodes.AskQuestionForm.title6': 'Wert Hinzufügen',
  'ai.agent.nodes.AskQuestionForm.attributeType.1': 'Einzeiliges Eingabefeld',
  'ai.agent.nodes.AskQuestionForm.attributeType.2': 'Mehrzeiliges Eingabefeld',
  'ai.agent.nodes.AskQuestionForm.attributeType.3': 'Einzelauswahl-Dropdown',
  'ai.agent.nodes.AskQuestionForm.attributeType.4': 'Mehrfachauswahl-Dropdown',
  'ai.agent.nodes.AskQuestionForm.attributeType.5': 'Radio-Button',
  'ai.agent.nodes.AskQuestionForm.attributeType.6': 'Checkbox',
  'ai.agent.nodes.AskQuestionForm.attributeType.7':
    'Datum (bis Jahr-Monat-Tag)',
  'ai.agent.nodes.AskQuestionForm.attributeType.8':
    'Datum (bis Stunde-Minute-Sekunde)',
  'ai.agent.nodes.AskQuestionForm.attributeType.9':
    'Datumsbereich (bis Jahr-Monat-Tag)',
  'ai.agent.nodes.AskQuestionForm.attributeType.10':
    'Datumsbereich (bis Stunde-Minute-Sekunde)',
  'ai.agent.nodes.AskQuestionForm.attributeType.11': 'Datei-Upload',
  'ai.agent.nodes.AskQuestionForm.addForm': 'Variable Hinzufügen',
  'ai.agent.nodes.AskQuestionForm.addForm.button1': 'Senden',
  'ai.agent.nodes.AskQuestionForm.addForm.button2': 'Abbrechen',
  'ai.agent.nodes.AskQuestionForm.show.upload': 'Datei Hochladen',
  'ai.agent.nodes.AskQuestionForm.show.upload.tips':
    'Hochladen Sie Dateien, bis zu 5 Dateien, jede Datei bis zu 20 MB',
  //aiagent AskQuestionLLM
  'ai.agent.nodes.AskQuestionLLM.tip1': 'Bitte geben sie den attributnamen ein',
  'ai.agent.nodes.AskQuestionLLM.tip2': 'Bitte geben sie den attributcode ein',
  'ai.agent.nodes.AskQuestionLLM.title1': 'Attributname',
  'ai.agent.nodes.AskQuestionLLM.title2': 'Attributcodierung',
  'ai.agent.nodes.AskQuestionLLM.title3': 'Attributformatanforderungen',
  'ai.agent.nodes.AskQuestionLLM.title4': 'In die Variable speichern',

  //aiagent Rag
  'ai.agent.nodes.Rag.knowledgeType': 'Wissenbasis-Typ',
  'ai.agent.nodes.Rag.selectKnowledgeTag': 'Wissenbasis-Tag auswählen',
  'ai.agent.nodes.Rag.ragName': 'RAG-Wissensdatenbankname',
  'ai.agent.nodes.Rag.reUserAsk': 'Kundenfrage Umschreiben',
  'ai.agent.nodes.Rag.selectKnowledgeType':
    'Wählen Sie Den Wissenbasis-Typ Aus',
  'ai.agent.nodes.Rag.faqKnoewledge': 'FAQ-Wissenbasis',
  'ai.agent.nodes.Rag.ragKnoewledge': 'RAG-Wissenbasis',
  'ai.agent.nodes.Rag.selectRagKnowledgeType':
    'Wählen Sie Die AIGC RAG Wissenbasis Aus',
  'ai.agent.nodes.Rag.a&qStyle': 'Frage-Und-Antwort-Stil',
  'ai.agent.nodes.Rag.a&qStyle1': 'Prägnant Und Klar',
  'ai.agent.nodes.Rag.a&qStyle2': 'Sanfte Begleitung',
  'ai.agent.nodes.Rag.cantUnderstand': 'Roboter-Antwort Bei Fehlendem Wissen',
  'ai.agent.nodes.Rag.tip1':
    'Sanfte Begleitung Öffnen, AIGC Chatbot wird Ihren Kunden eine angenehmere Antwort liefern.',
  'ai.agent.nodes.Rag.tip2':
    'Die Einstellung Hier Ist Die Antwort Des Roboters, Wenn Ihm Wissen Fehlt',
  //aiagent Intent
  'ai.agent.nodes.Intent.intentName': 'Absichtsname',
  'ai.agent.nodes.Intent.intentJudgmentBasis':
    'Grundlage Für Die Absichtsbeurteilung',
  'ai.agent.nodes.Intent.tip':
    'Hinweis: Die beurteilungsgrundlage ist eine wichtige grundlage für AIGC, um anhand der aktuellen kundeneingabe festzustellen, ob sie zur aktuellen absicht gehört. bitte füllen sie diese sorgfältig aus.',
  'ai.agent.nodes.Intent.addIntent': 'Absicht Hinzufügen',
  'ai.agent.testAgent': 'Testroboter',
  // 设置等待回复
  'ai.agent.waiting.reply.table.waiting.time': 'Wartezeit',
  'ai.agent.waiting.reply.table.reminder.language': 'Erinnerungsrede',
  'ai.agent.script.add': 'Hinzufügen ausdruck',
  'ai.agent.script.time.minute': 'Minute',
  'ai.agent.script.time.second': 'Sekunden',
  'ai.agent.script.time.tips': 'Bitte geben sie zahlen zwischen 0 und 59 ein',
  'ai.agent.script.settings.num.tips':
    'Die Anzahl Der Erinnerungseinstellungen Darf 5 Nicht überschreiten',
  'ai.agent.script.settings.not.empty.tips':
    'Wartezeit Und Erinnerungsausdruck Dürfen Nicht Leer Sein!',

  'ai.agent.nodes.start.type.2': 'Standard Welcome Agent',
  'ai.agent.nodes.start.type.2.content':
    'Wenn Kunden über Web-Online-Chat, App-Online-Chat, Shopify, WeChat-Miniprogramme Mit Unternehmen Kommunizieren, Wird Zuerst Der Aktuelle Willkommens-Agent Ausgeführt',
  'ai.agent.nodes.start.type.3': 'Standard Fallback-Absicht',
  'ai.agent.nodes.start.type.3.content':
    'Wenn das system keinen agenten findet, wird die standardabsicht ausgeführt',

  //aiagent ToolVariableSetting
  'ai.agent.nodes.ToolVariableSetting.title1': 'Variable auswählen',
  'ai.agent.nodes.ToolVariableSetting.title2': 'Anpassungsregel auswählen',
  'ai.agent.nodes.ToolVariableSetting.title3': 'Präfixinhalt eingeben',
  'ai.agent.nodes.ToolVariableSetting.title4': 'Suffixinhalt eingeben',
  'ai.agent.nodes.ToolVariableSetting.title5': 'Bestimmten Wert eingeben',
  'ai.agent.nodes.ToolVariableSetting.title6': 'Zahlenbereich eingeben',
  'ai.agent.nodes.ToolVariableSetting.title7': 'Summenwert eingeben "1"',
  'ai.agent.nodes.ToolVariableSetting.title8': 'Summenwert eingeben "2"',
  'ai.agent.nodes.ToolVariableSetting.title9': 'Differenzwert eingeben "1"',
  'ai.agent.nodes.ToolVariableSetting.title10': 'Differenzwert eingeben "2"',
  'ai.agent.nodes.ToolVariableSetting.title11': 'Produktwert eingeben "1"',
  'ai.agent.nodes.ToolVariableSetting.title12': 'Produktwert eingeben "2"',
  'ai.agent.nodes.ToolVariableSetting.title13': 'Quotientenwert eingeben "1"',
  'ai.agent.nodes.ToolVariableSetting.title14': 'Quotientenwert eingeben "2"',
  'ai.agent.nodes.ToolVariableSetting.title15': 'Aufgerundeten Wert eingeben',
  'ai.agent.nodes.ToolVariableSetting.title16': 'Abgerundeten Wert eingeben',
  'ai.agent.nodes.ToolVariableSetting.title17': 'Attribute des Arbeitsauftrags',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num1': 'Ist gleich',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num2': 'Addition von',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num3': 'Subtraktion von',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num4': 'Multiplizieren mit',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num5': 'Dividieren durch',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num6': 'Aufrunden',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num7': 'Abrunden',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str1': 'Präfix hinzufügen',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str2': 'Suffix hinzufügen',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str3':
    'Leerzeichen entfernen',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str4':
    'Präfix und Suffix hinzufügen',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str5': 'Ist gleich',
  'ai.agent.nodes.ToolVariableSetting.node.title': 'Variable aktualisieren',

  'ai.agent.nodes.ToolUpdateTicket.title1': 'Kundenattribute',
  'ai.agent.nodes.ToolUpdateTicket.title2': 'Kundenattributwert',
  'ai.agent.nodes.ToolUpdateTicket.title3': 'Andere Attribute ändern',
  'ai.agent.nodes.ToolUpdateTicket.title4': 'Attributwert',
  'ai.agent.nodes.ToolUpdateTicket.title5': 'Ticketattribute',
  'ai.agent.nodes.ToolUpdateTicket.title6':
    'Attributwertregel: resolved(gelöst) terminated(geschlossen)',
  'ai.agent.nodes.ToolUpdateTicket.title7':
    'Attributwertregel: (P5) niedrige Auswirkung (P4) mittlere Auswirkung (P3) schwere Auswirkung (P2) dringende Behandlung (P1) Service-Crash',
  'ai.agent.nodes.ToolUpdateTicket.title8':
    'Attributwertregeln: 1 (Männlich), 2 (Weiblich), 3 (Andere)',
  'ai.agent.nodes.ToolUpdateTicket.title9':
    'Attributwertregeln: 1 (VIP-Nutzer), 2 (Normaler Nutzer)',
  'ai.agent.nodes.ToolUpdateTicket.title10':
    'Bitte geben Sie das Standarddatumformat ein, z. B.: 1979-04-01',
  'ai.agent.nodes.ToolUpdateTicket.title1005':
    'Bitte geben Sie den Optionswert ein (aus der Definition der Ticket-Erweiterungseigenschaften abrufbar), wird im "Optionen-Tab" hinzugefügt; Benutzer können nur eine Option auswählen',
  'ai.agent.nodes.ToolUpdateTicket.title1006':
    'Bitte geben Sie Optionswerte ein (aus der Definition der Ticket-Erweiterungseigenschaften abrufbar), mehrere Werte bitte mit Komma (,) trennen; Benutzer können mehrere Optionen auswählen',
  'ai.agent.nodes.ToolUpdateTicket.title1007':
    'Attributwertregeln: true (aktiviert) / false (deaktiviert)',
  'ai.agent.nodes.ToolUpdateTicket.title1008':
    'Bitte geben Sie das Standardzeitformat ein, z. B.: 2025-04-03 00:00:00',

  'ai.agent.nodes.ConditionCheck.title1': 'Bedingung',
  'ai.agent.nodes.ConditionCheck.title2': 'Sonst',
  'ai.agent.nodes.ConditionCheck.title3': 'Fehlgeschlagen',
  'ai.agent.nodes.ConditionCheck.title4': 'Wenn',
  'ai.agent.nodes.ConditionCheck.title5': 'Ist',
  'ai.agent.nodes.ConditionCheck.title6': 'Enthält',
  'ai.agent.nodes.ConditionCheck.title7': 'Ist leer',
  'ai.agent.nodes.ConditionCheck.title8': 'Ist nicht leer',
  'ai.agent.nodes.ConditionCheck.title9': 'Kundeneingabe',
  'ai.agent.nodes.ConditionCheck.title10': 'Bedingung hinzufügen',
  'ai.agent.nodes.ConditionCheck.title11': 'Größer als',
  'ai.agent.nodes.ConditionCheck.title12': 'Kleiner als',

  'ai.agent.nodes.AskQuestionLLM.addForm': 'Attribut hinzufügen',
  'ai.agent.nodes.ToolSetCustomerTag.title1': 'Tag auswählen',
  // MessageHotIssue
  'ai.agent.nodes.MessageHotIssue.title1': 'Konfigurationsmethode',
  'ai.agent.nodes.MessageHotIssue.title1.type1': 'Manuelle bearbeitung',
  'ai.agent.nodes.MessageHotIssue.title1.type2': 'Automatische empfehlung',
  'ai.agent.nodes.MessageHotIssue.title2': 'Anzeigeformat',
  'ai.agent.nodes.MessageHotIssue.title2.type1': 'Horizontal',
  'ai.agent.nodes.MessageHotIssue.title2.type2': 'Vertikal',
  'ai.agent.nodes.MessageHotIssue.title3': 'Ausgangssprache auswählen',
  'ai.agent.nodes.MessageHotIssue.title4': 'Kategoriename',
  'ai.agent.nodes.MessageHotIssue.title4.tip':
    'Geben sie den kategorienamen ein',
  'ai.agent.nodes.MessageHotIssue.title5': 'Frage',
  'ai.agent.nodes.MessageHotIssue.title5.tip': 'Geben sie eine frage ein',
  'ai.agent.nodes.MessageHotIssue.title6': 'Frage hinzufügen',
  'ai.agent.nodes.MessageHotIssue.title7': 'Kategorie hinzufügen',
  'ai.agent.nodes.MessageHotIssue.title8': 'Top-fragen',
  'ai.agent.nodes.MessageHotIssue.title9': 'Automatische empfehlung',
  'ai.agent.nodes.MessageHotIssue.title10': 'Intelligente übersetzung',
  'ai.agent.nodes.MessageHotIssue.title11': 'Zielsprache',
  'ai.agent.nodes.MessageHotIssue.title12':
    'Regel für automatische FAQ-empfehlung',
  'ai.agent.nodes.MessageHotIssue.title12.tip1': 'Automatisch aus den letzten',
  'ai.agent.nodes.MessageHotIssue.title12.tip2': 'tagen die top',
  'ai.agent.nodes.MessageHotIssue.title12.tip3': 'FAQs anzeigen',
  'ai.agent.nodes.MessageHotIssue.title12.remark':
    'Hinweis: Wenn eine FAQ mehrere fragevarianten hat, wird nur die erste angezeigt',

  'ai.agent.nodes.AskQuestionCard.title1':
    'Bitte wählen sie ihre bestellung aus',
  'ai.agent.nodes.AskQuestionCard.title2': 'Kartenlayout',
  'ai.agent.nodes.AskQuestionCard.title3':
    'Dies ist ein beispiel-produkttitel. Sie können hier die hauptmerkmale des produkts beschreiben',
  'ai.agent.nodes.AskQuestionCard.title4': '€0.00',
  'ai.agent.nodes.AskQuestionCard.title5': 'Zugestellt',
  'ai.agent.nodes.AskQuestionCard.title6': 'Karussell',
  'ai.agent.nodes.AskQuestionCard.title7': 'Liste',
  'ai.agent.nodes.AskQuestionCard.title8': 'Kartendaten',
  'ai.agent.nodes.AskQuestionCard.title9': 'Bild-URL',
  'ai.agent.nodes.AskQuestionCard.title10': 'Produkttitel',
  'ai.agent.nodes.AskQuestionCard.title11': 'Produktpreis (Optional)',
  'ai.agent.nodes.AskQuestionCard.title12': 'Produktmenge (Optional)',
  'ai.agent.nodes.AskQuestionCard.title13': 'Bestellstatus (Optional)',
  'ai.agent.nodes.AskQuestionCard.title14':
    'Hinweis: Dies ist eine JSON-Variable',
  'ai.agent.nodes.AskQuestionCard.title15': 'Format der JSON-Variable:',
  'ai.agent.nodes.AskQuestionCard.tip1':
    'Rufen Sie Daten über eine API ab, speichern Sie sie in einer JSON-Variable und verwenden Sie sie als Liste für die Kartendaten. Ordnen Sie die Eigenschaften jedes JSON-Objekts in der Liste den entsprechenden Feldern unten zu.',

  'ai.agent.nodes.ToolDelay.title1': 'Verzögerung um x sekunden',
  'ai.agent.nodes.header.save.error.formCancle':
    'Stellen Sie sicher, dass der Abbruchknoten des Formulars verbunden ist',

  'ai.agent.exit.confirm':
    'Sie haben ungespeicherte Änderungen. Sind Sie sicher, dass Sie fortfahren möchten?',
};
